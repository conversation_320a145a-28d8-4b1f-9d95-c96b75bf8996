{"ast": null, "code": "const {\n  default: axiosInstance\n} = require(\".\");\n\n// add report\nexport const addReport = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// get all reports\nexport const getAllReports = async filters => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// get all reports by user\nexport const getAllReportsByUser = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// get all reports for ranking\nexport const getAllReportsForRanking = async filters => {\n  try {\n    const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};", "map": {"version": 3, "names": ["default", "axiosInstance", "require", "addReport", "payload", "response", "post", "data", "error", "getAllReports", "filters", "getAllReportsByUser", "getAllReportsForRanking", "get"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/reports.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add report\r\nexport const addReport = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/add-report\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports\r\nexport const getAllReports = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports\", filters);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports by user\r\nexport const getAllReportsByUser = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/reports/get-all-reports-by-user\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all reports for ranking\r\nexport const getAllReportsForRanking = async (filters) => {\r\n    try {\r\n        const response = await axiosInstance.get(\"/api/reports/get-all-reports-for-ranking\");\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n} \r\n"], "mappings": "AAAA,MAAM;EAAEA,OAAO,EAAEC;AAAc,CAAC,GAAGC,OAAO,CAAC,GAAG,CAAC;;AAE/C;AACA,OAAO,MAAMC,SAAS,GAAG,MAAOC,OAAO,IAAK;EACxC,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,yBAAyB,EAAEF,OAAO,CAAC;IAC7E,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAME,aAAa,GAAG,MAAOC,OAAO,IAAK;EAC5C,IAAI;IACA,MAAML,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,8BAA8B,EAAEI,OAAO,CAAC;IAClF,OAAOL,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMI,mBAAmB,GAAG,MAAOP,OAAO,IAAK;EAClD,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,sCAAsC,EAAEF,OAAO,CAAC;IAC1F,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMK,uBAAuB,GAAG,MAAOF,OAAO,IAAK;EACtD,IAAI;IACA,MAAML,QAAQ,GAAG,MAAMJ,aAAa,CAACY,GAAG,CAAC,0CAA0C,CAAC;IACpF,OAAOR,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}