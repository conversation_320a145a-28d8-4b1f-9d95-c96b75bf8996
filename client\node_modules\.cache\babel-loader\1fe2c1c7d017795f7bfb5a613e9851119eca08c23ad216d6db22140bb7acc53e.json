{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizResult.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport { TbArrowLeft, TbCheck, TbX, TbTrophy, TbBrain, TbTarget, TbRefresh, TbEye, TbBulb } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport ContentRenderer from '../../../components/ContentRenderer';\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst QuizResult = () => {\n  _s();\n  var _location$state, _result$correctAnswer2, _result$wrongAnswers, _result$correctAnswer3, _result$correctAnswer4, _result$correctAnswer5;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [showReview, setShowReview] = useState(false);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const result = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.result;\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({\n          examId: id\n        });\n        dispatch(HideLoading());\n        if (response.success) {\n          var _response$data;\n          setExamData(response.data);\n          setQuestions(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || []);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n  useEffect(() => {\n    if (result) {\n      // Sound playing functionality can be added here if needed\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n    }\n  }, [result]);\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  if (!result || !examData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading results...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this);\n  }\n  if (showReview) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 py-4 sm:py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 rounded-2xl p-6 sm:p-8 shadow-2xl border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4\",\n              children: /*#__PURE__*/_jsxDEV(TbEye, {\n                className: \"w-8 h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl sm:text-3xl font-black text-white mb-3 drop-shadow-lg\",\n              children: \"Review Your Answers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white/90 text-base sm:text-lg font-medium drop-shadow-md\",\n              children: \"Detailed breakdown of your quiz performance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4 sm:space-y-6 mb-6 sm:mb-8\",\n          children: questions.map((question, index) => {\n            var _result$correctAnswer, _result$wrongAnswers$, _question$options, _question$options2;\n            const userAnswer = ((_result$correctAnswer = result.correctAnswers.find(q => q._id === question._id)) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.userAnswer) || ((_result$wrongAnswers$ = result.wrongAnswers.find(q => q._id === question._id)) === null || _result$wrongAnswers$ === void 0 ? void 0 : _result$wrongAnswers$.userAnswer) || \"\";\n            const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-4 sm:px-6 py-4 border-b-2 ${isCorrect ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300' : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg sm:text-xl font-bold text-gray-900 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex items-center justify-center w-8 h-8 rounded-full mr-3 ${isCorrect ? 'bg-green-100' : 'bg-red-100'}`,\n                      children: isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-5 h-5 text-green-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 149,\n                        columnNumber: 29\n                      }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                        className: \"w-5 h-5 text-red-600\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 151,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 145,\n                      columnNumber: 25\n                    }, this), \"Question \", index + 1]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold shadow-lg border-2 ${isCorrect ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white border-green-300' : 'bg-gradient-to-r from-red-500 to-pink-500 text-white border-red-300'}`,\n                    children: isCorrect ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 163,\n                        columnNumber: 29\n                      }, this), \"Correct\"]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(TbX, {\n                        className: \"w-4 h-4 mr-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 168,\n                        columnNumber: 29\n                      }, this), \"Incorrect\"]\n                    }, void 0, true)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 sm:p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4\",\n                  children: question.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3 sm:mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: question.image || question.imageUrl,\n                    alt: \"Question\",\n                    className: \"max-w-full h-auto rounded-lg border border-gray-200 mx-auto\",\n                    className: \"max-w-full h-auto rounded-lg border border-gray-200 mx-auto max-h-[300px]\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-4 sm:p-6 rounded-2xl border-2 shadow-lg ${isCorrect ? 'border-green-300 bg-gradient-to-br from-green-50 to-emerald-50' : 'border-red-300 bg-gradient-to-br from-red-50 to-pink-50'}`,\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-3\",\n                      children: [isCorrect ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-5 h-5 text-green-600 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 29\n                      }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                        className: \"w-5 h-5 text-red-600 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-800 text-base\",\n                        children: \"Your Answer\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 204,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: `text-base font-semibold px-3 py-2 rounded-lg ${isCorrect ? 'text-green-800 bg-green-100 border border-green-200' : 'text-red-800 bg-red-100 border border-red-200'}`,\n                      children: question.type === \"mcq\" || question.answerType === \"Options\" ? ((_question$options = question.options) === null || _question$options === void 0 ? void 0 : _question$options[userAnswer]) || \"Not answered\" : userAnswer || \"Not answered\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 206,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4 sm:p-6 rounded-2xl border-2 border-green-300 bg-gradient-to-br from-green-50 to-emerald-50 shadow-lg\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-5 h-5 text-green-600 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 219,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                        className: \"font-bold text-gray-800 text-base\",\n                        children: \"Correct Answer\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 220,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-green-800 text-base font-semibold bg-green-100 px-3 py-2 rounded-lg border border-green-200\",\n                      children: question.type === \"mcq\" || question.answerType === \"Options\" ? (_question$options2 = question.options) === null || _question$options2 === void 0 ? void 0 : _question$options2[question.correctOption || question.correctAnswer] : question.correctAnswer || question.correctOption\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 21\n                }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-6 p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 shadow-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"group flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-sm sm:text-base\",\n                    onClick: () => {\n                      var _question$options3, _question$options4;\n                      return fetchExplanation(question.name, question.type === \"mcq\" || question.answerType === \"Options\" ? (_question$options3 = question.options) === null || _question$options3 === void 0 ? void 0 : _question$options3[question.correctOption || question.correctAnswer] : question.correctAnswer || question.correctOption, question.type === \"mcq\" || question.answerType === \"Options\" ? ((_question$options4 = question.options) === null || _question$options4 === void 0 ? void 0 : _question$options4[userAnswer]) || \"Not answered\" : userAnswer || \"Not answered\", question.image || question.imageUrl);\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n                      className: \"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 27\n                    }, this), \"Get AI Explanation\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 25\n                  }, this), explanations[question.name] && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-4 p-4 sm:p-6 bg-white rounded-xl border-2 border-blue-200 shadow-md\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center mb-3\",\n                      children: [/*#__PURE__*/_jsxDEV(TbBulb, {\n                        className: \"w-5 h-5 text-blue-600 mr-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 253,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                        className: \"font-bold text-blue-800 text-base\",\n                        children: \"AI Explanation\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 254,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm sm:text-base text-gray-700 leading-relaxed\",\n                      children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                        text: explanations[question.name]\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 257,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center space-y-4 sm:space-y-0 sm:space-x-6 sm:flex sm:justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-gray-600 to-slate-600 text-white rounded-2xl font-bold hover:from-gray-700 hover:to-slate-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\",\n            onClick: () => setShowReview(false),\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), \"Back to Results\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group flex items-center justify-center w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\",\n            onClick: () => navigate('/user/hub'),\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), \"Hub\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4 sm:py-8\",\n    children: [result.verdict === \"Pass\" && /*#__PURE__*/_jsxDEV(Confetti, {\n      width: width,\n      height: height\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 37\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-4 left-4 z-10\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate(\"/quiz\"),\n        className: \"group flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-2xl hover:shadow-blue-500/25 border border-blue-400/20 relative overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n          className: \"w-4 h-4 group-hover:-translate-x-1 transition-transform duration-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm tracking-wide\",\n          children: \"Take Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `px-4 sm:px-6 py-8 sm:py-12 text-center relative overflow-hidden ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-green-50 via-emerald-50 to-green-100\" : \"bg-gradient-to-br from-red-50 via-pink-50 to-red-100\"}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute -top-20 -right-20 w-40 h-40 rounded-full blur-3xl opacity-20 ${result.verdict === \"Pass\" ? \"bg-green-400\" : \"bg-red-400\"}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `absolute -bottom-20 -left-20 w-40 h-40 rounded-full blur-3xl opacity-20 ${result.verdict === \"Pass\" ? \"bg-emerald-400\" : \"bg-pink-400\"}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative z-10\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 rounded-full mb-6 shadow-2xl ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-green-400 to-emerald-500\" : \"bg-gradient-to-br from-red-400 to-pink-500\"}`,\n              children: result.verdict === \"Pass\" ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-12 h-12 sm:w-16 sm:h-16 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(TbX, {\n                className: \"w-12 h-12 sm:w-16 sm:h-16 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: `text-3xl sm:text-4xl md:text-5xl font-black mb-4 ${result.verdict === \"Pass\" ? \"text-green-800\" : \"text-red-800\"}`,\n              children: result.verdict === \"Pass\" ? \"🎉 Congratulations!\" : \"💪 Better Luck Next Time!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-lg sm:text-xl font-semibold px-2 ${result.verdict === \"Pass\" ? \"text-green-700\" : \"text-red-700\"}`,\n              children: result.verdict === \"Pass\" ? \"You've successfully passed the quiz! Amazing work! 🌟\" : \"Keep practicing and try again! You've got this! 🚀\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-4 sm:p-6 md:p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 sm:p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border-2 border-green-200 shadow-lg hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-6 h-6 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl sm:text-4xl font-black text-green-700 mb-1\",\n                children: ((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-bold text-green-600\",\n                children: \"Correct Answers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 sm:p-6 bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 shadow-lg hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(TbX, {\n                  className: \"w-6 h-6 text-red-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl sm:text-4xl font-black text-red-700 mb-1\",\n                children: ((_result$wrongAnswers = result.wrongAnswers) === null || _result$wrongAnswers === void 0 ? void 0 : _result$wrongAnswers.length) || 0\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-bold text-red-600\",\n                children: \"Wrong Answers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                  className: \"w-6 h-6 text-blue-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl sm:text-4xl font-black text-blue-700 mb-1\",\n                children: questions.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-bold text-blue-600\",\n                children: \"Total Questions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4 sm:p-6 bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl border-2 border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-3\",\n                children: /*#__PURE__*/_jsxDEV(TbTarget, {\n                  className: \"w-6 h-6 text-purple-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 389,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl sm:text-4xl font-black text-purple-700 mb-1\",\n                children: [examData.passingMarks, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-bold text-purple-600\",\n                children: \"Pass Mark\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mb-4 shadow-lg\",\n                children: /*#__PURE__*/_jsxDEV(TbTarget, {\n                  className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl sm:text-3xl font-black text-gray-800 mb-2\",\n                children: \"Your Final Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 font-medium\",\n                children: \"See how well you performed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-300 rounded-full h-6 sm:h-8 overflow-hidden shadow-inner\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `h-full rounded-full transition-all duration-1000 ease-out relative ${result.verdict === \"Pass\" ? \"bg-gradient-to-r from-green-500 via-emerald-500 to-green-600\" : \"bg-gradient-to-r from-red-500 via-pink-500 to-red-600\"}`,\n                  style: {\n                    width: `${(((_result$correctAnswer3 = result.correctAnswers) === null || _result$correctAnswer3 === void 0 ? void 0 : _result$correctAnswer3.length) || 0) / questions.length * 100}%`\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-white bg-opacity-30 rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 410,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center mt-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-4xl sm:text-5xl font-black ${result.verdict === \"Pass\" ? \"text-green-700\" : \"text-red-700\"}`,\n                  children: [Math.round((((_result$correctAnswer4 = result.correctAnswers) === null || _result$correctAnswer4 === void 0 ? void 0 : _result$correctAnswer4.length) || 0) / questions.length * 100), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-600 font-semibold mt-2\",\n                  children: [((_result$correctAnswer5 = result.correctAnswers) === null || _result$correctAnswer5 === void 0 ? void 0 : _result$correctAnswer5.length) || 0, \" out of \", questions.length, \" questions correct\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), result.xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8\",\n            children: /*#__PURE__*/_jsxDEV(XPResultDisplay, {\n              xpData: result.xpData\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\",\n              onClick: () => setShowReview(true),\n              children: [/*#__PURE__*/_jsxDEV(TbEye, {\n                className: \"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this), \"Review Answers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\",\n              onClick: () => navigate(`/quiz/${id}/play`),\n              children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                className: \"w-5 h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this), \"Retake Quiz\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\",\n              onClick: () => navigate('/quiz'),\n              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this), \"Take Quiz Page\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 left-4 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/quiz'),\n        className: \"flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105\",\n        title: \"Back to Quiz Page\",\n        children: /*#__PURE__*/_jsxDEV(TbArrowLeft, {\n          className: \"w-6 h-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 292,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizResult, \"D4V/cGZGP63efKMIrBtSCOFOvoE=\", false, function () {\n  return [useParams, useNavigate, useLocation, useDispatch, useWindowSize];\n});\n_c = QuizResult;\nexport default QuizResult;\nvar _c;\n$RefreshReg$(_c, \"QuizResult\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useLocation", "useDispatch", "message", "Confetti", "useWindowSize", "TbArrowLeft", "TbCheck", "TbX", "TbTrophy", "TbBrain", "TbTarget", "TbRefresh", "TbEye", "TbBulb", "getExamById", "chatWithChatGPTToExplainAns", "HideLoading", "ShowLoading", "Content<PERSON><PERSON><PERSON>", "XPResultDisplay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "QuizResult", "_s", "_location$state", "_result$correctAnswer2", "_result$wrongAnswers", "_result$correctAnswer3", "_result$correctAnswer4", "_result$correctAnswer5", "examData", "setExamData", "questions", "setQuestions", "explanations", "setExplanations", "showReview", "setShowReview", "id", "navigate", "location", "dispatch", "width", "height", "result", "state", "fetchExamData", "response", "examId", "success", "_response$data", "data", "error", "console", "log", "verdict", "document", "body", "classList", "add", "remove", "fetchExplanation", "question", "expectedAnswer", "userAnswer", "imageUrl", "prev", "explanation", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "_result$correctAnswer", "_result$wrongAnswers$", "_question$options", "_question$options2", "correctAnswers", "find", "q", "_id", "wrongAnswers", "isCorrect", "some", "name", "image", "src", "alt", "type", "answerType", "options", "correctOption", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "_question$options3", "_question$options4", "text", "length", "passingMarks", "style", "Math", "round", "xpData", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizResult.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useDispatch } from 'react-redux';\nimport { message } from 'antd';\nimport Confetti from 'react-confetti';\nimport useWindowSize from 'react-use/lib/useWindowSize';\nimport {\n  TbArrowLeft,\n  TbCheck,\n  TbX,\n  TbTrophy,\n  TbBrain,\n  TbTarget,\n  TbRefresh,\n  TbEye,\n  TbBulb\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport ContentRenderer from '../../../components/ContentRenderer';\nimport XPResultDisplay from '../../../components/modern/XPResultDisplay';\nimport './responsive.css';\n\nconst QuizResult = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [explanations, setExplanations] = useState({});\n  const [showReview, setShowReview] = useState(false);\n  \n  const { id } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const { width, height } = useWindowSize();\n  \n  const result = location.state?.result;\n\n  useEffect(() => {\n    const fetchExamData = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getExamById({ examId: id });\n        dispatch(HideLoading());\n        \n        if (response.success) {\n          setExamData(response.data);\n          setQuestions(response.data?.questions || []);\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n        navigate('/user/quiz');\n      }\n    };\n\n    if (id) {\n      fetchExamData();\n    }\n  }, [id, dispatch, navigate]);\n\n  useEffect(() => {\n    if (result) {\n      // Sound playing functionality can be added here if needed\n      console.log(`Quiz ${result.verdict === \"Pass\" ? \"passed\" : \"failed\"}!`);\n    }\n  }, [result]);\n\n  // Add quiz-fullscreen class for fullscreen experience\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n\n  if (!result || !examData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading results...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (showReview) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 py-4 sm:py-8\">\n        <div className=\"max-w-4xl mx-auto px-4\">\n          {/* Header */}\n          <div className=\"text-center mb-8\">\n            <div className=\"bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 rounded-2xl p-6 sm:p-8 shadow-2xl border border-blue-200\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4\">\n                <TbEye className=\"w-8 h-8 text-white\" />\n              </div>\n              <h2 className=\"text-2xl sm:text-3xl font-black text-white mb-3 drop-shadow-lg\">\n                Review Your Answers\n              </h2>\n              <p className=\"text-white/90 text-base sm:text-lg font-medium drop-shadow-md\">\n                Detailed breakdown of your quiz performance\n              </p>\n            </div>\n          </div>\n\n          {/* Questions Review */}\n          <div className=\"space-y-4 sm:space-y-6 mb-6 sm:mb-8\">\n            {questions.map((question, index) => {\n              const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||\n                                result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || \"\";\n              const isCorrect = result.correctAnswers.some(q => q._id === question._id);\n\n              return (\n                <div key={index} className=\"bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden\">\n                  {/* Question Header */}\n                  <div className={`px-4 sm:px-6 py-4 border-b-2 ${\n                    isCorrect\n                      ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300'\n                      : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'\n                  }`}>\n                    <div className=\"flex items-center justify-between\">\n                      <h3 className=\"text-lg sm:text-xl font-bold text-gray-900 flex items-center\">\n                        <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full mr-3 ${\n                          isCorrect ? 'bg-green-100' : 'bg-red-100'\n                        }`}>\n                          {isCorrect ? (\n                            <TbCheck className=\"w-5 h-5 text-green-600\" />\n                          ) : (\n                            <TbX className=\"w-5 h-5 text-red-600\" />\n                          )}\n                        </span>\n                        Question {index + 1}\n                      </h3>\n                      <span className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold shadow-lg border-2 ${\n                        isCorrect\n                          ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white border-green-300'\n                          : 'bg-gradient-to-r from-red-500 to-pink-500 text-white border-red-300'\n                      }`}>\n                        {isCorrect ? (\n                          <>\n                            <TbCheck className=\"w-4 h-4 mr-1\" />\n                            Correct\n                          </>\n                        ) : (\n                          <>\n                            <TbX className=\"w-4 h-4 mr-1\" />\n                            Incorrect\n                          </>\n                        )}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Question Content */}\n                  <div className=\"p-4 sm:p-6\">\n                    <h4 className=\"text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4\">{question.name}</h4>\n\n                    {(question.image || question.imageUrl) && (\n                      <div className=\"mb-3 sm:mb-4\">\n                        <img\n                          src={question.image || question.imageUrl}\n                          alt=\"Question\"\n                          className=\"max-w-full h-auto rounded-lg border border-gray-200 mx-auto\"\n                          className=\"max-w-full h-auto rounded-lg border border-gray-200 mx-auto max-h-[300px]\"\n                        />\n                      </div>\n                    )}\n\n                    {/* Answer Comparison */}\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n                      <div className={`p-4 sm:p-6 rounded-2xl border-2 shadow-lg ${\n                        isCorrect\n                          ? 'border-green-300 bg-gradient-to-br from-green-50 to-emerald-50'\n                          : 'border-red-300 bg-gradient-to-br from-red-50 to-pink-50'\n                      }`}>\n                        <div className=\"flex items-center mb-3\">\n                          {isCorrect ? (\n                            <TbCheck className=\"w-5 h-5 text-green-600 mr-2\" />\n                          ) : (\n                            <TbX className=\"w-5 h-5 text-red-600 mr-2\" />\n                          )}\n                          <h5 className=\"font-bold text-gray-800 text-base\">Your Answer</h5>\n                        </div>\n                        <p className={`text-base font-semibold px-3 py-2 rounded-lg ${\n                          isCorrect\n                            ? 'text-green-800 bg-green-100 border border-green-200'\n                            : 'text-red-800 bg-red-100 border border-red-200'\n                        }`}>\n                          {question.type === \"mcq\" || question.answerType === \"Options\"\n                            ? question.options?.[userAnswer] || \"Not answered\"\n                            : userAnswer || \"Not answered\"}\n                        </p>\n                      </div>\n\n                      <div className=\"p-4 sm:p-6 rounded-2xl border-2 border-green-300 bg-gradient-to-br from-green-50 to-emerald-50 shadow-lg\">\n                        <div className=\"flex items-center mb-3\">\n                          <TbCheck className=\"w-5 h-5 text-green-600 mr-2\" />\n                          <h5 className=\"font-bold text-gray-800 text-base\">Correct Answer</h5>\n                        </div>\n                        <p className=\"text-green-800 text-base font-semibold bg-green-100 px-3 py-2 rounded-lg border border-green-200\">\n                          {question.type === \"mcq\" || question.answerType === \"Options\"\n                            ? question.options?.[question.correctOption || question.correctAnswer]\n                            : (question.correctAnswer || question.correctOption)}\n                        </p>\n                      </div>\n                    </div>\n\n                    {/* Explanation Section */}\n                    {!isCorrect && (\n                      <div className=\"mt-6 p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 shadow-lg\">\n                        <button\n                          className=\"group flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-sm sm:text-base\"\n                          onClick={() => fetchExplanation(\n                            question.name,\n                            question.type === \"mcq\" || question.answerType === \"Options\"\n                              ? question.options?.[question.correctOption || question.correctAnswer]\n                              : (question.correctAnswer || question.correctOption),\n                            question.type === \"mcq\" || question.answerType === \"Options\"\n                              ? question.options?.[userAnswer] || \"Not answered\"\n                              : userAnswer || \"Not answered\",\n                            question.image || question.imageUrl\n                          )}\n                        >\n                          <TbBulb className=\"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n                          Get AI Explanation\n                        </button>\n\n                        {explanations[question.name] && (\n                          <div className=\"mt-4 p-4 sm:p-6 bg-white rounded-xl border-2 border-blue-200 shadow-md\">\n                            <div className=\"flex items-center mb-3\">\n                              <TbBulb className=\"w-5 h-5 text-blue-600 mr-2\" />\n                              <h6 className=\"font-bold text-blue-800 text-base\">AI Explanation</h6>\n                            </div>\n                            <div className=\"text-sm sm:text-base text-gray-700 leading-relaxed\">\n                              <ContentRenderer text={explanations[question.name]} />\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n\n          {/* Back Buttons */}\n          <div className=\"text-center space-y-4 sm:space-y-0 sm:space-x-6 sm:flex sm:justify-center\">\n            <button\n              className=\"group flex items-center justify-center w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-gray-600 to-slate-600 text-white rounded-2xl font-bold hover:from-gray-700 hover:to-slate-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\"\n              onClick={() => setShowReview(false)}\n            >\n              <TbArrowLeft className=\"w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300\" />\n              Back to Results\n            </button>\n            <button\n              className=\"group flex items-center justify-center w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\"\n              onClick={() => navigate('/user/hub')}\n            >\n              <TbTrophy className=\"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n              Hub\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4 sm:py-8\">\n      {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\n\n      {/* Back to Quiz Tab - Top Left */}\n      <div className=\"absolute top-4 left-4 z-10\">\n        <button\n          onClick={() => navigate(\"/quiz\")}\n          className=\"group flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-2xl hover:shadow-blue-500/25 border border-blue-400/20 relative overflow-hidden\"\n        >\n          <TbArrowLeft className=\"w-4 h-4 group-hover:-translate-x-1 transition-transform duration-300\" />\n          <span className=\"text-sm tracking-wide\">Take Quiz</span>\n          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\"></div>\n        </button>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto px-4\">\n        <div className=\"bg-white rounded-2xl shadow-xl overflow-hidden\">\n          {/* Header */}\n          <div className={`px-4 sm:px-6 py-8 sm:py-12 text-center relative overflow-hidden ${\n            result.verdict === \"Pass\"\n              ? \"bg-gradient-to-br from-green-50 via-emerald-50 to-green-100\"\n              : \"bg-gradient-to-br from-red-50 via-pink-50 to-red-100\"\n          }`}>\n            {/* Background decoration */}\n            <div className=\"absolute inset-0 overflow-hidden\">\n              <div className={`absolute -top-20 -right-20 w-40 h-40 rounded-full blur-3xl opacity-20 ${\n                result.verdict === \"Pass\" ? \"bg-green-400\" : \"bg-red-400\"\n              }`}></div>\n              <div className={`absolute -bottom-20 -left-20 w-40 h-40 rounded-full blur-3xl opacity-20 ${\n                result.verdict === \"Pass\" ? \"bg-emerald-400\" : \"bg-pink-400\"\n              }`}></div>\n            </div>\n\n            <div className=\"relative z-10\">\n              <div className={`inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 rounded-full mb-6 shadow-2xl ${\n                result.verdict === \"Pass\"\n                  ? \"bg-gradient-to-br from-green-400 to-emerald-500\"\n                  : \"bg-gradient-to-br from-red-400 to-pink-500\"\n              }`}>\n                {result.verdict === \"Pass\" ? (\n                  <TbCheck className=\"w-12 h-12 sm:w-16 sm:h-16 text-white\" />\n                ) : (\n                  <TbX className=\"w-12 h-12 sm:w-16 sm:h-16 text-white\" />\n                )}\n              </div>\n\n              <h1 className={`text-3xl sm:text-4xl md:text-5xl font-black mb-4 ${\n                result.verdict === \"Pass\" ? \"text-green-800\" : \"text-red-800\"\n              }`}>\n                {result.verdict === \"Pass\" ? \"🎉 Congratulations!\" : \"💪 Better Luck Next Time!\"}\n              </h1>\n\n              <p className={`text-lg sm:text-xl font-semibold px-2 ${\n                result.verdict === \"Pass\" ? \"text-green-700\" : \"text-red-700\"\n              }`}>\n                {result.verdict === \"Pass\"\n                  ? \"You've successfully passed the quiz! Amazing work! 🌟\"\n                  : \"Keep practicing and try again! You've got this! 🚀\"}\n              </p>\n            </div>\n          </div>\n\n          {/* Statistics */}\n          <div className=\"p-4 sm:p-6 md:p-8\">\n            <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8\">\n              <div className=\"text-center p-4 sm:p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border-2 border-green-200 shadow-lg hover:shadow-xl transition-all duration-300\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3\">\n                  <TbCheck className=\"w-6 h-6 text-green-600\" />\n                </div>\n                <div className=\"text-3xl sm:text-4xl font-black text-green-700 mb-1\">\n                  {result.correctAnswers?.length || 0}\n                </div>\n                <div className=\"text-sm font-bold text-green-600\">Correct Answers</div>\n              </div>\n\n              <div className=\"text-center p-4 sm:p-6 bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 shadow-lg hover:shadow-xl transition-all duration-300\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mb-3\">\n                  <TbX className=\"w-6 h-6 text-red-600\" />\n                </div>\n                <div className=\"text-3xl sm:text-4xl font-black text-red-700 mb-1\">\n                  {result.wrongAnswers?.length || 0}\n                </div>\n                <div className=\"text-sm font-bold text-red-600\">Wrong Answers</div>\n              </div>\n\n              <div className=\"text-center p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3\">\n                  <TbBrain className=\"w-6 h-6 text-blue-600\" />\n                </div>\n                <div className=\"text-3xl sm:text-4xl font-black text-blue-700 mb-1\">\n                  {questions.length}\n                </div>\n                <div className=\"text-sm font-bold text-blue-600\">Total Questions</div>\n              </div>\n\n              <div className=\"text-center p-4 sm:p-6 bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl border-2 border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-3\">\n                  <TbTarget className=\"w-6 h-6 text-purple-600\" />\n                </div>\n                <div className=\"text-3xl sm:text-4xl font-black text-purple-700 mb-1\">\n                  {examData.passingMarks}%\n                </div>\n                <div className=\"text-sm font-bold text-purple-600\">Pass Mark</div>\n              </div>\n            </div>\n\n            {/* Score Percentage */}\n            <div className=\"mb-8\">\n              <div className=\"text-center mb-6\">\n                <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mb-4 shadow-lg\">\n                  <TbTarget className=\"w-8 h-8 text-white\" />\n                </div>\n                <h3 className=\"text-2xl sm:text-3xl font-black text-gray-800 mb-2\">Your Final Score</h3>\n                <p className=\"text-gray-600 font-medium\">See how well you performed</p>\n              </div>\n\n              <div className=\"relative\">\n                <div className=\"w-full bg-gray-300 rounded-full h-6 sm:h-8 overflow-hidden shadow-inner\">\n                  <div\n                    className={`h-full rounded-full transition-all duration-1000 ease-out relative ${\n                      result.verdict === \"Pass\"\n                        ? \"bg-gradient-to-r from-green-500 via-emerald-500 to-green-600\"\n                        : \"bg-gradient-to-r from-red-500 via-pink-500 to-red-600\"\n                    }`}\n                    style={{\n                      width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%`\n                    }}\n                  >\n                    <div className=\"absolute inset-0 bg-white bg-opacity-30 rounded-full\"></div>\n                  </div>\n                </div>\n\n                <div className=\"text-center mt-4\">\n                  <span className={`text-4xl sm:text-5xl font-black ${\n                    result.verdict === \"Pass\" ? \"text-green-700\" : \"text-red-700\"\n                  }`}>\n                    {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\n                  </span>\n                  <p className=\"text-gray-600 font-semibold mt-2\">\n                    {result.correctAnswers?.length || 0} out of {questions.length} questions correct\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* XP Display */}\n            {result.xpData && (\n              <div className=\"mb-8\">\n                <XPResultDisplay xpData={result.xpData} />\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button\n                className=\"group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\"\n                onClick={() => setShowReview(true)}\n              >\n                <TbEye className=\"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n                Review Answers\n              </button>\n\n              <button\n                className=\"group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\"\n                onClick={() => navigate(`/quiz/${id}/play`)}\n              >\n                <TbRefresh className=\"w-5 h-5 mr-2 group-hover:rotate-180 transition-transform duration-300\" />\n                Retake Quiz\n              </button>\n\n              <button\n                className=\"group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base\"\n                onClick={() => navigate('/quiz')}\n              >\n                <TbBrain className=\"w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300\" />\n                Take Quiz Page\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Back Navigation Arrow */}\n      <div className=\"fixed bottom-4 left-4 z-50\">\n        <button\n          onClick={() => navigate('/quiz')}\n          className=\"flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105\"\n          title=\"Back to Quiz Page\"\n        >\n          <TbArrowLeft className=\"w-6 h-6\" />\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizResult;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SACEC,WAAW,EACXC,OAAO,EACPC,GAAG,EACHC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,SAAS,EACTC,KAAK,EACLC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,2BAA2B,QAAQ,wBAAwB;AACpE,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAM;IAAE4C;EAAG,CAAC,GAAG1C,SAAS,CAAC,CAAC;EAC1B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM2C,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE2C,KAAK;IAAEC;EAAO,CAAC,GAAGzC,aAAa,CAAC,CAAC;EAEzC,MAAM0C,MAAM,IAAApB,eAAA,GAAGgB,QAAQ,CAACK,KAAK,cAAArB,eAAA,uBAAdA,eAAA,CAAgBoB,MAAM;EAErCjD,SAAS,CAAC,MAAM;IACd,MAAMmD,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFL,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;QACvB,MAAMgC,QAAQ,GAAG,MAAMnC,WAAW,CAAC;UAAEoC,MAAM,EAAEV;QAAG,CAAC,CAAC;QAClDG,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAIiC,QAAQ,CAACE,OAAO,EAAE;UAAA,IAAAC,cAAA;UACpBnB,WAAW,CAACgB,QAAQ,CAACI,IAAI,CAAC;UAC1BlB,YAAY,CAAC,EAAAiB,cAAA,GAAAH,QAAQ,CAACI,IAAI,cAAAD,cAAA,uBAAbA,cAAA,CAAelB,SAAS,KAAI,EAAE,CAAC;QAC9C,CAAC,MAAM;UACLhC,OAAO,CAACoD,KAAK,CAACL,QAAQ,CAAC/C,OAAO,CAAC;UAC/BuC,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOa,KAAK,EAAE;QACdX,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;QACvBd,OAAO,CAACoD,KAAK,CAACA,KAAK,CAACpD,OAAO,CAAC;QAC5BuC,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAED,IAAID,EAAE,EAAE;MACNQ,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAACR,EAAE,EAAEG,QAAQ,EAAEF,QAAQ,CAAC,CAAC;EAE5B5C,SAAS,CAAC,MAAM;IACd,IAAIiD,MAAM,EAAE;MACV;MACAS,OAAO,CAACC,GAAG,CAAE,QAAOV,MAAM,CAACW,OAAO,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAS,GAAE,CAAC;IACzE;EACF,CAAC,EAAE,CAACX,MAAM,CAAC,CAAC;;EAEZ;EACAjD,SAAS,CAAC,MAAM;IACd6D,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAE9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,cAAc,EAAEC,UAAU,EAAEC,QAAQ,KAAK;IACjF,IAAI;MACFxB,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMgC,QAAQ,GAAG,MAAMlC,2BAA2B,CAAC;QAAEiD,QAAQ;QAAEC,cAAc;QAAEC,UAAU;QAAEC;MAAS,CAAC,CAAC;MACtGxB,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIiC,QAAQ,CAACE,OAAO,EAAE;QACpBd,eAAe,CAAE+B,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAACJ,QAAQ,GAAGf,QAAQ,CAACoB;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACLnE,OAAO,CAACoD,KAAK,CAACL,QAAQ,CAACK,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdX,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvBd,OAAO,CAACoD,KAAK,CAACA,KAAK,CAACpD,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,IAAI,CAAC4C,MAAM,IAAI,CAACd,QAAQ,EAAE;IACxB,oBACEX,OAAA;MAAKiD,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGlD,OAAA;QAAKiD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlD,OAAA;UAAKiD,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC9FtD,OAAA;UAAGiD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIrC,UAAU,EAAE;IACd,oBACEjB,OAAA;MAAKiD,SAAS,EAAC,sCAAsC;MAAAC,QAAA,eACnDlD,OAAA;QAAKiD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErClD,OAAA;UAAKiD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BlD,OAAA;YAAKiD,SAAS,EAAC,sHAAsH;YAAAC,QAAA,gBACnIlD,OAAA;cAAKiD,SAAS,EAAC,iFAAiF;cAAAC,QAAA,eAC9FlD,OAAA,CAACT,KAAK;gBAAC0D,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACNtD,OAAA;cAAIiD,SAAS,EAAC,gEAAgE;cAAAC,QAAA,EAAC;YAE/E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtD,OAAA;cAAGiD,SAAS,EAAC,+DAA+D;cAAAC,QAAA,EAAC;YAE7E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtD,OAAA;UAAKiD,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjDrC,SAAS,CAAC0C,GAAG,CAAC,CAACZ,QAAQ,EAAEa,KAAK,KAAK;YAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,kBAAA;YAClC,MAAMf,UAAU,GAAG,EAAAY,qBAAA,GAAAhC,MAAM,CAACoC,cAAc,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKrB,QAAQ,CAACqB,GAAG,CAAC,cAAAP,qBAAA,uBAAvDA,qBAAA,CAAyDZ,UAAU,OAAAa,qBAAA,GACpEjC,MAAM,CAACwC,YAAY,CAACH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKrB,QAAQ,CAACqB,GAAG,CAAC,cAAAN,qBAAA,uBAArDA,qBAAA,CAAuDb,UAAU,KAAI,EAAE;YACzF,MAAMqB,SAAS,GAAGzC,MAAM,CAACoC,cAAc,CAACM,IAAI,CAACJ,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKrB,QAAQ,CAACqB,GAAG,CAAC;YAEzE,oBACEhE,OAAA;cAAiBiD,SAAS,EAAC,sEAAsE;cAAAC,QAAA,gBAE/FlD,OAAA;gBAAKiD,SAAS,EAAG,gCACfiB,SAAS,GACL,+DAA+D,GAC/D,wDACL,EAAE;gBAAAhB,QAAA,eACDlD,OAAA;kBAAKiD,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChDlD,OAAA;oBAAIiD,SAAS,EAAC,8DAA8D;oBAAAC,QAAA,gBAC1ElD,OAAA;sBAAMiD,SAAS,EAAG,qEAChBiB,SAAS,GAAG,cAAc,GAAG,YAC9B,EAAE;sBAAAhB,QAAA,EACAgB,SAAS,gBACRlE,OAAA,CAACf,OAAO;wBAACgE,SAAS,EAAC;sBAAwB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAE9CtD,OAAA,CAACd,GAAG;wBAAC+D,SAAS,EAAC;sBAAsB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBACxC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC,aACE,EAACE,KAAK,GAAG,CAAC;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,eACLtD,OAAA;oBAAMiD,SAAS,EAAG,sFAChBiB,SAAS,GACL,4EAA4E,GAC5E,qEACL,EAAE;oBAAAhB,QAAA,EACAgB,SAAS,gBACRlE,OAAA,CAAAE,SAAA;sBAAAgD,QAAA,gBACElD,OAAA,CAACf,OAAO;wBAACgE,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,WAEtC;oBAAA,eAAE,CAAC,gBAEHtD,OAAA,CAAAE,SAAA;sBAAAgD,QAAA,gBACElD,OAAA,CAACd,GAAG;wBAAC+D,SAAS,EAAC;sBAAc;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAElC;oBAAA,eAAE;kBACH;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNtD,OAAA;gBAAKiD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlD,OAAA;kBAAIiD,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EAAEP,QAAQ,CAACyB;gBAAI;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAE/F,CAACX,QAAQ,CAAC0B,KAAK,IAAI1B,QAAQ,CAACG,QAAQ,kBACnC9C,OAAA;kBAAKiD,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3BlD,OAAA;oBACEsE,GAAG,EAAE3B,QAAQ,CAAC0B,KAAK,IAAI1B,QAAQ,CAACG,QAAS;oBACzCyB,GAAG,EAAC,UAAU;oBACdtB,SAAS,EAAC,6DAA6D;oBACvEA,SAAS,EAAC;kBAA2E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,eAGDtD,OAAA;kBAAKiD,SAAS,EAAC,4CAA4C;kBAAAC,QAAA,gBACzDlD,OAAA;oBAAKiD,SAAS,EAAG,6CACfiB,SAAS,GACL,gEAAgE,GAChE,yDACL,EAAE;oBAAAhB,QAAA,gBACDlD,OAAA;sBAAKiD,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,GACpCgB,SAAS,gBACRlE,OAAA,CAACf,OAAO;wBAACgE,SAAS,EAAC;sBAA6B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAEnDtD,OAAA,CAACd,GAAG;wBAAC+D,SAAS,EAAC;sBAA2B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAC7C,eACDtD,OAAA;wBAAIiD,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAW;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC,eACNtD,OAAA;sBAAGiD,SAAS,EAAG,gDACbiB,SAAS,GACL,qDAAqD,GACrD,+CACL,EAAE;sBAAAhB,QAAA,EACAP,QAAQ,CAAC6B,IAAI,KAAK,KAAK,IAAI7B,QAAQ,CAAC8B,UAAU,KAAK,SAAS,GACzD,EAAAd,iBAAA,GAAAhB,QAAQ,CAAC+B,OAAO,cAAAf,iBAAA,uBAAhBA,iBAAA,CAAmBd,UAAU,CAAC,KAAI,cAAc,GAChDA,UAAU,IAAI;oBAAc;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eAENtD,OAAA;oBAAKiD,SAAS,EAAC,0GAA0G;oBAAAC,QAAA,gBACvHlD,OAAA;sBAAKiD,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrClD,OAAA,CAACf,OAAO;wBAACgE,SAAS,EAAC;sBAA6B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnDtD,OAAA;wBAAIiD,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC,eACNtD,OAAA;sBAAGiD,SAAS,EAAC,kGAAkG;sBAAAC,QAAA,EAC5GP,QAAQ,CAAC6B,IAAI,KAAK,KAAK,IAAI7B,QAAQ,CAAC8B,UAAU,KAAK,SAAS,IAAAb,kBAAA,GACzDjB,QAAQ,CAAC+B,OAAO,cAAAd,kBAAA,uBAAhBA,kBAAA,CAAmBjB,QAAQ,CAACgC,aAAa,IAAIhC,QAAQ,CAACiC,aAAa,CAAC,GACnEjC,QAAQ,CAACiC,aAAa,IAAIjC,QAAQ,CAACgC;oBAAc;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGL,CAACY,SAAS,iBACTlE,OAAA;kBAAKiD,SAAS,EAAC,4GAA4G;kBAAAC,QAAA,gBACzHlD,OAAA;oBACEiD,SAAS,EAAC,4PAA4P;oBACtQ4B,OAAO,EAAEA,CAAA;sBAAA,IAAAC,kBAAA,EAAAC,kBAAA;sBAAA,OAAMrC,gBAAgB,CAC7BC,QAAQ,CAACyB,IAAI,EACbzB,QAAQ,CAAC6B,IAAI,KAAK,KAAK,IAAI7B,QAAQ,CAAC8B,UAAU,KAAK,SAAS,IAAAK,kBAAA,GACxDnC,QAAQ,CAAC+B,OAAO,cAAAI,kBAAA,uBAAhBA,kBAAA,CAAmBnC,QAAQ,CAACgC,aAAa,IAAIhC,QAAQ,CAACiC,aAAa,CAAC,GACnEjC,QAAQ,CAACiC,aAAa,IAAIjC,QAAQ,CAACgC,aAAc,EACtDhC,QAAQ,CAAC6B,IAAI,KAAK,KAAK,IAAI7B,QAAQ,CAAC8B,UAAU,KAAK,SAAS,GACxD,EAAAM,kBAAA,GAAApC,QAAQ,CAAC+B,OAAO,cAAAK,kBAAA,uBAAhBA,kBAAA,CAAmBlC,UAAU,CAAC,KAAI,cAAc,GAChDA,UAAU,IAAI,cAAc,EAChCF,QAAQ,CAAC0B,KAAK,IAAI1B,QAAQ,CAACG,QAC7B,CAAC;oBAAA,CAAC;oBAAAI,QAAA,gBAEFlD,OAAA,CAACR,MAAM;sBAACyD,SAAS,EAAC;oBAAsE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,sBAE7F;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAERvC,YAAY,CAAC4B,QAAQ,CAACyB,IAAI,CAAC,iBAC1BpE,OAAA;oBAAKiD,SAAS,EAAC,wEAAwE;oBAAAC,QAAA,gBACrFlD,OAAA;sBAAKiD,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACrClD,OAAA,CAACR,MAAM;wBAACyD,SAAS,EAAC;sBAA4B;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjDtD,OAAA;wBAAIiD,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC,eACNtD,OAAA;sBAAKiD,SAAS,EAAC,oDAAoD;sBAAAC,QAAA,eACjElD,OAAA,CAACH,eAAe;wBAACmF,IAAI,EAAEjE,YAAY,CAAC4B,QAAQ,CAACyB,IAAI;sBAAE;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GA/HEE,KAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgIV,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtD,OAAA;UAAKiD,SAAS,EAAC,2EAA2E;UAAAC,QAAA,gBACxFlD,OAAA;YACEiD,SAAS,EAAC,iTAAiT;YAC3T4B,OAAO,EAAEA,CAAA,KAAM3D,aAAa,CAAC,KAAK,CAAE;YAAAgC,QAAA,gBAEpClD,OAAA,CAAChB,WAAW;cAACiE,SAAS,EAAC;YAA2E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mBAEvG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtD,OAAA;YACEiD,SAAS,EAAC,uTAAuT;YACjU4B,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAC,WAAW,CAAE;YAAA8B,QAAA,gBAErClD,OAAA,CAACb,QAAQ;cAAC8D,SAAS,EAAC;YAAsE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,OAE/F;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEtD,OAAA;IAAKiD,SAAS,EAAC,wEAAwE;IAAAC,QAAA,GACpFzB,MAAM,CAACW,OAAO,KAAK,MAAM,iBAAIpC,OAAA,CAAClB,QAAQ;MAACyC,KAAK,EAAEA,KAAM;MAACC,MAAM,EAAEA;IAAO;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGxEtD,OAAA;MAAKiD,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzClD,OAAA;QACE6E,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAC,OAAO,CAAE;QACjC6B,SAAS,EAAC,kVAAkV;QAAAC,QAAA,gBAE5VlD,OAAA,CAAChB,WAAW;UAACiE,SAAS,EAAC;QAAsE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChGtD,OAAA;UAAMiD,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDtD,OAAA;UAAKiD,SAAS,EAAC;QAA6I;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7J;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENtD,OAAA;MAAKiD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrClD,OAAA;QAAKiD,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAE7DlD,OAAA;UAAKiD,SAAS,EAAG,mEACfxB,MAAM,CAACW,OAAO,KAAK,MAAM,GACrB,6DAA6D,GAC7D,sDACL,EAAE;UAAAc,QAAA,gBAEDlD,OAAA;YAAKiD,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/ClD,OAAA;cAAKiD,SAAS,EAAG,yEACfxB,MAAM,CAACW,OAAO,KAAK,MAAM,GAAG,cAAc,GAAG,YAC9C;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACVtD,OAAA;cAAKiD,SAAS,EAAG,2EACfxB,MAAM,CAACW,OAAO,KAAK,MAAM,GAAG,gBAAgB,GAAG,aAChD;YAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAENtD,OAAA;YAAKiD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlD,OAAA;cAAKiD,SAAS,EAAG,kGACfxB,MAAM,CAACW,OAAO,KAAK,MAAM,GACrB,iDAAiD,GACjD,4CACL,EAAE;cAAAc,QAAA,EACAzB,MAAM,CAACW,OAAO,KAAK,MAAM,gBACxBpC,OAAA,CAACf,OAAO;gBAACgE,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAE5DtD,OAAA,CAACd,GAAG;gBAAC+D,SAAS,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACxD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENtD,OAAA;cAAIiD,SAAS,EAAG,oDACdxB,MAAM,CAACW,OAAO,KAAK,MAAM,GAAG,gBAAgB,GAAG,cAChD,EAAE;cAAAc,QAAA,EACAzB,MAAM,CAACW,OAAO,KAAK,MAAM,GAAG,qBAAqB,GAAG;YAA2B;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC,eAELtD,OAAA;cAAGiD,SAAS,EAAG,yCACbxB,MAAM,CAACW,OAAO,KAAK,MAAM,GAAG,gBAAgB,GAAG,cAChD,EAAE;cAAAc,QAAA,EACAzB,MAAM,CAACW,OAAO,KAAK,MAAM,GACtB,uDAAuD,GACvD;YAAoD;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtD,OAAA;UAAKiD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClD,OAAA;YAAKiD,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnFlD,OAAA;cAAKiD,SAAS,EAAC,kKAAkK;cAAAC,QAAA,gBAC/KlD,OAAA;gBAAKiD,SAAS,EAAC,kFAAkF;gBAAAC,QAAA,eAC/FlD,OAAA,CAACf,OAAO;kBAACgE,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,qDAAqD;gBAAAC,QAAA,EACjE,EAAA5C,sBAAA,GAAAmB,MAAM,CAACoC,cAAc,cAAAvD,sBAAA,uBAArBA,sBAAA,CAAuB2E,MAAM,KAAI;cAAC;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eAENtD,OAAA;cAAKiD,SAAS,EAAC,2JAA2J;cAAAC,QAAA,gBACxKlD,OAAA;gBAAKiD,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,eAC7FlD,OAAA,CAACd,GAAG;kBAAC+D,SAAS,EAAC;gBAAsB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC/D,EAAA3C,oBAAA,GAAAkB,MAAM,CAACwC,YAAY,cAAA1D,oBAAA,uBAAnBA,oBAAA,CAAqB0E,MAAM,KAAI;cAAC;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,eAENtD,OAAA;cAAKiD,SAAS,EAAC,+JAA+J;cAAAC,QAAA,gBAC5KlD,OAAA;gBAAKiD,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,eAC9FlD,OAAA,CAACZ,OAAO;kBAAC6D,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,EAChErC,SAAS,CAACoE;cAAM;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAENtD,OAAA;cAAKiD,SAAS,EAAC,mKAAmK;cAAAC,QAAA,gBAChLlD,OAAA;gBAAKiD,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAChGlD,OAAA,CAACX,QAAQ;kBAAC4D,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,GAClEvC,QAAQ,CAACuE,YAAY,EAAC,GACzB;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtD,OAAA;gBAAKiD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtD,OAAA;YAAKiD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBlD,OAAA;cAAKiD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BlD,OAAA;gBAAKiD,SAAS,EAAC,6HAA6H;gBAAAC,QAAA,eAC1IlD,OAAA,CAACX,QAAQ;kBAAC4D,SAAS,EAAC;gBAAoB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNtD,OAAA;gBAAIiD,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxFtD,OAAA;gBAAGiD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,eAENtD,OAAA;cAAKiD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBlD,OAAA;gBAAKiD,SAAS,EAAC,yEAAyE;gBAAAC,QAAA,eACtFlD,OAAA;kBACEiD,SAAS,EAAG,sEACVxB,MAAM,CAACW,OAAO,KAAK,MAAM,GACrB,8DAA8D,GAC9D,uDACL,EAAE;kBACH+C,KAAK,EAAE;oBACL5D,KAAK,EAAG,GAAG,CAAC,EAAAf,sBAAA,GAAAiB,MAAM,CAACoC,cAAc,cAAArD,sBAAA,uBAArBA,sBAAA,CAAuByE,MAAM,KAAI,CAAC,IAAIpE,SAAS,CAACoE,MAAM,GAAI,GAAI;kBAC5E,CAAE;kBAAA/B,QAAA,eAEFlD,OAAA;oBAAKiD,SAAS,EAAC;kBAAsD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtD,OAAA;gBAAKiD,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlD,OAAA;kBAAMiD,SAAS,EAAG,mCAChBxB,MAAM,CAACW,OAAO,KAAK,MAAM,GAAG,gBAAgB,GAAG,cAChD,EAAE;kBAAAc,QAAA,GACAkC,IAAI,CAACC,KAAK,CAAE,CAAC,EAAA5E,sBAAA,GAAAgB,MAAM,CAACoC,cAAc,cAAApD,sBAAA,uBAArBA,sBAAA,CAAuBwE,MAAM,KAAI,CAAC,IAAIpE,SAAS,CAACoE,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;gBAAA;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPtD,OAAA;kBAAGiD,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAC5C,EAAAxC,sBAAA,GAAAe,MAAM,CAACoC,cAAc,cAAAnD,sBAAA,uBAArBA,sBAAA,CAAuBuE,MAAM,KAAI,CAAC,EAAC,UAAQ,EAACpE,SAAS,CAACoE,MAAM,EAAC,oBAChE;gBAAA;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL7B,MAAM,CAAC6D,MAAM,iBACZtF,OAAA;YAAKiD,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBlD,OAAA,CAACF,eAAe;cAACwF,MAAM,EAAE7D,MAAM,CAAC6D;YAAO;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN,eAGDtD,OAAA;YAAKiD,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DlD,OAAA;cACEiD,SAAS,EAAC,kSAAkS;cAC5S4B,OAAO,EAAEA,CAAA,KAAM3D,aAAa,CAAC,IAAI,CAAE;cAAAgC,QAAA,gBAEnClD,OAAA,CAACT,KAAK;gBAAC0D,SAAS,EAAC;cAAsE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAE5F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETtD,OAAA;cACEiD,SAAS,EAAC,sSAAsS;cAChT4B,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAE,SAAQD,EAAG,OAAM,CAAE;cAAA+B,QAAA,gBAE5ClD,OAAA,CAACV,SAAS;gBAAC2D,SAAS,EAAC;cAAuE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEjG;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETtD,OAAA;cACEiD,SAAS,EAAC,sSAAsS;cAChT4B,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAC,OAAO,CAAE;cAAA8B,QAAA,gBAEjClD,OAAA,CAACZ,OAAO;gBAAC6D,SAAS,EAAC;cAAsE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,kBAE9F;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAKiD,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzClD,OAAA;QACE6E,OAAO,EAAEA,CAAA,KAAMzD,QAAQ,CAAC,OAAO,CAAE;QACjC6B,SAAS,EAAC,wJAAwJ;QAClKsC,KAAK,EAAC,mBAAmB;QAAArC,QAAA,eAEzBlD,OAAA,CAAChB,WAAW;UAACiE,SAAS,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CA7cID,UAAU;EAAA,QAMC1B,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW,EACFG,aAAa;AAAA;AAAAyG,EAAA,GAVnCrF,UAAU;AA+chB,eAAeA,UAAU;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}