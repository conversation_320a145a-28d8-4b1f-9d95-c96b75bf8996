{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingCard.js\";\nimport React from 'react';\nimport { TbTrophy, TbMedal, TbCrown, TbStar, TbFlame, TbBolt } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport XPProgressBar from './XPProgressBar';\nimport LevelBadge from './LevelBadge';\nimport EnhancedAchievementBadge from './EnhancedAchievementBadge';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingCard = ({\n  user,\n  rank,\n  classRank,\n  isCurrentUser = false,\n  layout = 'horizontal',\n  // 'horizontal' or 'vertical'\n  size = 'medium',\n  // 'small', 'medium', 'large'\n  showStats = true,\n  className = ''\n}) => {\n  // Size configurations - Optimized profile circle sizes for better visibility\n  const sizeConfig = {\n    small: {\n      avatar: 'w-12 h-12',\n      text: 'text-sm',\n      subtext: 'text-xs',\n      padding: 'p-3',\n      spacing: 'space-x-3'\n    },\n    medium: {\n      avatar: 'w-14 h-14',\n      text: 'text-base',\n      subtext: 'text-sm',\n      padding: 'p-4',\n      spacing: 'space-x-4'\n    },\n    large: {\n      avatar: 'w-16 h-16',\n      text: 'text-lg',\n      subtext: 'text-base',\n      padding: 'p-5',\n      spacing: 'space-x-5'\n    }\n  };\n  const config = sizeConfig[size];\n\n  // Get subscription status styling with improved status detection\n  const getSubscriptionStyling = () => {\n    const subscriptionStatus = (user === null || user === void 0 ? void 0 : user.subscriptionStatus) || (user === null || user === void 0 ? void 0 : user.normalizedSubscriptionStatus) || 'free';\n\n    // Normalize status for better handling\n    const normalizedStatus = subscriptionStatus.toLowerCase();\n    if (normalizedStatus === 'active' || normalizedStatus === 'premium') {\n      return {\n        avatarClass: 'ring-4 ring-yellow-400 ring-offset-2 ring-offset-white',\n        badge: 'status-premium',\n        glow: 'shadow-xl shadow-yellow-500/30 hover:shadow-yellow-500/40',\n        statusText: 'Premium',\n        badgeIcon: '👑',\n        borderClass: 'ring-2 ring-yellow-400',\n        bgClass: 'bg-gradient-to-r from-yellow-500 via-amber-500 to-orange-500 text-white shadow-lg border border-yellow-400/50',\n        cardBg: 'bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50',\n        textColor: 'text-yellow-700',\n        borderColor: 'border-yellow-200'\n      };\n    } else if (normalizedStatus === 'free') {\n      return {\n        avatarClass: 'ring-2 ring-blue-300 ring-offset-2 ring-offset-white',\n        badge: 'status-free',\n        glow: 'shadow-md shadow-blue-500/20 hover:shadow-blue-500/30',\n        statusText: 'Free',\n        badgeIcon: '🆓',\n        borderClass: 'ring-2 ring-blue-400',\n        bgClass: 'bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white shadow-md border border-blue-400/50',\n        cardBg: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50',\n        textColor: 'text-blue-700',\n        borderColor: 'border-blue-200'\n      };\n    } else {\n      return {\n        avatarClass: 'ring-2 ring-red-400 ring-offset-2 ring-offset-white opacity-75',\n        badge: 'status-expired',\n        glow: 'shadow-lg shadow-red-500/25 hover:shadow-red-500/35',\n        statusText: 'Expired',\n        badgeIcon: '⏰',\n        borderClass: 'ring-2 ring-red-400',\n        bgClass: 'bg-gradient-to-r from-red-500 via-red-600 to-red-700 text-white shadow-lg border border-red-400/50',\n        cardBg: 'bg-gradient-to-br from-red-50 via-pink-50 to-red-50',\n        textColor: 'text-red-700',\n        borderColor: 'border-red-200'\n      };\n    }\n  };\n  const styling = getSubscriptionStyling();\n\n  // Get rank icon and color\n  const getRankDisplay = () => {\n    const safeRank = rank || 0;\n    if (safeRank === 1) {\n      return {\n        icon: TbCrown,\n        color: 'text-yellow-500',\n        bg: 'bg-yellow-50'\n      };\n    } else if (safeRank === 2) {\n      return {\n        icon: TbMedal,\n        color: 'text-gray-400',\n        bg: 'bg-gray-50'\n      };\n    } else if (safeRank === 3) {\n      return {\n        icon: TbTrophy,\n        color: 'text-amber-600',\n        bg: 'bg-amber-50'\n      };\n    } else if (safeRank <= 10 && safeRank > 0) {\n      return {\n        icon: TbStar,\n        color: 'text-blue-500',\n        bg: 'bg-blue-50'\n      };\n    } else {\n      return {\n        icon: null,\n        color: 'text-gray-500',\n        bg: 'bg-gray-50'\n      };\n    }\n  };\n  const rankDisplay = getRankDisplay();\n  const RankIcon = rankDisplay.icon;\n\n  // Avatar wrapper with subscription styling (removed unused component)\n\n  if (layout === 'vertical') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    ${styling.cardBg || 'bg-white'} rounded-xl border ${styling.borderColor || 'border-gray-200'}\n                    ${styling.glow} transition-all duration-300 hover:scale-105 hover:shadow-xl\n                    transform hover:-translate-y-1 animate-fadeInUp\n                    ${isCurrentUser ? 'current-user-card ring-2 ring-blue-500 ring-offset-2' : ''}\n                    ${className}\n                `,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ${rankDisplay.bg} ${rankDisplay.color}\n                    `,\n          children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold\",\n            children: [\"#\", rank]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 21\n        }, this), classRank && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold\",\n            children: [\"C\", classRank]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3 hover:scale-105 transition-transform duration-200\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                        ${styling.avatarClass} ${styling.borderClass}\n                    `,\n          children: user !== null && user !== void 0 && user.profilePicture || user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: user.profilePicture || user.profileImage,\n            alt: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n            className: \"w-full h-full object-cover object-center\",\n            style: {\n              objectFit: 'cover'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-bold text-white\",\n              children: ((user === null || user === void 0 ? void 0 : user.name) || 'U').charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate max-w-24`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(LevelBadge, {\n            level: (user === null || user === void 0 ? void 0 : user.currentLevel) || 1,\n            size: \"small\",\n            showTitle: false,\n            animated: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: `${config.subtext} text-blue-600 font-medium`,\n              children: [(user === null || user === void 0 ? void 0 : user.totalXP) || 0, \" XP\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 29\n            }, this), (user === null || user === void 0 ? void 0 : user.xpToNextLevel) > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs text-gray-400`,\n              children: [user.xpToNextLevel, \" to next\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs text-gray-400`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" pts (legacy)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 21\n        }, this), (user === null || user === void 0 ? void 0 : user.averageScore) && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `${config.subtext} text-gray-500`,\n          children: [\"Avg: \", user.averageScore, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.currentStreak) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.achievements) && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [user.achievements.slice(0, 3).map((achievement, index) => /*#__PURE__*/_jsxDEV(EnhancedAchievementBadge, {\n            achievement: achievement,\n            size: \"small\",\n            showTooltip: true,\n            animated: true,\n            showXP: false\n          }, achievement.id || achievement.type || index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 33\n          }, this)), user.achievements.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500 ml-1\",\n            children: [\"+\", user.achievements.length - 3]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        inline-flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-semibold\n                        ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                        backdrop-blur-sm\n                    `,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: styling.badgeIcon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: styling.statusText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Horizontal layout (default)\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200 hover:scale-105 transition-all duration-300\n                hover:shadow-xl transform hover:-translate-y-1 animate-fadeInUp\n                ${isCurrentUser ? 'current-user-card ring-2 ring-blue-500 ring-offset-2' : ''}\n                ${className}\n            `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 flex-shrink-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `,\n        children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-bold\",\n          children: [\"#\", rank || '?']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 17\n      }, this), classRank && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-bold\",\n          children: [\"C\", classRank]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-shrink-0 hover:scale-105 transition-transform duration-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                    ${styling.avatarClass} ${styling.borderClass}\n                `,\n        children: user !== null && user !== void 0 && user.profilePicture || user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: user.profilePicture || user.profileImage,\n          alt: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n          className: \"w-full h-full object-cover object-center\",\n          style: {\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold text-white\",\n            children: ((user === null || user === void 0 ? void 0 : user.name) || 'U').charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(LevelBadge, {\n          level: (user === null || user === void 0 ? void 0 : user.currentLevel) || 1,\n          size: \"small\",\n          showTitle: false,\n          animated: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.bgClass}\n                    `,\n          children: styling.statusText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 17\n      }, this), showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbBolt, {\n            className: \"w-3 h-3 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-blue-600 font-medium`,\n            children: [(user === null || user === void 0 ? void 0 : user.totalXP) || 0, \" XP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-xs text-gray-400`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" pts\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.passedExamsCount) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-green-600`,\n          children: [user.passedExamsCount, \" passed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.quizzesTaken) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-blue-600`,\n          children: [user.quizzesTaken, \" quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.averageScore) && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-gray-600`,\n          children: [user.averageScore, \"% avg\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.currentStreak) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 21\n      }, this), (user === null || user === void 0 ? void 0 : user.xpToNextLevel) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(XPProgressBar, {\n          currentXP: user.totalXP || 0,\n          totalXP: (user.totalXP || 0) + (user.xpToNextLevel || 0),\n          currentLevel: user.currentLevel || 1,\n          xpToNextLevel: user.xpToNextLevel || 0,\n          size: \"small\",\n          showLevel: false,\n          showXPNumbers: false,\n          showAnimation: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 21\n      }, this), (user === null || user === void 0 ? void 0 : user.achievements) && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(AchievementList, {\n          achievements: user.achievements,\n          maxDisplay: 5,\n          size: \"small\",\n          layout: \"horizontal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-right flex-shrink-0 space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`,\n          children: (user.rankingScore || user.score || user.totalXP || user.totalPoints || 0).toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${config.subtext} text-gray-500`,\n          children: user.rankingScore ? 'ranking pts' : user.totalXP ? 'XP' : 'points'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 21\n        }, this), user.breakdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-400 mt-1\",\n          children: [\"XP: \", (user.totalXP || 0).toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-semibold flex-shrink-0\n                    ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                    backdrop-blur-sm\n                `,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs\",\n          children: styling.badgeIcon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: styling.statusText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 405,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 9\n  }, this);\n};\n_c = UserRankingCard;\nexport default UserRankingCard;\nvar _c;\n$RefreshReg$(_c, \"UserRankingCard\");", "map": {"version": 3, "names": ["React", "TbTrophy", "TbMedal", "TbCrown", "TbStar", "TbFlame", "TbBolt", "AchievementList", "XPProgressBar", "LevelBadge", "EnhancedAchievementBadge", "jsxDEV", "_jsxDEV", "UserRankingCard", "user", "rank", "classRank", "isCurrentUser", "layout", "size", "showStats", "className", "sizeConfig", "small", "avatar", "text", "subtext", "padding", "spacing", "medium", "large", "config", "getSubscriptionStyling", "subscriptionStatus", "normalizedSubscriptionStatus", "normalizedStatus", "toLowerCase", "avatarClass", "badge", "glow", "statusText", "badgeIcon", "borderClass", "bgClass", "cardBg", "textColor", "borderColor", "styling", "getRankDisplay", "safeRank", "icon", "color", "bg", "rankDisplay", "RankIcon", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "profilePicture", "profileImage", "src", "alt", "name", "style", "objectFit", "char<PERSON>t", "toUpperCase", "level", "currentLevel", "showTitle", "animated", "totalXP", "xpToNextLevel", "totalPoints", "averageScore", "currentStreak", "achievements", "length", "slice", "map", "achievement", "index", "showTooltip", "showXP", "id", "type", "passedExamsCount", "undefined", "quizzesTaken", "currentXP", "showLevel", "showXPNumbers", "showAnimation", "maxDisplay", "rankingScore", "score", "toLocaleString", "breakdown", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingCard.js"], "sourcesContent": ["import React from 'react';\nimport { Tb<PERSON>rophy, TbMedal, TbCrown, TbStar, TbFlame, TbBolt } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport XPProgressBar from './XPProgressBar';\nimport LevelBadge from './LevelBadge';\nimport EnhancedAchievementBadge from './EnhancedAchievementBadge';\n\nconst UserRankingCard = ({\n    user,\n    rank,\n    classRank,\n    isCurrentUser = false,\n    layout = 'horizontal', // 'horizontal' or 'vertical'\n    size = 'medium', // 'small', 'medium', 'large'\n    showStats = true,\n    className = ''\n}) => {\n    // Size configurations - Optimized profile circle sizes for better visibility\n    const sizeConfig = {\n        small: {\n            avatar: 'w-12 h-12',\n            text: 'text-sm',\n            subtext: 'text-xs',\n            padding: 'p-3',\n            spacing: 'space-x-3'\n        },\n        medium: {\n            avatar: 'w-14 h-14',\n            text: 'text-base',\n            subtext: 'text-sm',\n            padding: 'p-4',\n            spacing: 'space-x-4'\n        },\n        large: {\n            avatar: 'w-16 h-16',\n            text: 'text-lg',\n            subtext: 'text-base',\n            padding: 'p-5',\n            spacing: 'space-x-5'\n        }\n    };\n\n    const config = sizeConfig[size];\n\n    // Get subscription status styling with improved status detection\n    const getSubscriptionStyling = () => {\n        const subscriptionStatus = user?.subscriptionStatus || user?.normalizedSubscriptionStatus || 'free';\n\n        // Normalize status for better handling\n        const normalizedStatus = subscriptionStatus.toLowerCase();\n\n        if (normalizedStatus === 'active' || normalizedStatus === 'premium') {\n            return {\n                avatarClass: 'ring-4 ring-yellow-400 ring-offset-2 ring-offset-white',\n                badge: 'status-premium',\n                glow: 'shadow-xl shadow-yellow-500/30 hover:shadow-yellow-500/40',\n                statusText: 'Premium',\n                badgeIcon: '👑',\n                borderClass: 'ring-2 ring-yellow-400',\n                bgClass: 'bg-gradient-to-r from-yellow-500 via-amber-500 to-orange-500 text-white shadow-lg border border-yellow-400/50',\n                cardBg: 'bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50',\n                textColor: 'text-yellow-700',\n                borderColor: 'border-yellow-200'\n            };\n        } else if (normalizedStatus === 'free') {\n            return {\n                avatarClass: 'ring-2 ring-blue-300 ring-offset-2 ring-offset-white',\n                badge: 'status-free',\n                glow: 'shadow-md shadow-blue-500/20 hover:shadow-blue-500/30',\n                statusText: 'Free',\n                badgeIcon: '🆓',\n                borderClass: 'ring-2 ring-blue-400',\n                bgClass: 'bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white shadow-md border border-blue-400/50',\n                cardBg: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50',\n                textColor: 'text-blue-700',\n                borderColor: 'border-blue-200'\n            };\n        } else {\n            return {\n                avatarClass: 'ring-2 ring-red-400 ring-offset-2 ring-offset-white opacity-75',\n                badge: 'status-expired',\n                glow: 'shadow-lg shadow-red-500/25 hover:shadow-red-500/35',\n                statusText: 'Expired',\n                badgeIcon: '⏰',\n                borderClass: 'ring-2 ring-red-400',\n                bgClass: 'bg-gradient-to-r from-red-500 via-red-600 to-red-700 text-white shadow-lg border border-red-400/50',\n                cardBg: 'bg-gradient-to-br from-red-50 via-pink-50 to-red-50',\n                textColor: 'text-red-700',\n                borderColor: 'border-red-200'\n            };\n        }\n    };\n\n    const styling = getSubscriptionStyling();\n\n    // Get rank icon and color\n    const getRankDisplay = () => {\n        const safeRank = rank || 0;\n        if (safeRank === 1) {\n            return { icon: TbCrown, color: 'text-yellow-500', bg: 'bg-yellow-50' };\n        } else if (safeRank === 2) {\n            return { icon: TbMedal, color: 'text-gray-400', bg: 'bg-gray-50' };\n        } else if (safeRank === 3) {\n            return { icon: TbTrophy, color: 'text-amber-600', bg: 'bg-amber-50' };\n        } else if (safeRank <= 10 && safeRank > 0) {\n            return { icon: TbStar, color: 'text-blue-500', bg: 'bg-blue-50' };\n        } else {\n            return { icon: null, color: 'text-gray-500', bg: 'bg-gray-50' };\n        }\n    };\n\n    const rankDisplay = getRankDisplay();\n    const RankIcon = rankDisplay.icon;\n\n\n\n    // Avatar wrapper with subscription styling (removed unused component)\n\n    if (layout === 'vertical') {\n        return (\n            <div\n                className={`\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    ${styling.cardBg || 'bg-white'} rounded-xl border ${styling.borderColor || 'border-gray-200'}\n                    ${styling.glow} transition-all duration-300 hover:scale-105 hover:shadow-xl\n                    transform hover:-translate-y-1 animate-fadeInUp\n                    ${isCurrentUser ? 'current-user-card ring-2 ring-blue-500 ring-offset-2' : ''}\n                    ${className}\n                `}\n            >\n                {/* Rank Badges */}\n                <div className=\"flex items-center space-x-2 mb-3\">\n                    {/* Overall Rank */}\n                    <div className={`\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ${rankDisplay.bg} ${rankDisplay.color}\n                    `}>\n                        {RankIcon ? (\n                            <RankIcon className=\"w-4 h-4\" />\n                        ) : (\n                            <span className=\"text-xs font-bold\">#{rank}</span>\n                        )}\n                    </div>\n\n                    {/* Class Rank */}\n                    {classRank && (\n                        <div className=\"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700\">\n                            <span className=\"text-xs font-bold\">C{classRank}</span>\n                        </div>\n                    )}\n                </div>\n\n                {/* Avatar - Instagram Style Small Circle */}\n                <div className=\"mb-3 hover:scale-105 transition-transform duration-200\">\n                    <div className={`\n                        ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                        ${styling.avatarClass} ${styling.borderClass}\n                    `}>\n                        {user?.profilePicture || user?.profileImage ? (\n                            <img\n                                src={user.profilePicture || user.profileImage}\n                                alt={user?.name || 'User'}\n                                className=\"w-full h-full object-cover object-center\"\n                                style={{ objectFit: 'cover' }}\n                            />\n                        ) : (\n                            <div className=\"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\">\n                                <span className=\"text-xs font-bold text-white\">\n                                    {(user?.name || 'U').charAt(0).toUpperCase()}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                </div>\n\n                {/* User Info */}\n                <div className=\"space-y-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate max-w-24`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n\n                    {/* XP and Level Info */}\n                    <div className=\"flex items-center space-x-2\">\n                        <LevelBadge\n                            level={user?.currentLevel || 1}\n                            size=\"small\"\n                            showTitle={false}\n                            animated={true}\n                        />\n                        <div className=\"flex flex-col\">\n                            <p className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </p>\n                            {user?.xpToNextLevel > 0 && (\n                                <p className={`text-xs text-gray-400`}>\n                                    {user.xpToNextLevel} to next\n                                </p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Legacy Points (smaller) */}\n                    <p className={`text-xs text-gray-400`}>\n                        {user?.totalPoints || 0} pts (legacy)\n                    </p>\n\n                    {/* Enhanced Stats */}\n                    {user?.averageScore && (\n                        <p className={`${config.subtext} text-gray-500`}>\n                            Avg: {user.averageScore}%\n                        </p>\n                    )}\n\n                    {user?.currentStreak > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                            <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                {user.currentStreak}\n                            </span>\n                        </div>\n                    )}\n\n                    {/* Enhanced Achievements */}\n                    {user?.achievements && user.achievements.length > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            {user.achievements.slice(0, 3).map((achievement, index) => (\n                                <EnhancedAchievementBadge\n                                    key={achievement.id || achievement.type || index}\n                                    achievement={achievement}\n                                    size=\"small\"\n                                    showTooltip={true}\n                                    animated={true}\n                                    showXP={false}\n                                />\n                            ))}\n                            {user.achievements.length > 3 && (\n                                <span className=\"text-xs text-gray-500 ml-1\">\n                                    +{user.achievements.length - 3}\n                                </span>\n                            )}\n                        </div>\n                    )}\n\n                    {/* Modern Subscription Badge */}\n                    <div className={`\n                        inline-flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-semibold\n                        ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                        backdrop-blur-sm\n                    `}>\n                        <span className=\"text-sm\">{styling.badgeIcon}</span>\n                        <span>{styling.statusText}</span>\n                    </div>\n                </div>\n            </div>\n        );\n    }\n\n    // Horizontal layout (default)\n    return (\n        <div\n            className={`\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200 hover:scale-105 transition-all duration-300\n                hover:shadow-xl transform hover:-translate-y-1 animate-fadeInUp\n                ${isCurrentUser ? 'current-user-card ring-2 ring-blue-500 ring-offset-2' : ''}\n                ${className}\n            `}\n        >\n            {/* Rank Badges */}\n            <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                {/* Overall Rank */}\n                <div className={`\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `}>\n                    {RankIcon ? (\n                        <RankIcon className=\"w-5 h-5\" />\n                    ) : (\n                        <span className=\"text-sm font-bold\">#{rank || '?'}</span>\n                    )}\n                </div>\n\n                {/* Class Rank */}\n                {classRank && (\n                    <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700\">\n                        <span className=\"text-sm font-bold\">C{classRank}</span>\n                    </div>\n                )}\n            </div>\n\n            {/* Avatar - Instagram Style Small Circle */}\n            <div className=\"flex-shrink-0 hover:scale-105 transition-transform duration-200\">\n                <div className={`\n                    ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                    ${styling.avatarClass} ${styling.borderClass}\n                `}>\n                    {user?.profilePicture || user?.profileImage ? (\n                        <img\n                            src={user.profilePicture || user.profileImage}\n                            alt={user?.name || 'User'}\n                            className=\"w-full h-full object-cover object-center\"\n                            style={{ objectFit: 'cover' }}\n                        />\n                    ) : (\n                        <div className=\"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\">\n                            <span className=\"text-xs font-bold text-white\">\n                                {(user?.name || 'U').charAt(0).toUpperCase()}\n                            </span>\n                        </div>\n                    )}\n                </div>\n            </div>\n\n            {/* User Info */}\n            <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2 mb-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n                    <LevelBadge\n                        level={user?.currentLevel || 1}\n                        size=\"small\"\n                        showTitle={false}\n                        animated={true}\n                    />\n                    <span className={`\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.bgClass}\n                    `}>\n                        {styling.statusText}\n                    </span>\n                </div>\n                \n                {showStats && (\n                    <div className=\"flex items-center space-x-4\">\n                        {/* XP Display */}\n                        <div className=\"flex items-center space-x-1\">\n                            <TbBolt className=\"w-3 h-3 text-blue-500\" />\n                            <span className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </span>\n                        </div>\n\n                        {/* Legacy Points (smaller) */}\n                        <span className={`text-xs text-gray-400`}>\n                            {user?.totalPoints || 0} pts\n                        </span>\n\n                        {user?.passedExamsCount !== undefined && (\n                            <span className={`${config.subtext} text-green-600`}>\n                                {user.passedExamsCount} passed\n                            </span>\n                        )}\n                        {user?.quizzesTaken !== undefined && (\n                            <span className={`${config.subtext} text-blue-600`}>\n                                {user.quizzesTaken} quizzes\n                            </span>\n                        )}\n                        {user?.averageScore && (\n                            <span className={`${config.subtext} text-gray-600`}>\n                                {user.averageScore}% avg\n                            </span>\n                        )}\n                        {user?.currentStreak > 0 && (\n                            <div className=\"flex items-center space-x-1\">\n                                <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                                <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                    {user.currentStreak}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                )}\n\n                {/* XP Progress Bar */}\n                {user?.xpToNextLevel > 0 && (\n                    <div className=\"mt-2\">\n                        <XPProgressBar\n                            currentXP={user.totalXP || 0}\n                            totalXP={(user.totalXP || 0) + (user.xpToNextLevel || 0)}\n                            currentLevel={user.currentLevel || 1}\n                            xpToNextLevel={user.xpToNextLevel || 0}\n                            size=\"small\"\n                            showLevel={false}\n                            showXPNumbers={false}\n                            showAnimation={false}\n                        />\n                    </div>\n                )}\n\n                {/* Achievements for horizontal layout */}\n                {user?.achievements && user.achievements.length > 0 && (\n                    <div className=\"mt-2\">\n                        <AchievementList\n                            achievements={user.achievements}\n                            maxDisplay={5}\n                            size=\"small\"\n                            layout=\"horizontal\"\n                        />\n                    </div>\n                )}\n            </div>\n\n            {/* Score and Subscription Badge */}\n            <div className=\"text-right flex-shrink-0 space-y-2\">\n                <div>\n                    <div className={`font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`}>\n                        {(user.rankingScore || user.score || user.totalXP || user.totalPoints || 0).toLocaleString()}\n                    </div>\n                    <div className={`${config.subtext} text-gray-500`}>\n                        {user.rankingScore ? 'ranking pts' : user.totalXP ? 'XP' : 'points'}\n                    </div>\n                    {user.breakdown && (\n                        <div className=\"text-xs text-gray-400 mt-1\">\n                            XP: {(user.totalXP || 0).toLocaleString()}\n                        </div>\n                    )}\n                </div>\n\n                {/* Modern Subscription Badge for Horizontal Layout */}\n                <div className={`\n                    inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-semibold flex-shrink-0\n                    ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                    backdrop-blur-sm\n                `}>\n                    <span className=\"text-xs\">{styling.badgeIcon}</span>\n                    <span>{styling.statusText}</span>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default UserRankingCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACpF,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,wBAAwB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,eAAe,GAAGA,CAAC;EACrBC,IAAI;EACJC,IAAI;EACJC,SAAS;EACTC,aAAa,GAAG,KAAK;EACrBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EAAE;EACjBC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG;AAChB,CAAC,KAAK;EACF;EACA,MAAMC,UAAU,GAAG;IACfC,KAAK,EAAE;MACHC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACJL,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDE,KAAK,EAAE;MACHN,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb;EACJ,CAAC;EAED,MAAMG,MAAM,GAAGT,UAAU,CAACH,IAAI,CAAC;;EAE/B;EACA,MAAMa,sBAAsB,GAAGA,CAAA,KAAM;IACjC,MAAMC,kBAAkB,GAAG,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,MAAInB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,4BAA4B,KAAI,MAAM;;IAEnG;IACA,MAAMC,gBAAgB,GAAGF,kBAAkB,CAACG,WAAW,CAAC,CAAC;IAEzD,IAAID,gBAAgB,KAAK,QAAQ,IAAIA,gBAAgB,KAAK,SAAS,EAAE;MACjE,OAAO;QACHE,WAAW,EAAE,wDAAwD;QACrEC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,2DAA2D;QACjEC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,wBAAwB;QACrCC,OAAO,EAAE,+GAA+G;QACxHC,MAAM,EAAE,4DAA4D;QACpEC,SAAS,EAAE,iBAAiB;QAC5BC,WAAW,EAAE;MACjB,CAAC;IACL,CAAC,MAAM,IAAIX,gBAAgB,KAAK,MAAM,EAAE;MACpC,OAAO;QACHE,WAAW,EAAE,sDAAsD;QACnEC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,uDAAuD;QAC7DC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,sBAAsB;QACnCC,OAAO,EAAE,0GAA0G;QACnHC,MAAM,EAAE,yDAAyD;QACjEC,SAAS,EAAE,eAAe;QAC1BC,WAAW,EAAE;MACjB,CAAC;IACL,CAAC,MAAM;MACH,OAAO;QACHT,WAAW,EAAE,gEAAgE;QAC7EC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,qDAAqD;QAC3DC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,GAAG;QACdC,WAAW,EAAE,qBAAqB;QAClCC,OAAO,EAAE,oGAAoG;QAC7GC,MAAM,EAAE,qDAAqD;QAC7DC,SAAS,EAAE,cAAc;QACzBC,WAAW,EAAE;MACjB,CAAC;IACL;EACJ,CAAC;EAED,MAAMC,OAAO,GAAGf,sBAAsB,CAAC,CAAC;;EAExC;EACA,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,QAAQ,GAAGlC,IAAI,IAAI,CAAC;IAC1B,IAAIkC,QAAQ,KAAK,CAAC,EAAE;MAChB,OAAO;QAAEC,IAAI,EAAE/C,OAAO;QAAEgD,KAAK,EAAE,iBAAiB;QAAEC,EAAE,EAAE;MAAe,CAAC;IAC1E,CAAC,MAAM,IAAIH,QAAQ,KAAK,CAAC,EAAE;MACvB,OAAO;QAAEC,IAAI,EAAEhD,OAAO;QAAEiD,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACtE,CAAC,MAAM,IAAIH,QAAQ,KAAK,CAAC,EAAE;MACvB,OAAO;QAAEC,IAAI,EAAEjD,QAAQ;QAAEkD,KAAK,EAAE,gBAAgB;QAAEC,EAAE,EAAE;MAAc,CAAC;IACzE,CAAC,MAAM,IAAIH,QAAQ,IAAI,EAAE,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACvC,OAAO;QAAEC,IAAI,EAAE9C,MAAM;QAAE+C,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACrE,CAAC,MAAM;MACH,OAAO;QAAEF,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACnE;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGL,cAAc,CAAC,CAAC;EACpC,MAAMM,QAAQ,GAAGD,WAAW,CAACH,IAAI;;EAIjC;;EAEA,IAAIhC,MAAM,KAAK,UAAU,EAAE;IACvB,oBACIN,OAAA;MACIS,SAAS,EAAG;AAC5B,0EAA0EU,MAAM,CAACJ,OAAQ;AACzF,sBAAsBoB,OAAO,CAACH,MAAM,IAAI,UAAW,sBAAqBG,OAAO,CAACD,WAAW,IAAI,iBAAkB;AACjH,sBAAsBC,OAAO,CAACR,IAAK;AACnC;AACA,sBAAsBtB,aAAa,GAAG,sDAAsD,GAAG,EAAG;AAClG,sBAAsBI,SAAU;AAChC,iBAAkB;MAAAkC,QAAA,gBAGF3C,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAkC,QAAA,gBAE7C3C,OAAA;UAAKS,SAAS,EAAG;AACrC;AACA,0BAA0BgC,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AAC9D,qBAAsB;UAAAI,QAAA,EACGD,QAAQ,gBACL1C,OAAA,CAAC0C,QAAQ;YAACjC,SAAS,EAAC;UAAS;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhC/C,OAAA;YAAMS,SAAS,EAAC,mBAAmB;YAAAkC,QAAA,GAAC,GAAC,EAACxC,IAAI;UAAA;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QACpD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGL3C,SAAS,iBACNJ,OAAA;UAAKS,SAAS,EAAC,iFAAiF;UAAAkC,QAAA,eAC5F3C,OAAA;YAAMS,SAAS,EAAC,mBAAmB;YAAAkC,QAAA,GAAC,GAAC,EAACvC,SAAS;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGN/C,OAAA;QAAKS,SAAS,EAAC,wDAAwD;QAAAkC,QAAA,eACnE3C,OAAA;UAAKS,SAAS,EAAG;AACrC,0BAA0BU,MAAM,CAACP,MAAO;AACxC,0BAA0BuB,OAAO,CAACV,WAAY,IAAGU,OAAO,CAACL,WAAY;AACrE,qBAAsB;UAAAa,QAAA,EACGzC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,cAAc,IAAI9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+C,YAAY,gBACvCjD,OAAA;YACIkD,GAAG,EAAEhD,IAAI,CAAC8C,cAAc,IAAI9C,IAAI,CAAC+C,YAAa;YAC9CE,GAAG,EAAE,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,IAAI,KAAI,MAAO;YAC1B3C,SAAS,EAAC,0CAA0C;YACpD4C,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAQ;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,gBAEF/C,OAAA;YAAKS,SAAS,EAAC,8FAA8F;YAAAkC,QAAA,eACzG3C,OAAA;cAAMS,SAAS,EAAC,8BAA8B;cAAAkC,QAAA,EACzC,CAAC,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,IAAI,KAAI,GAAG,EAAEG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN/C,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAkC,QAAA,gBACtB3C,OAAA;UAAIS,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,kCAAkC;UAAA8B,QAAA,EACzE,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,IAAI,KAAI;QAAc;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAGL/C,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAkC,QAAA,gBACxC3C,OAAA,CAACH,UAAU;YACP4D,KAAK,EAAE,CAAAvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,YAAY,KAAI,CAAE;YAC/BnD,IAAI,EAAC,OAAO;YACZoD,SAAS,EAAE,KAAM;YACjBC,QAAQ,EAAE;UAAK;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACF/C,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAkC,QAAA,gBAC1B3C,OAAA;cAAGS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,4BAA4B;cAAA6B,QAAA,GACvD,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,OAAO,KAAI,CAAC,EAAC,KACxB;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACH,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,aAAa,IAAG,CAAC,iBACpB9D,OAAA;cAAGS,SAAS,EAAG,uBAAuB;cAAAkC,QAAA,GACjCzC,IAAI,CAAC4D,aAAa,EAAC,UACxB;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN/C,OAAA;UAAGS,SAAS,EAAG,uBAAuB;UAAAkC,QAAA,GACjC,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,WAAW,KAAI,CAAC,EAAC,eAC5B;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGH,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,YAAY,kBACfhE,OAAA;UAAGS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA6B,QAAA,GAAC,OACxC,EAACzC,IAAI,CAAC8D,YAAY,EAAC,GAC5B;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACN,EAEA,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,aAAa,IAAG,CAAC,iBACpBjE,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAkC,QAAA,gBACxC3C,OAAA,CAACP,OAAO;YAACgB,SAAS,EAAC;UAAyB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C/C,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAA6B,QAAA,EAC5DzC,IAAI,CAAC+D;UAAa;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR,EAGA,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,YAAY,KAAIhE,IAAI,CAACgE,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC/CnE,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAkC,QAAA,GACvCzC,IAAI,CAACgE,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAClDvE,OAAA,CAACF,wBAAwB;YAErBwE,WAAW,EAAEA,WAAY;YACzB/D,IAAI,EAAC,OAAO;YACZiE,WAAW,EAAE,IAAK;YAClBZ,QAAQ,EAAE,IAAK;YACfa,MAAM,EAAE;UAAM,GALTH,WAAW,CAACI,EAAE,IAAIJ,WAAW,CAACK,IAAI,IAAIJ,KAAK;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMnD,CACJ,CAAC,EACD7C,IAAI,CAACgE,YAAY,CAACC,MAAM,GAAG,CAAC,iBACzBnE,OAAA;YAAMS,SAAS,EAAC,4BAA4B;YAAAkC,QAAA,GAAC,GACxC,EAACzC,IAAI,CAACgE,YAAY,CAACC,MAAM,GAAG,CAAC;UAAA;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR,eAGD/C,OAAA;UAAKS,SAAS,EAAG;AACrC;AACA,0BAA0B0B,OAAO,CAACJ,OAAQ;AAC1C;AACA,qBAAsB;UAAAY,QAAA,gBACE3C,OAAA;YAAMS,SAAS,EAAC,SAAS;YAAAkC,QAAA,EAAER,OAAO,CAACN;UAAS;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpD/C,OAAA;YAAA2C,QAAA,EAAOR,OAAO,CAACP;UAAU;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;;EAEA;EACA,oBACI/C,OAAA;IACIS,SAAS,EAAG;AACxB,iDAAiDU,MAAM,CAACH,OAAQ,IAAGG,MAAM,CAACJ,OAAQ;AAClF;AACA;AACA,kBAAkBV,aAAa,GAAG,sDAAsD,GAAG,EAAG;AAC9F,kBAAkBI,SAAU;AAC5B,aAAc;IAAAkC,QAAA,gBAGF3C,OAAA;MAAKS,SAAS,EAAC,2CAA2C;MAAAkC,QAAA,gBAEtD3C,OAAA;QAAKS,SAAS,EAAG;AACjC;AACA,sBAAsBgC,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AAC1D,iBAAkB;QAAAI,QAAA,EACGD,QAAQ,gBACL1C,OAAA,CAAC0C,QAAQ;UAACjC,SAAS,EAAC;QAAS;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEhC/C,OAAA;UAAMS,SAAS,EAAC,mBAAmB;UAAAkC,QAAA,GAAC,GAAC,EAACxC,IAAI,IAAI,GAAG;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAC3D;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGL3C,SAAS,iBACNJ,OAAA;QAAKS,SAAS,EAAC,mFAAmF;QAAAkC,QAAA,eAC9F3C,OAAA;UAAMS,SAAS,EAAC,mBAAmB;UAAAkC,QAAA,GAAC,GAAC,EAACvC,SAAS;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGN/C,OAAA;MAAKS,SAAS,EAAC,iEAAiE;MAAAkC,QAAA,eAC5E3C,OAAA;QAAKS,SAAS,EAAG;AACjC,sBAAsBU,MAAM,CAACP,MAAO;AACpC,sBAAsBuB,OAAO,CAACV,WAAY,IAAGU,OAAO,CAACL,WAAY;AACjE,iBAAkB;QAAAa,QAAA,EACGzC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE8C,cAAc,IAAI9C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE+C,YAAY,gBACvCjD,OAAA;UACIkD,GAAG,EAAEhD,IAAI,CAAC8C,cAAc,IAAI9C,IAAI,CAAC+C,YAAa;UAC9CE,GAAG,EAAE,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,IAAI,KAAI,MAAO;UAC1B3C,SAAS,EAAC,0CAA0C;UACpD4C,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAQ;QAAE;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,gBAEF/C,OAAA;UAAKS,SAAS,EAAC,8FAA8F;UAAAkC,QAAA,eACzG3C,OAAA;YAAMS,SAAS,EAAC,8BAA8B;YAAAkC,QAAA,EACzC,CAAC,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,IAAI,KAAI,GAAG,EAAEG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN/C,OAAA;MAAKS,SAAS,EAAC,gBAAgB;MAAAkC,QAAA,gBAC3B3C,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAkC,QAAA,gBAC7C3C,OAAA;UAAIS,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,yBAAyB;UAAA8B,QAAA,EAChE,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,IAAI,KAAI;QAAc;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACL/C,OAAA,CAACH,UAAU;UACP4D,KAAK,EAAE,CAAAvD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD,YAAY,KAAI,CAAE;UAC/BnD,IAAI,EAAC,OAAO;UACZoD,SAAS,EAAE,KAAM;UACjBC,QAAQ,EAAE;QAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACF/C,OAAA;UAAMS,SAAS,EAAG;AACtC;AACA,0BAA0B0B,OAAO,CAACJ,OAAQ;AAC1C,qBAAsB;UAAAY,QAAA,EACGR,OAAO,CAACP;QAAU;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELvC,SAAS,iBACNR,OAAA;QAAKS,SAAS,EAAC,6BAA6B;QAAAkC,QAAA,gBAExC3C,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAkC,QAAA,gBACxC3C,OAAA,CAACN,MAAM;YAACe,SAAS,EAAC;UAAuB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5C/C,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,4BAA4B;YAAA6B,QAAA,GAC1D,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,OAAO,KAAI,CAAC,EAAC,KACxB;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN/C,OAAA;UAAMS,SAAS,EAAG,uBAAuB;UAAAkC,QAAA,GACpC,CAAAzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,WAAW,KAAI,CAAC,EAAC,MAC5B;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAEN,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,gBAAgB,MAAKC,SAAS,iBACjC7E,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,iBAAiB;UAAA6B,QAAA,GAC/CzC,IAAI,CAAC0E,gBAAgB,EAAC,SAC3B;QAAA;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,YAAY,MAAKD,SAAS,iBAC7B7E,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA6B,QAAA,GAC9CzC,IAAI,CAAC4E,YAAY,EAAC,UACvB;QAAA;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D,YAAY,kBACfhE,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA6B,QAAA,GAC9CzC,IAAI,CAAC8D,YAAY,EAAC,OACvB;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,aAAa,IAAG,CAAC,iBACpBjE,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAkC,QAAA,gBACxC3C,OAAA,CAACP,OAAO;YAACgB,SAAS,EAAC;UAAyB;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C/C,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAA6B,QAAA,EAC5DzC,IAAI,CAAC+D;UAAa;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR,EAGA,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D,aAAa,IAAG,CAAC,iBACpB9D,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAkC,QAAA,eACjB3C,OAAA,CAACJ,aAAa;UACVmF,SAAS,EAAE7E,IAAI,CAAC2D,OAAO,IAAI,CAAE;UAC7BA,OAAO,EAAE,CAAC3D,IAAI,CAAC2D,OAAO,IAAI,CAAC,KAAK3D,IAAI,CAAC4D,aAAa,IAAI,CAAC,CAAE;UACzDJ,YAAY,EAAExD,IAAI,CAACwD,YAAY,IAAI,CAAE;UACrCI,aAAa,EAAE5D,IAAI,CAAC4D,aAAa,IAAI,CAAE;UACvCvD,IAAI,EAAC,OAAO;UACZyE,SAAS,EAAE,KAAM;UACjBC,aAAa,EAAE,KAAM;UACrBC,aAAa,EAAE;QAAM;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGA,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,YAAY,KAAIhE,IAAI,CAACgE,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC/CnE,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAkC,QAAA,eACjB3C,OAAA,CAACL,eAAe;UACZuE,YAAY,EAAEhE,IAAI,CAACgE,YAAa;UAChCiB,UAAU,EAAE,CAAE;UACd5E,IAAI,EAAC,OAAO;UACZD,MAAM,EAAC;QAAY;UAAAsC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGN/C,OAAA;MAAKS,SAAS,EAAC,oCAAoC;MAAAkC,QAAA,gBAC/C3C,OAAA;QAAA2C,QAAA,gBACI3C,OAAA;UAAKS,SAAS,EAAG,aAAYU,MAAM,CAACN,IAAK,IAAGR,aAAa,GAAG,eAAe,GAAG,eAAgB,EAAE;UAAAsC,QAAA,EAC3F,CAACzC,IAAI,CAACkF,YAAY,IAAIlF,IAAI,CAACmF,KAAK,IAAInF,IAAI,CAAC2D,OAAO,IAAI3D,IAAI,CAAC6D,WAAW,IAAI,CAAC,EAAEuB,cAAc,CAAC;QAAC;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,eACN/C,OAAA;UAAKS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA6B,QAAA,EAC7CzC,IAAI,CAACkF,YAAY,GAAG,aAAa,GAAGlF,IAAI,CAAC2D,OAAO,GAAG,IAAI,GAAG;QAAQ;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,EACL7C,IAAI,CAACqF,SAAS,iBACXvF,OAAA;UAAKS,SAAS,EAAC,4BAA4B;UAAAkC,QAAA,GAAC,MACpC,EAAC,CAACzC,IAAI,CAAC2D,OAAO,IAAI,CAAC,EAAEyB,cAAc,CAAC,CAAC;QAAA;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGN/C,OAAA;QAAKS,SAAS,EAAG;AACjC;AACA,sBAAsB0B,OAAO,CAACJ,OAAQ;AACtC;AACA,iBAAkB;QAAAY,QAAA,gBACE3C,OAAA;UAAMS,SAAS,EAAC,SAAS;UAAAkC,QAAA,EAAER,OAAO,CAACN;QAAS;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpD/C,OAAA;UAAA2C,QAAA,EAAOR,OAAO,CAACP;QAAU;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACyC,EAAA,GAxaIvF,eAAe;AA0arB,eAAeA,eAAe;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}