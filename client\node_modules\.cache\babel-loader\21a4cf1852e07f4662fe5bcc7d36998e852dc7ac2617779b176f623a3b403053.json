{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { TbUser, TbUsers, TbTrophy, TbPlayerPlay, TbPlayerPause, TbClock, TbSearch, TbFilter } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingList = ({\n  users = [],\n  currentUserId = null,\n  layout = 'horizontal',\n  // 'horizontal', 'vertical', 'grid'\n  size = 'medium',\n  showStats = true,\n  className = '',\n  currentUserRef = null,\n  showFindMe = false,\n  lastUpdated = null,\n  autoRefresh = false,\n  onAutoRefreshToggle = null\n}) => {\n  _s();\n  // State for search and filtering\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'\n  const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'\n  const [localShowFindMe, setLocalShowFindMe] = useState(false);\n  const localCurrentUserRef = useRef(null);\n\n  // Use passed refs or local ones\n  const userRef = currentUserRef || localCurrentUserRef;\n  const findMeActive = showFindMe || localShowFindMe;\n\n  // Filter and search users\n  const filteredUsers = users.filter(user => {\n    var _user$name, _user$email, _user$subscriptionSta;\n    // Search filter\n    const matchesSearch = ((_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Subscription filter\n    const userStatus = ((_user$subscriptionSta = user.subscriptionStatus) === null || _user$subscriptionSta === void 0 ? void 0 : _user$subscriptionSta.toLowerCase()) || 'free';\n    let matchesFilter = true;\n    switch (filterType) {\n      case 'premium':\n        matchesFilter = userStatus === 'premium' || userStatus === 'active';\n        break;\n      case 'expired':\n        matchesFilter = userStatus === 'expired';\n        break;\n      case 'free':\n        matchesFilter = userStatus === 'free';\n        break;\n      default:\n        matchesFilter = true;\n    }\n    return matchesSearch && matchesFilter;\n  }).sort((a, b) => {\n    switch (sortBy) {\n      case 'xp':\n        return (b.totalXP || 0) - (a.totalXP || 0);\n      case 'name':\n        return (a.name || '').localeCompare(b.name || '');\n      default:\n        return (a.rank || 0) - (b.rank || 0);\n    }\n  });\n\n  // Calculate class ranks for filtered users\n  const usersWithClassRank = filteredUsers.map(user => {\n    // Group users by class and calculate class rank\n    const sameClassUsers = filteredUsers.filter(u => u.class === user.class);\n    const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n    return {\n      ...user,\n      classRank\n    };\n  });\n\n  // Find current user\n  const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n\n  // Scroll to current user\n  const scrollToCurrentUser = () => {\n    if (userRef.current) {\n      userRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setLocalShowFindMe(true);\n      // Hide the highlight after 3 seconds\n      setTimeout(() => setLocalShowFindMe(false), 3000);\n    }\n  };\n\n  // Get layout classes\n  const getLayoutClasses = () => {\n    switch (layout) {\n      case 'vertical':\n        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n      case 'grid':\n        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n      case 'horizontal':\n      default:\n        return 'space-y-3';\n    }\n  };\n\n  // Container animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  // Stats summary with enhanced calculations\n  const totalUsers = users.length;\n  const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium' || u.normalizedSubscriptionStatus === 'premium').length;\n\n  // Use ranking score or XP as the primary metric\n  const topScore = users.length > 0 ? Math.max(...users.map(u => u.rankingScore || u.totalXP || u.totalPoints || 0)) : 0;\n\n  // Calculate additional stats\n  const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n  const averageXP = users.length > 0 ? Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [showStats && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-7 h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent\",\n                children: \"Leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: \"Top performers across all levels\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 29\n          }, this), lastUpdated && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-blue-700 font-medium\",\n              children: [\"Updated \", new Date(lastUpdated).toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [onAutoRefreshToggle && /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: onAutoRefreshToggle,\n            className: `px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${autoRefresh ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`,\n            children: [autoRefresh ? /*#__PURE__*/_jsxDEV(TbPlayerPause, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 52\n            }, this) : /*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 92\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: autoRefresh ? 'Auto' : 'Manual'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 33\n          }, this), currentUserId && /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: scrollToCurrentUser,\n            className: \"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUser, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Find Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 sm:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-blue-700\",\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-blue-900 mb-1\",\n            children: totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-blue-600 font-medium\",\n            children: [activeUsers, \" active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-yellow-700\",\n              children: \"Premium Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-yellow-900 mb-1\",\n            children: premiumUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-yellow-600 font-medium\",\n            children: [totalUsers > 0 ? Math.round(premiumUsers / totalUsers * 100) : 0, \"% premium\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-green-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbUser, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-green-700\",\n              children: \"Top Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-green-900 mb-1\",\n            children: topScore.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-green-600 font-medium\",\n            children: \"ranking points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-purple-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-purple-700\",\n              children: \"Avg XP\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-purple-900 mb-1\",\n            children: averageXP.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: \"experience points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: containerVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      className: getLayoutClasses(),\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: usersWithClassRank.map((user, index) => {\n          const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n          const rank = user.rank || index + 1;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            ref: isCurrentUser ? userRef : null,\n            layout: true,\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              scale: 0.9\n            },\n            transition: {\n              duration: 0.2\n            },\n            className: `${isCurrentUser && findMeActive ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl' : isCurrentUser ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(UserRankingCard, {\n              user: user,\n              rank: rank,\n              classRank: user.classRank,\n              isCurrentUser: isCurrentUser,\n              layout: layout,\n              size: size,\n              showStats: showStats\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 33\n            }, this)\n          }, user.userId || user._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 13\n    }, this), usersWithClassRank.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No users found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"No users available to display\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 17\n    }, this), currentUserId && usersWithClassRank.length > 10 && /*#__PURE__*/_jsxDEV(motion.button, {\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      onClick: scrollToCurrentUser,\n      className: \"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\",\n      title: \"Find me in ranking\",\n      children: /*#__PURE__*/_jsxDEV(TbUser, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 9\n  }, this);\n};\n_s(UserRankingList, \"leUq98Lm+jUc6TAm6eqSyBcgK5k=\");\n_c = UserRankingList;\nexport default UserRankingList;\nvar _c;\n$RefreshReg$(_c, \"UserRankingList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "TbUser", "TbUsers", "TbTrophy", "TbPlayerPlay", "TbPlayerPause", "TbClock", "TbSearch", "Tb<PERSON><PERSON>er", "UserRankingCard", "jsxDEV", "_jsxDEV", "UserRankingList", "users", "currentUserId", "layout", "size", "showStats", "className", "currentUserRef", "showFindMe", "lastUpdated", "autoRefresh", "onAutoRefreshToggle", "_s", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "sortBy", "setSortBy", "localShowFindMe", "setLocalShowFindMe", "localCurrentUserRef", "userRef", "findMeActive", "filteredUsers", "filter", "user", "_user$name", "_user$email", "_user$subscriptionSta", "matchesSearch", "name", "toLowerCase", "includes", "email", "userStatus", "subscriptionStatus", "matchesFilter", "sort", "a", "b", "totalXP", "localeCompare", "rank", "usersWithClassRank", "map", "sameClassUsers", "u", "class", "classRank", "findIndex", "_id", "userId", "currentUser", "find", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "setTimeout", "getLayoutClasses", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "totalUsers", "length", "premiumUsers", "normalizedSubscriptionStatus", "topScore", "Math", "max", "rankingScore", "totalPoints", "activeUsers", "totalQuizzesTaken", "averageXP", "round", "reduce", "sum", "children", "motion", "div", "initial", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Date", "toLocaleTimeString", "button", "whileHover", "scale", "whileTap", "onClick", "toLocaleString", "variants", "AnimatePresence", "index", "isCurrentUser", "ref", "exit", "duration", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingList.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { TbUser, TbUsers, Tb<PERSON>rophy, TbPlayerPlay, TbPlayerPause, Tb<PERSON>lock, TbSearch, TbFilter } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\n\nconst UserRankingList = ({\n    users = [],\n    currentUserId = null,\n    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'\n    size = 'medium',\n    showStats = true,\n    className = '',\n    currentUserRef = null,\n    showFindMe = false,\n    lastUpdated = null,\n    autoRefresh = false,\n    onAutoRefreshToggle = null\n}) => {\n    // State for search and filtering\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'\n    const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'\n    const [localShowFindMe, setLocalShowFindMe] = useState(false);\n    const localCurrentUserRef = useRef(null);\n\n    // Use passed refs or local ones\n    const userRef = currentUserRef || localCurrentUserRef;\n    const findMeActive = showFindMe || localShowFindMe;\n\n    // Filter and search users\n    const filteredUsers = users.filter(user => {\n        // Search filter\n        const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            user.email?.toLowerCase().includes(searchTerm.toLowerCase());\n\n        // Subscription filter\n        const userStatus = user.subscriptionStatus?.toLowerCase() || 'free';\n        let matchesFilter = true;\n\n        switch (filterType) {\n            case 'premium':\n                matchesFilter = userStatus === 'premium' || userStatus === 'active';\n                break;\n            case 'expired':\n                matchesFilter = userStatus === 'expired';\n                break;\n            case 'free':\n                matchesFilter = userStatus === 'free';\n                break;\n            default:\n                matchesFilter = true;\n        }\n\n        return matchesSearch && matchesFilter;\n    }).sort((a, b) => {\n        switch (sortBy) {\n            case 'xp':\n                return (b.totalXP || 0) - (a.totalXP || 0);\n            case 'name':\n                return (a.name || '').localeCompare(b.name || '');\n            default:\n                return (a.rank || 0) - (b.rank || 0);\n        }\n    });\n\n    // Calculate class ranks for filtered users\n    const usersWithClassRank = filteredUsers.map(user => {\n        // Group users by class and calculate class rank\n        const sameClassUsers = filteredUsers.filter(u => u.class === user.class);\n        const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n        return { ...user, classRank };\n    });\n\n    // Find current user\n    const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n\n    // Scroll to current user\n    const scrollToCurrentUser = () => {\n        if (userRef.current) {\n            userRef.current.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n            setLocalShowFindMe(true);\n            // Hide the highlight after 3 seconds\n            setTimeout(() => setLocalShowFindMe(false), 3000);\n        }\n    };\n\n    // Get layout classes\n    const getLayoutClasses = () => {\n        switch (layout) {\n            case 'vertical':\n                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n            case 'grid':\n                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n            case 'horizontal':\n            default:\n                return 'space-y-3';\n        }\n    };\n\n    // Container animation variants\n    const containerVariants = {\n        hidden: { opacity: 0 },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n\n    // Stats summary with enhanced calculations\n    const totalUsers = users.length;\n    const premiumUsers = users.filter(u =>\n        u.subscriptionStatus === 'active' ||\n        u.subscriptionStatus === 'premium' ||\n        u.normalizedSubscriptionStatus === 'premium'\n    ).length;\n\n    // Use ranking score or XP as the primary metric\n    const topScore = users.length > 0 ? Math.max(...users.map(u =>\n        u.rankingScore || u.totalXP || u.totalPoints || 0\n    )) : 0;\n\n    // Calculate additional stats\n    const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n    const averageXP = users.length > 0 ?\n        Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n\n    return (\n        <div className={`space-y-6 ${className}`}>\n            {/* Header with Stats */}\n            {showStats && (\n                <motion.div\n                    initial={{ opacity: 0, y: -20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\"\n                >\n                    <div className=\"flex items-center justify-between mb-6\">\n                        <div className=\"flex items-center space-x-4\">\n                            <div className=\"flex items-center space-x-3\">\n                                <div className=\"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg\">\n                                    <TbTrophy className=\"w-7 h-7 text-white\" />\n                                </div>\n                                <div>\n                                    <h2 className=\"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent\">\n                                        Leaderboard\n                                    </h2>\n                                    <p className=\"text-sm text-gray-600 font-medium\">Top performers across all levels</p>\n                                </div>\n                            </div>\n\n                            {lastUpdated && (\n                                <div className=\"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200\">\n                                    <TbClock className=\"w-4 h-4 text-blue-600\" />\n                                    <span className=\"text-sm text-blue-700 font-medium\">\n                                        Updated {new Date(lastUpdated).toLocaleTimeString()}\n                                    </span>\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"flex items-center space-x-2\">\n                            {/* Auto-refresh toggle */}\n                            {onAutoRefreshToggle && (\n                                <motion.button\n                                    whileHover={{ scale: 1.05 }}\n                                    whileTap={{ scale: 0.95 }}\n                                    onClick={onAutoRefreshToggle}\n                                    className={`px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${\n                                        autoRefresh\n                                            ? 'bg-green-500 hover:bg-green-600 text-white'\n                                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'\n                                    }`}\n                                >\n                                    {autoRefresh ? <TbPlayerPause className=\"w-4 h-4\" /> : <TbPlayerPlay className=\"w-4 h-4\" />}\n                                    <span className=\"hidden sm:inline\">\n                                        {autoRefresh ? 'Auto' : 'Manual'}\n                                    </span>\n                                </motion.button>\n                            )}\n\n\n\n                            {/* Find Me button */}\n                            {currentUserId && (\n                                <motion.button\n                                    whileHover={{ scale: 1.05 }}\n                                    whileTap={{ scale: 0.95 }}\n                                    onClick={scrollToCurrentUser}\n                                    className=\"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\"\n                                >\n                                    <TbUser className=\"w-4 h-4\" />\n                                    <span className=\"hidden sm:inline\">Find Me</span>\n                                </motion.button>\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4\">\n                        <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-blue-500 rounded-lg\">\n                                    <TbUsers className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-blue-700\">Total Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-blue-900 mb-1\">{totalUsers}</div>\n                            <div className=\"text-xs text-blue-600 font-medium\">{activeUsers} active</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-yellow-700\">Premium Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-yellow-900 mb-1\">{premiumUsers}</div>\n                            <div className=\"text-xs text-yellow-600 font-medium\">\n                                {totalUsers > 0 ? Math.round((premiumUsers / totalUsers) * 100) : 0}% premium\n                            </div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-green-500 rounded-lg\">\n                                    <TbUser className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-green-700\">Top Score</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-green-900 mb-1\">{topScore.toLocaleString()}</div>\n                            <div className=\"text-xs text-green-600 font-medium\">ranking points</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-purple-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-purple-700\">Avg XP</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-purple-900 mb-1\">{averageXP.toLocaleString()}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">experience points</div>\n                        </div>\n                    </div>\n                </motion.div>\n            )}\n\n\n\n            {/* User List */}\n            <motion.div\n                variants={containerVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className={getLayoutClasses()}\n            >\n                <AnimatePresence>\n                    {usersWithClassRank.map((user, index) => {\n                        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n                        const rank = user.rank || index + 1;\n\n                        return (\n                            <motion.div\n                                key={user.userId || user._id}\n                                ref={isCurrentUser ? userRef : null}\n                                layout\n                                initial={{ opacity: 0, scale: 0.9 }}\n                                animate={{ opacity: 1, scale: 1 }}\n                                exit={{ opacity: 0, scale: 0.9 }}\n                                transition={{ duration: 0.2 }}\n                                className={`${\n                                    isCurrentUser && findMeActive\n                                        ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl'\n                                        : isCurrentUser\n                                        ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg'\n                                        : ''\n                                }`}\n                            >\n                                <UserRankingCard\n                                    user={user}\n                                    rank={rank}\n                                    classRank={user.classRank}\n                                    isCurrentUser={isCurrentUser}\n                                    layout={layout}\n                                    size={size}\n                                    showStats={showStats}\n                                />\n                            </motion.div>\n                        );\n                    })}\n                </AnimatePresence>\n            </motion.div>\n\n            {/* Empty State */}\n            {usersWithClassRank.length === 0 && (\n                <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    className=\"text-center py-12\"\n                >\n                    <TbUsers className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No users found</h3>\n                    <p className=\"text-gray-500\">\n                        No users available to display\n                    </p>\n                </motion.div>\n            )}\n\n            {/* Floating Action Button for Current User */}\n            {currentUserId && usersWithClassRank.length > 10 && (\n                <motion.button\n                    initial={{ opacity: 0, scale: 0 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    onClick={scrollToCurrentUser}\n                    className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\"\n                    title=\"Find me in ranking\"\n                >\n                    <TbUser className=\"w-6 h-6\" />\n                </motion.button>\n            )}\n        </div>\n    );\n};\n\nexport default UserRankingList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACpH,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,eAAe,GAAGA,CAAC;EACrBC,KAAK,GAAG,EAAE;EACVC,aAAa,GAAG,IAAI;EACpBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG,EAAE;EACdC,cAAc,GAAG,IAAI;EACrBC,UAAU,GAAG,KAAK;EAClBC,WAAW,GAAG,IAAI;EAClBC,WAAW,GAAG,KAAK;EACnBC,mBAAmB,GAAG;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMkC,mBAAmB,GAAGjC,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMkC,OAAO,GAAGf,cAAc,IAAIc,mBAAmB;EACrD,MAAME,YAAY,GAAGf,UAAU,IAAIW,eAAe;;EAElD;EACA,MAAMK,aAAa,GAAGvB,KAAK,CAACwB,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,qBAAA;IACvC;IACA,MAAMC,aAAa,GAAG,EAAAH,UAAA,GAAAD,IAAI,CAACK,IAAI,cAAAJ,UAAA,uBAATA,UAAA,CAAWK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,UAAU,CAACmB,WAAW,CAAC,CAAC,CAAC,OAAAJ,WAAA,GAC7DF,IAAI,CAACQ,KAAK,cAAAN,WAAA,uBAAVA,WAAA,CAAYI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,UAAU,CAACmB,WAAW,CAAC,CAAC,CAAC;;IAEhF;IACA,MAAMG,UAAU,GAAG,EAAAN,qBAAA,GAAAH,IAAI,CAACU,kBAAkB,cAAAP,qBAAA,uBAAvBA,qBAAA,CAAyBG,WAAW,CAAC,CAAC,KAAI,MAAM;IACnE,IAAIK,aAAa,GAAG,IAAI;IAExB,QAAQtB,UAAU;MACd,KAAK,SAAS;QACVsB,aAAa,GAAGF,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,QAAQ;QACnE;MACJ,KAAK,SAAS;QACVE,aAAa,GAAGF,UAAU,KAAK,SAAS;QACxC;MACJ,KAAK,MAAM;QACPE,aAAa,GAAGF,UAAU,KAAK,MAAM;QACrC;MACJ;QACIE,aAAa,GAAG,IAAI;IAC5B;IAEA,OAAOP,aAAa,IAAIO,aAAa;EACzC,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACd,QAAQvB,MAAM;MACV,KAAK,IAAI;QACL,OAAO,CAACuB,CAAC,CAACC,OAAO,IAAI,CAAC,KAAKF,CAAC,CAACE,OAAO,IAAI,CAAC,CAAC;MAC9C,KAAK,MAAM;QACP,OAAO,CAACF,CAAC,CAACR,IAAI,IAAI,EAAE,EAAEW,aAAa,CAACF,CAAC,CAACT,IAAI,IAAI,EAAE,CAAC;MACrD;QACI,OAAO,CAACQ,CAAC,CAACI,IAAI,IAAI,CAAC,KAAKH,CAAC,CAACG,IAAI,IAAI,CAAC,CAAC;IAC5C;EACJ,CAAC,CAAC;;EAEF;EACA,MAAMC,kBAAkB,GAAGpB,aAAa,CAACqB,GAAG,CAACnB,IAAI,IAAI;IACjD;IACA,MAAMoB,cAAc,GAAGtB,aAAa,CAACC,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKtB,IAAI,CAACsB,KAAK,CAAC;IACxE,MAAMC,SAAS,GAAGH,cAAc,CAACI,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,GAAG,KAAKzB,IAAI,CAACyB,GAAG,IAAIJ,CAAC,CAACK,MAAM,KAAK1B,IAAI,CAAC0B,MAAM,CAAC,GAAG,CAAC;IACnG,OAAO;MAAE,GAAG1B,IAAI;MAAEuB;IAAU,CAAC;EACjC,CAAC,CAAC;;EAEF;EACA,MAAMI,WAAW,GAAGT,kBAAkB,CAACU,IAAI,CAAC5B,IAAI,IAAIA,IAAI,CAACyB,GAAG,KAAKjD,aAAa,IAAIwB,IAAI,CAAC0B,MAAM,KAAKlD,aAAa,CAAC;;EAEhH;EACA,MAAMqD,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIjC,OAAO,CAACkC,OAAO,EAAE;MACjBlC,OAAO,CAACkC,OAAO,CAACC,cAAc,CAAC;QAC3BC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACFvC,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACAwC,UAAU,CAAC,MAAMxC,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACrD;EACJ,CAAC;;EAED;EACA,MAAMyC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,QAAQ1D,MAAM;MACV,KAAK,UAAU;QACX,OAAO,qEAAqE;MAChF,KAAK,MAAM;QACP,OAAO,sDAAsD;MACjE,KAAK,YAAY;MACjB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;;EAED;EACA,MAAM2D,iBAAiB,GAAG;IACtBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACLD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACRC,eAAe,EAAE;MACrB;IACJ;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGnE,KAAK,CAACoE,MAAM;EAC/B,MAAMC,YAAY,GAAGrE,KAAK,CAACwB,MAAM,CAACsB,CAAC,IAC/BA,CAAC,CAACX,kBAAkB,KAAK,QAAQ,IACjCW,CAAC,CAACX,kBAAkB,KAAK,SAAS,IAClCW,CAAC,CAACwB,4BAA4B,KAAK,SACvC,CAAC,CAACF,MAAM;;EAER;EACA,MAAMG,QAAQ,GAAGvE,KAAK,CAACoE,MAAM,GAAG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAGzE,KAAK,CAAC4C,GAAG,CAACE,CAAC,IACvDA,CAAC,CAAC4B,YAAY,IAAI5B,CAAC,CAACN,OAAO,IAAIM,CAAC,CAAC6B,WAAW,IAAI,CACpD,CAAC,CAAC,GAAG,CAAC;;EAEN;EACA,MAAMC,WAAW,GAAG5E,KAAK,CAACwB,MAAM,CAACsB,CAAC,IAAI,CAACA,CAAC,CAAC+B,iBAAiB,IAAI,CAAC,IAAI,CAAC,CAAC,CAACT,MAAM;EAC5E,MAAMU,SAAS,GAAG9E,KAAK,CAACoE,MAAM,GAAG,CAAC,GAC9BI,IAAI,CAACO,KAAK,CAAC/E,KAAK,CAACgF,MAAM,CAAC,CAACC,GAAG,EAAEnC,CAAC,KAAKmC,GAAG,IAAInC,CAAC,CAACN,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGxC,KAAK,CAACoE,MAAM,CAAC,GAAG,CAAC;EAEtF,oBACItE,OAAA;IAAKO,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAA6E,QAAA,GAEpC9E,SAAS,iBACNN,OAAA,CAACqF,MAAM,CAACC,GAAG;MACPC,OAAO,EAAE;QAAEtB,OAAO,EAAE,CAAC;QAAEuB,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAExB,OAAO,EAAE,CAAC;QAAEuB,CAAC,EAAE;MAAE,CAAE;MAC9BjF,SAAS,EAAC,kFAAkF;MAAA6E,QAAA,gBAE5FpF,OAAA;QAAKO,SAAS,EAAC,wCAAwC;QAAA6E,QAAA,gBACnDpF,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAA6E,QAAA,gBACxCpF,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA6E,QAAA,gBACxCpF,OAAA;cAAKO,SAAS,EAAC,yFAAyF;cAAA6E,QAAA,eACpGpF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN7F,OAAA;cAAAoF,QAAA,gBACIpF,OAAA;gBAAIO,SAAS,EAAC,2HAA2H;gBAAA6E,QAAA,EAAC;cAE1I;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7F,OAAA;gBAAGO,SAAS,EAAC,mCAAmC;gBAAA6E,QAAA,EAAC;cAAgC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAELnF,WAAW,iBACRV,OAAA;YAAKO,SAAS,EAAC,oFAAoF;YAAA6E,QAAA,gBAC/FpF,OAAA,CAACL,OAAO;cAACY,SAAS,EAAC;YAAuB;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C7F,OAAA;cAAMO,SAAS,EAAC,mCAAmC;cAAA6E,QAAA,GAAC,UACxC,EAAC,IAAIU,IAAI,CAACpF,WAAW,CAAC,CAACqF,kBAAkB,CAAC,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEN7F,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAA6E,QAAA,GAEvCxE,mBAAmB,iBAChBZ,OAAA,CAACqF,MAAM,CAACW,MAAM;YACVC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BE,OAAO,EAAExF,mBAAoB;YAC7BL,SAAS,EAAG,+FACRI,WAAW,GACL,4CAA4C,GAC5C,6CACT,EAAE;YAAAyE,QAAA,GAEFzE,WAAW,gBAAGX,OAAA,CAACN,aAAa;cAACa,SAAS,EAAC;YAAS;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG7F,OAAA,CAACP,YAAY;cAACc,SAAS,EAAC;YAAS;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3F7F,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAA6E,QAAA,EAC7BzE,WAAW,GAAG,MAAM,GAAG;YAAQ;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAClB,EAKA1F,aAAa,iBACVH,OAAA,CAACqF,MAAM,CAACW,MAAM;YACVC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BE,OAAO,EAAE5C,mBAAoB;YAC7BjD,SAAS,EAAC,0IAA0I;YAAA6E,QAAA,gBAEpJpF,OAAA,CAACV,MAAM;cAACiB,SAAS,EAAC;YAAS;cAAAmF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B7F,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAA6E,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAClB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN7F,OAAA;QAAKO,SAAS,EAAC,uCAAuC;QAAA6E,QAAA,gBAClDpF,OAAA;UAAKO,SAAS,EAAC,yJAAyJ;UAAA6E,QAAA,gBACpKpF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,gBAC7CpF,OAAA;cAAKO,SAAS,EAAC,4BAA4B;cAAA6E,QAAA,eACvCpF,OAAA,CAACT,OAAO;gBAACgB,SAAS,EAAC;cAAoB;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACN7F,OAAA;cAAMO,SAAS,EAAC,qCAAqC;cAAA6E,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACN7F,OAAA;YAAKO,SAAS,EAAC,wCAAwC;YAAA6E,QAAA,EAAEf;UAAU;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1E7F,OAAA;YAAKO,SAAS,EAAC,mCAAmC;YAAA6E,QAAA,GAAEN,WAAW,EAAC,SAAO;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAEN7F,OAAA;UAAKO,SAAS,EAAC,6JAA6J;UAAA6E,QAAA,gBACxKpF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,gBAC7CpF,OAAA;cAAKO,SAAS,EAAC,+DAA+D;cAAA6E,QAAA,eAC1EpF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN7F,OAAA;cAAMO,SAAS,EAAC,uCAAuC;cAAA6E,QAAA,EAAC;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACN7F,OAAA;YAAKO,SAAS,EAAC,0CAA0C;YAAA6E,QAAA,EAAEb;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9E7F,OAAA;YAAKO,SAAS,EAAC,qCAAqC;YAAA6E,QAAA,GAC/Cf,UAAU,GAAG,CAAC,GAAGK,IAAI,CAACO,KAAK,CAAEV,YAAY,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,WACxE;UAAA;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN7F,OAAA;UAAKO,SAAS,EAAC,4JAA4J;UAAA6E,QAAA,gBACvKpF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,gBAC7CpF,OAAA;cAAKO,SAAS,EAAC,6BAA6B;cAAA6E,QAAA,eACxCpF,OAAA,CAACV,MAAM;gBAACiB,SAAS,EAAC;cAAoB;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN7F,OAAA;cAAMO,SAAS,EAAC,sCAAsC;cAAA6E,QAAA,EAAC;YAAS;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACN7F,OAAA;YAAKO,SAAS,EAAC,yCAAyC;YAAA6E,QAAA,EAAEX,QAAQ,CAAC4B,cAAc,CAAC;UAAC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1F7F,OAAA;YAAKO,SAAS,EAAC,oCAAoC;YAAA6E,QAAA,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAEN7F,OAAA;UAAKO,SAAS,EAAC,2JAA2J;UAAA6E,QAAA,gBACtKpF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,gBAC7CpF,OAAA;cAAKO,SAAS,EAAC,8BAA8B;cAAA6E,QAAA,eACzCpF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN7F,OAAA;cAAMO,SAAS,EAAC,uCAAuC;cAAA6E,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACN7F,OAAA;YAAKO,SAAS,EAAC,0CAA0C;YAAA6E,QAAA,EAAEJ,SAAS,CAACqB,cAAc,CAAC;UAAC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5F7F,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA6E,QAAA,EAAC;UAAiB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACf,eAKD7F,OAAA,CAACqF,MAAM,CAACC,GAAG;MACPgB,QAAQ,EAAEvC,iBAAkB;MAC5BwB,OAAO,EAAC,QAAQ;MAChBE,OAAO,EAAC,SAAS;MACjBlF,SAAS,EAAEuD,gBAAgB,CAAC,CAAE;MAAAsB,QAAA,eAE9BpF,OAAA,CAACuG,eAAe;QAAAnB,QAAA,EACXvC,kBAAkB,CAACC,GAAG,CAAC,CAACnB,IAAI,EAAE6E,KAAK,KAAK;UACrC,MAAMC,aAAa,GAAG9E,IAAI,CAAC0B,MAAM,KAAKlD,aAAa,IAAIwB,IAAI,CAACyB,GAAG,KAAKjD,aAAa;UACjF,MAAMyC,IAAI,GAAGjB,IAAI,CAACiB,IAAI,IAAI4D,KAAK,GAAG,CAAC;UAEnC,oBACIxG,OAAA,CAACqF,MAAM,CAACC,GAAG;YAEPoB,GAAG,EAAED,aAAa,GAAGlF,OAAO,GAAG,IAAK;YACpCnB,MAAM;YACNmF,OAAO,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAEiC,KAAK,EAAE;YAAI,CAAE;YACpCT,OAAO,EAAE;cAAExB,OAAO,EAAE,CAAC;cAAEiC,KAAK,EAAE;YAAE,CAAE;YAClCS,IAAI,EAAE;cAAE1C,OAAO,EAAE,CAAC;cAAEiC,KAAK,EAAE;YAAI,CAAE;YACjC/B,UAAU,EAAE;cAAEyC,QAAQ,EAAE;YAAI,CAAE;YAC9BrG,SAAS,EAAG,GACRkG,aAAa,IAAIjF,YAAY,GACvB,6GAA6G,GAC7GiF,aAAa,GACb,+CAA+C,GAC/C,EACT,EAAE;YAAArB,QAAA,eAEHpF,OAAA,CAACF,eAAe;cACZ6B,IAAI,EAAEA,IAAK;cACXiB,IAAI,EAAEA,IAAK;cACXM,SAAS,EAAEvB,IAAI,CAACuB,SAAU;cAC1BuD,aAAa,EAAEA,aAAc;cAC7BrG,MAAM,EAAEA,MAAO;cACfC,IAAI,EAAEA,IAAK;cACXC,SAAS,EAAEA;YAAU;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC,GAvBGlE,IAAI,CAAC0B,MAAM,IAAI1B,IAAI,CAACyB,GAAG;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBpB,CAAC;QAErB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGZhD,kBAAkB,CAACyB,MAAM,KAAK,CAAC,iBAC5BtE,OAAA,CAACqF,MAAM,CAACC,GAAG;MACPC,OAAO,EAAE;QAAEtB,OAAO,EAAE;MAAE,CAAE;MACxBwB,OAAO,EAAE;QAAExB,OAAO,EAAE;MAAE,CAAE;MACxB1D,SAAS,EAAC,mBAAmB;MAAA6E,QAAA,gBAE7BpF,OAAA,CAACT,OAAO;QAACgB,SAAS,EAAC;MAAsC;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5D7F,OAAA;QAAIO,SAAS,EAAC,wCAAwC;QAAA6E,QAAA,EAAC;MAAc;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1E7F,OAAA;QAAGO,SAAS,EAAC,eAAe;QAAA6E,QAAA,EAAC;MAE7B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACf,EAGA1F,aAAa,IAAI0C,kBAAkB,CAACyB,MAAM,GAAG,EAAE,iBAC5CtE,OAAA,CAACqF,MAAM,CAACW,MAAM;MACVT,OAAO,EAAE;QAAEtB,OAAO,EAAE,CAAC;QAAEiC,KAAK,EAAE;MAAE,CAAE;MAClCT,OAAO,EAAE;QAAExB,OAAO,EAAE,CAAC;QAAEiC,KAAK,EAAE;MAAE,CAAE;MAClCD,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MACzBE,OAAO,EAAE5C,mBAAoB;MAC7BjD,SAAS,EAAC,6IAA6I;MACvJsG,KAAK,EAAC,oBAAoB;MAAAzB,QAAA,eAE1BpF,OAAA,CAACV,MAAM;QAACiB,SAAS,EAAC;MAAS;QAAAmF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAClB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAChF,EAAA,CAnUIZ,eAAe;AAAA6G,EAAA,GAAf7G,eAAe;AAqUrB,eAAeA,eAAe;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}