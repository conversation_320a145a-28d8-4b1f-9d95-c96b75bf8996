{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { TbBrain, TbSearch, TbFilter } from 'react-icons/tb';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReportsByUser } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { QuizGrid } from '../../../components/modern';\nimport './responsive.css';\nimport './style.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = () => {\n  _s();\n  const [exams, setExams] = useState([]);\n  const [filteredExams, setFilteredExams] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [userResults, setUserResults] = useState({});\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n\n  // Set default class filter to user's class\n  const userClass = (user === null || user === void 0 ? void 0 : user.class) || '';\n\n  // Fetch user results\n  const getUserResults = async () => {\n    try {\n      if (!(user !== null && user !== void 0 && user._id)) return;\n      const response = await getAllReportsByUser({\n        userId: user._id\n      });\n      if (response.success) {\n        // Create a map of exam ID to latest result\n        const resultsMap = {};\n        response.data.forEach(report => {\n          const examId = report.exam._id;\n          // Keep only the latest result for each exam\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\n            resultsMap[examId] = {\n              ...report.result,\n              createdAt: report.createdAt,\n              completedAt: report.createdAt\n            };\n          }\n        });\n        setUserResults(resultsMap);\n      }\n    } catch (error) {\n      console.error('Error fetching user results:', error);\n    }\n  };\n  useEffect(() => {\n    const getExams = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getAllExams();\n        dispatch(HideLoading());\n        if (response.success) {\n          // Sort exams by creation date (newest first)\n          const sortedExams = response.data.sort((a, b) => {\n            return new Date(b.createdAt || b.date || 0) - new Date(a.createdAt || a.date || 0);\n          });\n          setExams(sortedExams);\n\n          // Set default filter to user's class if available (with proper type conversion)\n          if (userClass) {\n            // Convert to string to match exam class format\n            setSelectedClass(String(userClass));\n          }\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n      }\n    };\n    getExams();\n    getUserResults(); // Fetch user results\n  }, [dispatch, userClass, user === null || user === void 0 ? void 0 : user._id]);\n\n  // Filter exams based on search term and selected class\n  useEffect(() => {\n    let filtered = [...exams];\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(exam => {\n        var _exam$subject;\n        return exam.name.toLowerCase().includes(searchTerm.toLowerCase()) || ((_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.toLowerCase().includes(searchTerm.toLowerCase()));\n      });\n    }\n\n    // Filter by class (with proper type conversion)\n    if (selectedClass) {\n      filtered = filtered.filter(exam => {\n        // Convert both to strings for comparison to handle number vs string mismatch\n        return String(exam.class) === String(selectedClass);\n      });\n    }\n\n    // Sort filtered results by newest first\n    filtered.sort((a, b) => {\n      return new Date(b.createdAt || b.date || 0) - new Date(a.createdAt || a.date || 0);\n    });\n    setFilteredExams(filtered);\n  }, [exams, searchTerm, selectedClass]);\n\n  // Get unique classes for filter dropdown\n  const availableClasses = [...new Set(exams.map(exam => exam.class).filter(Boolean))].sort();\n  const handleQuizStart = quiz => {\n    navigate(`/quiz/${quiz._id}/start`);\n  };\n\n  // Refresh data when component becomes visible (user returns from quiz)\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (!document.hidden) {\n        // Page became visible, refresh user results\n        getUserResults();\n      }\n    };\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    return () => {\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n    };\n  }, [user === null || user === void 0 ? void 0 : user._id]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"quiz-listing-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-listing-content\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"quiz-listing-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mb-6 shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(TbBrain, {\n              className: \"w-10 h-10 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"heading-2 text-gradient mb-4\",\n            children: \"Challenge Your Mind\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n            children: \"Test your knowledge with our comprehensive quizzes. Track your progress and improve your skills.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-6 mt-6 text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-green-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [exams.length, \" Available Quizzes\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-2 h-2 bg-blue-500 rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Your Class: \", userClass || 'All Classes']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-4xl mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(TbSearch, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search quizzes by name or subject...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sm:w-48\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(TbFilter, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: selectedClass,\n                  onChange: e => setSelectedClass(e.target.value),\n                  className: \"block w-full pl-10 pr-8 py-3 border border-gray-300 rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base appearance-none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Classes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this), availableClasses.map(className => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: className,\n                    children: [\"Class \", className]\n                  }, className, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 text-gray-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M19 9l-7 7-7-7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl px-4 py-3 border border-blue-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-medium text-blue-700\",\n                  children: [filteredExams.length, \" quiz\", filteredExams.length !== 1 ? 'es' : '', \" found\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 17\n              }, this), selectedClass && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-blue-600\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full\",\n                  children: [\"Class \", selectedClass]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-blue-600\",\n                  children: \"\\u2022\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full\",\n                  children: [\"\\\"\", searchTerm, \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), (searchTerm || selectedClass) && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSearchTerm('');\n                setSelectedClass('');\n              },\n              className: \"text-xs text-blue-600 hover:text-blue-800 font-medium transition-colors px-3 py-1 rounded-full hover:bg-blue-100\",\n              children: \"Clear filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: filteredExams.length > 0 ? /*#__PURE__*/_jsxDEV(QuizGrid, {\n          quizzes: filteredExams,\n          onQuizStart: handleQuizStart,\n          showResults: true,\n          userResults: userResults,\n          className: \"quiz-grid-container\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 13\n        }, this) : exams.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-16\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n              children: /*#__PURE__*/_jsxDEV(TbSearch, {\n                className: \"w-12 h-12 text-gray-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: \"No quizzes found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500 mb-6\",\n              children: \"We couldn't find any quizzes matching your search criteria. Try adjusting your filters or search terms.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchTerm('');\n                  setSelectedClass('');\n                },\n                className: \"w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-medium\",\n                children: \"Show All Quizzes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-400\",\n                children: \"or try searching for a different topic\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-16\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n              children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-12 h-12 text-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-3\",\n              children: \"No quizzes available yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500 mb-6\",\n              children: \"Quizzes will appear here once your instructor adds them. Check back soon!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.reload(),\n              className: \"px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-medium\",\n              children: \"Refresh Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 5\n  }, this);\n};\n_s(Quiz, \"j/Z5CDFVZuYgbdgfZCQob5bT6dE=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = Quiz;\nexport default Quiz;\nvar _c;\n$RefreshReg$(_c, \"Quiz\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useDispatch", "useSelector", "motion", "message", "TbBrain", "TbSearch", "Tb<PERSON><PERSON>er", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "QuizGrid", "jsxDEV", "_jsxDEV", "Quiz", "_s", "exams", "setExams", "filteredExams", "setFilteredExams", "searchTerm", "setSearchTerm", "selectedClass", "setSelectedClass", "userResults", "setUserResults", "navigate", "dispatch", "user", "state", "userClass", "class", "getUserResults", "_id", "response", "userId", "success", "resultsMap", "data", "for<PERSON>ach", "report", "examId", "exam", "Date", "createdAt", "result", "completedAt", "error", "console", "getExams", "sortedExams", "sort", "a", "b", "date", "String", "filtered", "filter", "_exam$subject", "name", "toLowerCase", "includes", "subject", "availableClasses", "Set", "map", "Boolean", "handleQuizStart", "quiz", "handleVisibilityChange", "document", "hidden", "addEventListener", "removeEventListener", "className", "children", "div", "initial", "opacity", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "transition", "delay", "type", "placeholder", "value", "onChange", "e", "target", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "quizzes", "onQuizStart", "showResults", "window", "location", "reload", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { motion } from 'framer-motion';\r\nimport { message } from 'antd';\r\nimport { Tb<PERSON><PERSON>, TbSearch, TbFilter } from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { getAllReportsByUser } from '../../../apicalls/reports';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport { QuizGrid } from '../../../components/modern';\r\nimport './responsive.css';\r\nimport './style.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedClass, setSelectedClass] = useState('');\r\n  const [userResults, setUserResults] = useState({});\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  // Set default class filter to user's class\r\n  const userClass = user?.class || '';\r\n\r\n  // Fetch user results\r\n  const getUserResults = async () => {\r\n    try {\r\n      if (!user?._id) return;\r\n\r\n      const response = await getAllReportsByUser({ userId: user._id });\r\n      if (response.success) {\r\n        // Create a map of exam ID to latest result\r\n        const resultsMap = {};\r\n        response.data.forEach(report => {\r\n          const examId = report.exam._id;\r\n          // Keep only the latest result for each exam\r\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\r\n            resultsMap[examId] = {\r\n              ...report.result,\r\n              createdAt: report.createdAt,\r\n              completedAt: report.createdAt\r\n            };\r\n          }\r\n        });\r\n        setUserResults(resultsMap);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching user results:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const getExams = async () => {\r\n      try {\r\n        dispatch(ShowLoading());\r\n        const response = await getAllExams();\r\n        dispatch(HideLoading());\r\n\r\n        if (response.success) {\r\n          // Sort exams by creation date (newest first)\r\n          const sortedExams = response.data.sort((a, b) => {\r\n            return new Date(b.createdAt || b.date || 0) - new Date(a.createdAt || a.date || 0);\r\n          });\r\n\r\n          setExams(sortedExams);\r\n\r\n          // Set default filter to user's class if available (with proper type conversion)\r\n          if (userClass) {\r\n            // Convert to string to match exam class format\r\n            setSelectedClass(String(userClass));\r\n          }\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message);\r\n      }\r\n    };\r\n\r\n    getExams();\r\n    getUserResults(); // Fetch user results\r\n  }, [dispatch, userClass, user?._id]);\r\n\r\n  // Filter exams based on search term and selected class\r\n  useEffect(() => {\r\n    let filtered = [...exams];\r\n\r\n    // Filter by search term\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(exam =>\r\n        exam.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n\r\n    // Filter by class (with proper type conversion)\r\n    if (selectedClass) {\r\n      filtered = filtered.filter(exam => {\r\n        // Convert both to strings for comparison to handle number vs string mismatch\r\n        return String(exam.class) === String(selectedClass);\r\n      });\r\n    }\r\n\r\n    // Sort filtered results by newest first\r\n    filtered.sort((a, b) => {\r\n      return new Date(b.createdAt || b.date || 0) - new Date(a.createdAt || a.date || 0);\r\n    });\r\n\r\n    setFilteredExams(filtered);\r\n  }, [exams, searchTerm, selectedClass]);\r\n\r\n  // Get unique classes for filter dropdown\r\n  const availableClasses = [...new Set(exams.map(exam => exam.class).filter(Boolean))].sort();\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    navigate(`/quiz/${quiz._id}/start`);\r\n  };\r\n\r\n  // Refresh data when component becomes visible (user returns from quiz)\r\n  useEffect(() => {\r\n    const handleVisibilityChange = () => {\r\n      if (!document.hidden) {\r\n        // Page became visible, refresh user results\r\n        getUserResults();\r\n      }\r\n    };\r\n\r\n    document.addEventListener('visibilitychange', handleVisibilityChange);\r\n    return () => {\r\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\r\n    };\r\n  }, [user?._id]);\r\n\r\n  return (\r\n    <div className=\"quiz-listing-container\">\r\n      <div className=\"quiz-listing-content\">\r\n\r\n\r\n        {/* Enhanced Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"quiz-listing-header\"\r\n        >\r\n          <div className=\"text-center mb-6\">\r\n            <div className=\"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mb-6 shadow-lg\">\r\n              <TbBrain className=\"w-10 h-10 text-white\" />\r\n            </div>\r\n            <h1 className=\"heading-2 text-gradient mb-4\">\r\n              Challenge Your Mind\r\n            </h1>\r\n            <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\r\n              Test your knowledge with our comprehensive quizzes. Track your progress and improve your skills.\r\n            </p>\r\n            <div className=\"flex items-center justify-center space-x-6 mt-6 text-sm text-gray-500\">\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\r\n                <span>{exams.length} Available Quizzes</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\r\n                <span>Your Class: {userClass || 'All Classes'}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Search and Filter Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"mb-8\"\r\n        >\r\n          <div className=\"max-w-4xl mx-auto\">\r\n            <div className=\"flex flex-col sm:flex-row gap-4 mb-6\">\r\n              {/* Search Box */}\r\n              <div className=\"flex-1 relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                  <TbSearch className=\"h-5 w-5 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search quizzes by name or subject...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-xl leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base\"\r\n                />\r\n              </div>\r\n\r\n              {/* Class Filter */}\r\n              <div className=\"sm:w-48\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                    <TbFilter className=\"h-5 w-5 text-gray-400\" />\r\n                  </div>\r\n                  <select\r\n                    value={selectedClass}\r\n                    onChange={(e) => setSelectedClass(e.target.value)}\r\n                    className=\"block w-full pl-10 pr-8 py-3 border border-gray-300 rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base appearance-none\"\r\n                  >\r\n                    <option value=\"\">All Classes</option>\r\n                    {availableClasses.map((className) => (\r\n                      <option key={className} value={className}>\r\n                        Class {className}\r\n                      </option>\r\n                    ))}\r\n                  </select>\r\n                  <div className=\"absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none\">\r\n                    <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\r\n                    </svg>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Enhanced Results Summary */}\r\n            <div className=\"flex items-center justify-between bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl px-4 py-3 border border-blue-100\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"flex items-center space-x-2\">\r\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\r\n                  <span className=\"text-sm font-medium text-blue-700\">\r\n                    {filteredExams.length} quiz{filteredExams.length !== 1 ? 'es' : ''} found\r\n                  </span>\r\n                </div>\r\n                {selectedClass && (\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <span className=\"text-xs text-blue-600\">•</span>\r\n                    <span className=\"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full\">\r\n                      Class {selectedClass}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n                {searchTerm && (\r\n                  <div className=\"flex items-center space-x-2\">\r\n                    <span className=\"text-xs text-blue-600\">•</span>\r\n                    <span className=\"text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full\">\r\n                      \"{searchTerm}\"\r\n                    </span>\r\n                  </div>\r\n                )}\r\n              </div>\r\n              {(searchTerm || selectedClass) && (\r\n                <button\r\n                  onClick={() => {\r\n                    setSearchTerm('');\r\n                    setSelectedClass('');\r\n                  }}\r\n                  className=\"text-xs text-blue-600 hover:text-blue-800 font-medium transition-colors px-3 py-1 rounded-full hover:bg-blue-100\"\r\n                >\r\n                  Clear filters\r\n                </button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Quiz Grid */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n        >\r\n          {filteredExams.length > 0 ? (\r\n            <QuizGrid\r\n              quizzes={filteredExams}\r\n              onQuizStart={handleQuizStart}\r\n              showResults={true}\r\n              userResults={userResults}\r\n              className=\"quiz-grid-container\"\r\n            />\r\n          ) : exams.length > 0 ? (\r\n            <div className=\"text-center py-16\">\r\n              <div className=\"max-w-md mx-auto\">\r\n                <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n                  <TbSearch className=\"w-12 h-12 text-gray-400\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\r\n                  No quizzes found\r\n                </h3>\r\n                <p className=\"text-gray-500 mb-6\">\r\n                  We couldn't find any quizzes matching your search criteria. Try adjusting your filters or search terms.\r\n                </p>\r\n                <div className=\"space-y-3\">\r\n                  <button\r\n                    onClick={() => {\r\n                      setSearchTerm('');\r\n                      setSelectedClass('');\r\n                    }}\r\n                    className=\"w-full sm:w-auto px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-medium\"\r\n                  >\r\n                    Show All Quizzes\r\n                  </button>\r\n                  <div className=\"text-sm text-gray-400\">\r\n                    or try searching for a different topic\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center py-16\">\r\n              <div className=\"max-w-md mx-auto\">\r\n                <div className=\"w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n                  <TbBrain className=\"w-12 h-12 text-blue-500\" />\r\n                </div>\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\r\n                  No quizzes available yet\r\n                </h3>\r\n                <p className=\"text-gray-500 mb-6\">\r\n                  Quizzes will appear here once your instructor adds them. Check back soon!\r\n                </p>\r\n                <button\r\n                  onClick={() => window.location.reload()}\r\n                  className=\"px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-medium\"\r\n                >\r\n                  Refresh Page\r\n                </button>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AAC5D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAO,kBAAkB;AACzB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM6B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM4B,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE4B;EAAK,CAAC,GAAG3B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAEnD;EACA,MAAME,SAAS,GAAG,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,KAAK,KAAI,EAAE;;EAEnC;EACA,MAAMC,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,IAAI,EAACJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEK,GAAG,GAAE;MAEhB,MAAMC,QAAQ,GAAG,MAAM1B,mBAAmB,CAAC;QAAE2B,MAAM,EAAEP,IAAI,CAACK;MAAI,CAAC,CAAC;MAChE,IAAIC,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMC,UAAU,GAAG,CAAC,CAAC;QACrBH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAACC,MAAM,IAAI;UAC9B,MAAMC,MAAM,GAAGD,MAAM,CAACE,IAAI,CAACT,GAAG;UAC9B;UACA,IAAI,CAACI,UAAU,CAACI,MAAM,CAAC,IAAI,IAAIE,IAAI,CAACH,MAAM,CAACI,SAAS,CAAC,GAAG,IAAID,IAAI,CAACN,UAAU,CAACI,MAAM,CAAC,CAACG,SAAS,CAAC,EAAE;YAC9FP,UAAU,CAACI,MAAM,CAAC,GAAG;cACnB,GAAGD,MAAM,CAACK,MAAM;cAChBD,SAAS,EAAEJ,MAAM,CAACI,SAAS;cAC3BE,WAAW,EAAEN,MAAM,CAACI;YACtB,CAAC;UACH;QACF,CAAC,CAAC;QACFnB,cAAc,CAACY,UAAU,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAEDjD,SAAS,CAAC,MAAM;IACd,MAAMmD,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACFtB,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAMwB,QAAQ,GAAG,MAAM3B,WAAW,CAAC,CAAC;QACpCoB,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAIyB,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMc,WAAW,GAAGhB,QAAQ,CAACI,IAAI,CAACa,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YAC/C,OAAO,IAAIV,IAAI,CAACU,CAAC,CAACT,SAAS,IAAIS,CAAC,CAACC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAIX,IAAI,CAACS,CAAC,CAACR,SAAS,IAAIQ,CAAC,CAACE,IAAI,IAAI,CAAC,CAAC;UACpF,CAAC,CAAC;UAEFrC,QAAQ,CAACiC,WAAW,CAAC;;UAErB;UACA,IAAIpB,SAAS,EAAE;YACb;YACAP,gBAAgB,CAACgC,MAAM,CAACzB,SAAS,CAAC,CAAC;UACrC;QACF,CAAC,MAAM;UACL3B,OAAO,CAAC4C,KAAK,CAACb,QAAQ,CAAC/B,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAO4C,KAAK,EAAE;QACdpB,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAAC;QACvBN,OAAO,CAAC4C,KAAK,CAACA,KAAK,CAAC5C,OAAO,CAAC;MAC9B;IACF,CAAC;IAED8C,QAAQ,CAAC,CAAC;IACVjB,cAAc,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC,EAAE,CAACL,QAAQ,EAAEG,SAAS,EAAEF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,GAAG,CAAC,CAAC;;EAEpC;EACAnC,SAAS,CAAC,MAAM;IACd,IAAI0D,QAAQ,GAAG,CAAC,GAAGxC,KAAK,CAAC;;IAEzB;IACA,IAAII,UAAU,EAAE;MACdoC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACf,IAAI;QAAA,IAAAgB,aAAA;QAAA,OAC7BhB,IAAI,CAACiB,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,UAAU,CAACwC,WAAW,CAAC,CAAC,CAAC,MAAAF,aAAA,GAC1DhB,IAAI,CAACoB,OAAO,cAAAJ,aAAA,uBAAZA,aAAA,CAAcE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzC,UAAU,CAACwC,WAAW,CAAC,CAAC,CAAC;MAAA,CAChE,CAAC;IACH;;IAEA;IACA,IAAItC,aAAa,EAAE;MACjBkC,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACf,IAAI,IAAI;QACjC;QACA,OAAOa,MAAM,CAACb,IAAI,CAACX,KAAK,CAAC,KAAKwB,MAAM,CAACjC,aAAa,CAAC;MACrD,CAAC,CAAC;IACJ;;IAEA;IACAkC,QAAQ,CAACL,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,OAAO,IAAIV,IAAI,CAACU,CAAC,CAACT,SAAS,IAAIS,CAAC,CAACC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAIX,IAAI,CAACS,CAAC,CAACR,SAAS,IAAIQ,CAAC,CAACE,IAAI,IAAI,CAAC,CAAC;IACpF,CAAC,CAAC;IAEFnC,gBAAgB,CAACqC,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACxC,KAAK,EAAEI,UAAU,EAAEE,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAMyC,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAChD,KAAK,CAACiD,GAAG,CAACvB,IAAI,IAAIA,IAAI,CAACX,KAAK,CAAC,CAAC0B,MAAM,CAACS,OAAO,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,CAAC;EAE3F,MAAMgB,eAAe,GAAIC,IAAI,IAAK;IAChC1C,QAAQ,CAAE,SAAQ0C,IAAI,CAACnC,GAAI,QAAO,CAAC;EACrC,CAAC;;EAED;EACAnC,SAAS,CAAC,MAAM;IACd,MAAMuE,sBAAsB,GAAGA,CAAA,KAAM;MACnC,IAAI,CAACC,QAAQ,CAACC,MAAM,EAAE;QACpB;QACAvC,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;IAEDsC,QAAQ,CAACE,gBAAgB,CAAC,kBAAkB,EAAEH,sBAAsB,CAAC;IACrE,OAAO,MAAM;MACXC,QAAQ,CAACG,mBAAmB,CAAC,kBAAkB,EAAEJ,sBAAsB,CAAC;IAC1E,CAAC;EACH,CAAC,EAAE,CAACzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,GAAG,CAAC,CAAC;EAEf,oBACEpB,OAAA;IAAK6D,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACrC9D,OAAA;MAAK6D,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAInC9D,OAAA,CAACX,MAAM,CAAC0E,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BL,SAAS,EAAC,qBAAqB;QAAAC,QAAA,eAE/B9D,OAAA;UAAK6D,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/B9D,OAAA;YAAK6D,SAAS,EAAC,6HAA6H;YAAAC,QAAA,eAC1I9D,OAAA,CAACT,OAAO;cAACsE,SAAS,EAAC;YAAsB;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACNvE,OAAA;YAAI6D,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAE7C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvE,OAAA;YAAG6D,SAAS,EAAC,yCAAyC;YAAAC,QAAA,EAAC;UAEvD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJvE,OAAA;YAAK6D,SAAS,EAAC,uEAAuE;YAAAC,QAAA,gBACpF9D,OAAA;cAAK6D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9D,OAAA;gBAAK6D,SAAS,EAAC;cAAmC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzDvE,OAAA;gBAAA8D,QAAA,GAAO3D,KAAK,CAACqE,MAAM,EAAC,oBAAkB;cAAA;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNvE,OAAA;cAAK6D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9D,OAAA;gBAAK6D,SAAS,EAAC;cAAkC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDvE,OAAA;gBAAA8D,QAAA,GAAM,cAAY,EAAC7C,SAAS,IAAI,aAAa;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbvE,OAAA,CAACX,MAAM,CAAC0E,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAC3Bb,SAAS,EAAC,MAAM;QAAAC,QAAA,eAEhB9D,OAAA;UAAK6D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9D,OAAA;YAAK6D,SAAS,EAAC,sCAAsC;YAAAC,QAAA,gBAEnD9D,OAAA;cAAK6D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B9D,OAAA;gBAAK6D,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnF9D,OAAA,CAACR,QAAQ;kBAACqE,SAAS,EAAC;gBAAuB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACNvE,OAAA;gBACE2E,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,sCAAsC;gBAClDC,KAAK,EAAEtE,UAAW;gBAClBuE,QAAQ,EAAGC,CAAC,IAAKvE,aAAa,CAACuE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/ChB,SAAS,EAAC;cAAkO;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7O,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNvE,OAAA;cAAK6D,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtB9D,OAAA;gBAAK6D,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvB9D,OAAA;kBAAK6D,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnF9D,OAAA,CAACP,QAAQ;oBAACoE,SAAS,EAAC;kBAAuB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNvE,OAAA;kBACE6E,KAAK,EAAEpE,aAAc;kBACrBqE,QAAQ,EAAGC,CAAC,IAAKrE,gBAAgB,CAACqE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAClDhB,SAAS,EAAC,wLAAwL;kBAAAC,QAAA,gBAElM9D,OAAA;oBAAQ6E,KAAK,EAAC,EAAE;oBAAAf,QAAA,EAAC;kBAAW;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpCrB,gBAAgB,CAACE,GAAG,CAAES,SAAS,iBAC9B7D,OAAA;oBAAwB6E,KAAK,EAAEhB,SAAU;oBAAAC,QAAA,GAAC,QAClC,EAACD,SAAS;kBAAA,GADLA,SAAS;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEd,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eACTvE,OAAA;kBAAK6D,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eACpF9D,OAAA;oBAAK6D,SAAS,EAAC,uBAAuB;oBAACoB,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAArB,QAAA,eAC1F9D,OAAA;sBAAMoF,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNvE,OAAA;YAAK6D,SAAS,EAAC,0HAA0H;YAAAC,QAAA,gBACvI9D,OAAA;cAAK6D,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C9D,OAAA;gBAAK6D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C9D,OAAA;kBAAK6D,SAAS,EAAC;gBAAgD;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtEvE,OAAA;kBAAM6D,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,GAChDzD,aAAa,CAACmE,MAAM,EAAC,OAAK,EAACnE,aAAa,CAACmE,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,EAAC,QACrE;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EACL9D,aAAa,iBACZT,OAAA;gBAAK6D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C9D,OAAA;kBAAM6D,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDvE,OAAA;kBAAM6D,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,GAAC,QACnE,EAACrD,aAAa;gBAAA;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN,EACAhE,UAAU,iBACTP,OAAA;gBAAK6D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1C9D,OAAA;kBAAM6D,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChDvE,OAAA;kBAAM6D,SAAS,EAAC,0DAA0D;kBAAAC,QAAA,GAAC,IACxE,EAACvD,UAAU,EAAC,IACf;gBAAA;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACL,CAAChE,UAAU,IAAIE,aAAa,kBAC3BT,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAM;gBACbhF,aAAa,CAAC,EAAE,CAAC;gBACjBE,gBAAgB,CAAC,EAAE,CAAC;cACtB,CAAE;cACFmD,SAAS,EAAC,kHAAkH;cAAAC,QAAA,EAC7H;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbvE,OAAA,CAACX,MAAM,CAAC0E,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BO,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QAAAZ,QAAA,EAE1BzD,aAAa,CAACmE,MAAM,GAAG,CAAC,gBACvBxE,OAAA,CAACF,QAAQ;UACP2F,OAAO,EAAEpF,aAAc;UACvBqF,WAAW,EAAEpC,eAAgB;UAC7BqC,WAAW,EAAE,IAAK;UAClBhF,WAAW,EAAEA,WAAY;UACzBkD,SAAS,EAAC;QAAqB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,GACApE,KAAK,CAACqE,MAAM,GAAG,CAAC,gBAClBxE,OAAA;UAAK6D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC9D,OAAA;YAAK6D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B9D,OAAA;cAAK6D,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/F9D,OAAA,CAACR,QAAQ;gBAACqE,SAAS,EAAC;cAAyB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNvE,OAAA;cAAI6D,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLvE,OAAA;cAAG6D,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJvE,OAAA;cAAK6D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9D,OAAA;gBACEwF,OAAO,EAAEA,CAAA,KAAM;kBACbhF,aAAa,CAAC,EAAE,CAAC;kBACjBE,gBAAgB,CAAC,EAAE,CAAC;gBACtB,CAAE;gBACFmD,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,EACzH;cAED;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvE,OAAA;gBAAK6D,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAENvE,OAAA;UAAK6D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChC9D,OAAA;YAAK6D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B9D,OAAA;cAAK6D,SAAS,EAAC,kFAAkF;cAAAC,QAAA,eAC/F9D,OAAA,CAACT,OAAO;gBAACsE,SAAS,EAAC;cAAyB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACNvE,OAAA;cAAI6D,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAEzD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLvE,OAAA;cAAG6D,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAElC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJvE,OAAA;cACEwF,OAAO,EAAEA,CAAA,KAAMI,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxCjC,SAAS,EAAC,6FAA6F;cAAAC,QAAA,EACxG;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGH,CAAC;AAEV,CAAC;AAACrE,EAAA,CA7TID,IAAI;EAAA,QAMSf,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAA2G,EAAA,GARxB9F,IAAI;AA+TV,eAAeA,IAAI;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}