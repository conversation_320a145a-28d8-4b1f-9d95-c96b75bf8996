import React, { useEffect, useState, useRef, useCallback } from "react";
import { useSelector } from "react-redux";
import { Tb<PERSON><PERSON>hy, TbMedal, TbRefresh, TbAlertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from "react-icons/tb";
import { getAllReportsForRanking } from "../../../apicalls/reports";
import UserRankingList from "../../../components/modern/UserRankingList";
import { message } from "antd";
import axiosInstance from "../../../apicalls";
import { isSessionValid, validateTokenFormat, cleanupInvalidSession } from "../../../utils/authUtils";
import { autoRefreshToken } from "../../../apicalls/auth";

const Ranking = () => {
    console.log('🏆 Ranking component rendered');

    const userState = useSelector((state) => state.users || {});
    const { user } = userState;
    const [rankingData, setRankingData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [refreshing, setRefreshing] = useState(false);
    const [currentUserRank, setCurrentUserRank] = useState(null);
    const [showFindMe, setShowFindMe] = useState(false);
    const [lastUpdated, setLastUpdated] = useState(null);
    const [autoRefresh, setAutoRefresh] = useState(false);
    const currentUserRef = useRef(null);
    const refreshIntervalRef = useRef(null);

    console.log('🏆 Ranking state - rankingData length:', rankingData.length, 'loading:', loading, 'error:', error);

    const fetchRankingData = useCallback(async (showRefreshMessage = false) => {
        try {
            // Check if user is available
            if (!user || !user._id) {
                console.log('🚫 User not available, skipping ranking fetch');
                return;
            }

            console.log('🔄 Starting ranking data fetch for user:', user._id);
            setLoading(true);

            // Check if session is valid and refresh token if needed
            if (!isSessionValid()) {
                console.log('🔒 Session invalid, redirecting to login');
                message.error('Your session has expired. Please login again.');
                window.location.href = '/login';
                return;
            }

            // Validate and refresh token if needed
            const currentToken = localStorage.getItem('token');
            if (!currentToken) {
                console.log('🔒 No token found, redirecting to login');
                message.error('Please login to view rankings');
                window.location.href = '/login';
                return;
            }

            // Validate token format
            const tokenValidation = validateTokenFormat(currentToken);
            if (!tokenValidation.isValid) {
                console.log('🔒 Token validation failed:', tokenValidation.error);
                cleanupInvalidSession();
                message.error('Invalid session. Please login again.');
                window.location.href = '/login';
                return;
            }

            console.log('✅ Token validation passed, expires in', Math.floor(tokenValidation.timeLeft / 3600), 'hours');

            // Try to auto-refresh token if it's expiring soon
            try {
                await autoRefreshToken();
                console.log('🔄 Token auto-refresh attempted');
            } catch (refreshError) {
                console.log('⚠️ Token refresh failed:', refreshError);
                // Continue anyway, the request might still work
            }

            if (showRefreshMessage) {
                setRefreshing(true);
                message.loading("Refreshing rankings...", 1);
            }

            // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking
            let response;

            // Add cache-busting timestamp to ensure fresh data
            const timestamp = new Date().getTime();

            // Ensure we have the latest token for all requests
            const latestToken = localStorage.getItem('token');

            try {
                // Try new XP-based leaderboard first
                console.log('🎯 Attempting XP leaderboard API call...');

                const xpResponse = await axiosInstance.get(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Authorization': `Bearer ${latestToken}`
                    }
                });

                console.log('📊 XP leaderboard response:', xpResponse.data);

                if (xpResponse.data.success) {
                    response = xpResponse.data;
                    console.log('✅ Using XP-based leaderboard with', response.data?.length || 0, 'users');
                } else {
                    throw new Error('XP leaderboard failed, trying enhanced leaderboard');
                }
            } catch (xpError) {
                console.log('❌ XP leaderboard failed, trying enhanced leaderboard:', xpError);
                try {
                    const enhancedResponse = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {
                        headers: {
                            'Cache-Control': 'no-cache',
                            'Authorization': `Bearer ${latestToken}`
                        }
                    });

                    console.log('📊 Enhanced leaderboard response:', enhancedResponse.data);

                    if (enhancedResponse.data.success) {
                        response = enhancedResponse.data;
                        console.log('✅ Using enhanced leaderboard with', response.data?.length || 0, 'users');
                    } else {
                        throw new Error('Enhanced leaderboard failed');
                    }
                } catch (enhancedError) {
                    console.log('❌ Falling back to regular ranking:', enhancedError);
                    response = await getAllReportsForRanking();
                    console.log('📊 Regular ranking response:', response);
                }
            }

            if (response.success) {
                console.log('🔄 Transforming ranking data...', response.data?.length || 0, 'users');

                // Transform data to match UserRankingCard expectations with enhanced XP system support
                const transformedData = response.data.map((userData, index) => ({
                    // User identification - handle both old and new API formats
                    userId: userData.userId || userData._id,
                    _id: userData.userId || userData._id,
                    name: userData.userName || userData.name || 'Unknown User',
                    profilePicture: userData.userPhoto || userData.profileImage,
                    profileImage: userData.profileImage || userData.userPhoto,
                    school: userData.userSchool || userData.school || 'Unknown School',
                    class: userData.userClass || userData.class || 'Unknown',
                    level: userData.userLevel || userData.level || 'Primary',
                    email: userData.email,

                    // Legacy points system
                    totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,
                    totalPointsEarned: userData.totalPointsEarned || userData.totalPoints || 0,
                    quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,
                    totalQuizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,
                    passedExamsCount: userData.passedExamsCount || 0,
                    retryCount: userData.retryCount || 0,
                    scoreRatio: userData.scoreRatio || 0,

                    // XP System data (new)
                    totalXP: userData.totalXP || 0,
                    currentLevel: userData.currentLevel || 1,
                    xpToNextLevel: userData.xpToNextLevel || 0,
                    seasonXP: userData.seasonXP || 0,
                    lifetimeXP: userData.lifetimeXP || 0,

                    // Statistics
                    averageScore: userData.averageScore || 0,
                    bestStreak: userData.bestStreak || 0,
                    currentStreak: userData.currentStreak || 0,
                    achievements: userData.achievements || [],
                    achievementCount: userData.achievementCount || (userData.achievements ? userData.achievements.length : 0),

                    // Ranking data (prioritize ranking score from enhanced system)
                    rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,
                    rank: userData.rank || index + 1,
                    score: userData.rankingScore || userData.score || userData.totalXP || userData.totalPoints || 0,

                    // Subscription status (handle normalized status)
                    subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',
                    normalizedSubscriptionStatus: userData.normalizedSubscriptionStatus || userData.subscriptionStatus || 'free',
                    subscriptionPlan: userData.subscriptionPlan,
                    subscriptionEndDate: userData.subscriptionEndDate,

                    // XP breakdown (if available from new system)
                    breakdown: userData.breakdown || null,

                    // Additional metadata
                    createdAt: userData.createdAt,
                    updatedAt: userData.updatedAt
                }));

                console.log('✅ Setting ranking data with', transformedData.length, 'users');
                console.log('📋 Sample transformed user:', transformedData[0]);
                console.log('📋 All transformed data:', transformedData);

                setRankingData(transformedData);
                setError(null);
                setLastUpdated(new Date());

                // Find current user's rank
                const userRank = transformedData.findIndex(item =>
                    item._id === user?._id || item.userId === user?._id
                );
                setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);

                if (userRank >= 0) {
                    console.log('🎯 Current user rank:', userRank + 1);
                } else {
                    console.log('⚠️ Current user not found in ranking data');
                }

                if (showRefreshMessage) {
                    message.success("Rankings updated successfully!");
                }
            } else {
                console.log('❌ Response not successful:', response);
                setError(response.message || "Failed to fetch ranking data");
                message.error("Failed to load rankings");
            }
        } catch (err) {
            console.error('Ranking fetch error:', err);

            // Handle different types of errors
            let errorMessage = "An error occurred while fetching rankings";

            if (err.response) {
                // Server responded with error status
                const status = err.response.status;
                const responseMessage = err.response.data?.message || '';

                if (status === 401) {
                    // Authentication error
                    console.log('🔒 Authentication error, clearing session');
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                    errorMessage = "Your session has expired. Please login again.";
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                } else {
                    errorMessage = responseMessage || `Server error: ${status}`;
                }
            } else if (err.request) {
                // Request was made but no response received
                errorMessage = "Network error: Unable to connect to server";
            } else if (err.message) {
                // Something else happened
                errorMessage = err.message;
            }

            setError(errorMessage);
            message.error(errorMessage);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    }, [user]); // Add user as dependency

    useEffect(() => {
        console.log('🔄 Ranking component mounted, user:', user);
        console.log('🔄 User available:', !!user, 'User ID:', user?._id);

        if (user && user._id) {
            console.log('✅ User available, fetching ranking data');
            fetchRankingData();
        } else {
            console.log('⚠️ User not available yet, waiting...');
        }

        // Cleanup function to reset states when component unmounts
        return () => {
            setLoading(false);
            setRefreshing(false);
            setError(null);
        };
    }, [fetchRankingData]);

    // Separate effect to handle user changes
    useEffect(() => {
        if (user && user._id) {
            console.log('👤 User changed/available, fetching ranking data for:', user._id);
            fetchRankingData();
        }
    }, [user, fetchRankingData]);

    // Auto-refresh functionality
    useEffect(() => {
        if (autoRefresh) {
            refreshIntervalRef.current = setInterval(() => {
                fetchRankingData(false); // Silent refresh
            }, 30000); // Refresh every 30 seconds
        } else {
            if (refreshIntervalRef.current) {
                clearInterval(refreshIntervalRef.current);
                refreshIntervalRef.current = null;
            }
        }

        // Cleanup on unmount
        return () => {
            if (refreshIntervalRef.current) {
                clearInterval(refreshIntervalRef.current);
            }
        };
    }, [autoRefresh, fetchRankingData]);

    // Find Me functionality
    const handleFindMe = () => {
        if (currentUserRef.current) {
            currentUserRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
            setShowFindMe(true);
            setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds
        }
    };

    const handleRefresh = () => {
        console.log('🔄 Manual refresh triggered');
        fetchRankingData(true);
    };

    const handleForceRefresh = () => {
        console.log('🔄 Force refresh triggered - clearing cache');
        // Clear any cached data
        setRankingData([]);
        setError(null);
        setLoading(true);

        // Force a fresh API call
        fetchRankingData(true);
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
                <div className="text-center bg-white rounded-xl p-8 shadow-lg animate-fadeInUp">
                    <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4 animate-spin" />
                    <p className="text-gray-600 font-medium">Loading rankings...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
                <div className="text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full animate-fadeInUp">
                    <TbAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Rankings</h2>
                    <p className="text-gray-600 mb-6">{error}</p>
                    <button
                        onClick={handleRefresh}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 mx-auto hover:scale-105 active:scale-95"
                    >
                        <TbRefresh className="w-5 h-5" />
                        <span>Try Again</span>
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
            {/* Modern Background Pattern */}
            <div className="absolute inset-0 overflow-hidden">
                <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse"></div>
                <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-200 to-pink-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-2000"></div>
                <div className="absolute bottom-0 left-1/2 w-96 h-96 bg-gradient-to-br from-yellow-200 to-orange-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-4000"></div>

                {/* Grid Pattern */}
                <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            </div>

            <div className="relative z-10">
                {/* Modern Header with Back Button */}
                {/* Enhanced Header */}
                <div className="p-6 border-b border-white/20 animate-slideInLeft">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={() => window.history.back()}
                                className="bg-white/80 backdrop-blur-lg hover:bg-white/90 text-gray-700 px-4 py-2 rounded-xl font-semibold transition-all duration-200 flex items-center space-x-2 border border-gray-200 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95"
                            >
                                <TbArrowLeft className="w-5 h-5" />
                                <span>Back</span>
                            </button>

                            {/* Page Title */}
                            <div>
                                <h1 className="text-2xl font-black text-gray-900 gradient-text-blue">
                                    🏆 Student Rankings
                                </h1>
                                <p className="text-sm text-gray-600 font-medium">
                                    Compete with students across all levels
                                </p>
                            </div>
                        </div>

                        <div className="flex items-center space-x-3">
                            {/* Current User Rank Display */}
                            {currentUserRank && (
                                <div className="bg-white/80 backdrop-blur-lg rounded-xl px-4 py-2 border border-gray-200 shadow-lg">
                                    <div className="text-center">
                                        <div className="text-xs text-gray-500 font-medium">Your Rank</div>
                                        <div className="text-lg font-black text-blue-600">#{currentUserRank.rank}</div>
                                    </div>
                                </div>
                            )}

                            {/* Find Me Button */}
                            {currentUserRank && (
                                <button
                                    onClick={handleFindMe}
                                    className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-3 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg hover:scale-105 active:scale-95"
                                >
                                    <TbTarget className="w-5 h-5" />
                                    <span>Find Me</span>
                                </button>
                            )}

                            {/* Refresh Button */}
                            <button
                                onClick={handleRefresh}
                                disabled={refreshing}
                                className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 disabled:cursor-not-allowed"
                            >
                                <TbRefresh className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />
                                <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
                            </button>

                            {/* Debug Button - Temporary */}
                            <button
                                onClick={() => {
                                    console.log('🔍 Debug Info:');
                                    console.log('User:', user);
                                    console.log('Ranking Data:', rankingData);
                                    console.log('Ranking Data Length:', rankingData?.length);
                                    console.log('Error:', error);
                                    console.log('Loading:', loading);
                                    console.log('Token exists:', !!localStorage.getItem('token'));

                                    // Force fetch
                                    fetchRankingData(true);
                                }}
                                className="bg-red-500 hover:bg-red-600 text-white px-3 py-3 rounded-xl font-medium transition-all duration-200"
                                title="Debug & Force Fetch"
                            >
                                🔧
                            </button>


                        </div>
                    </div>
                </div>

                {/* Amazing Header */}
                <div className="text-center mb-8 px-4 animate-fadeInUp">
                    <div className="flex items-center justify-center gap-4 mb-6">
                        <div className="relative animate-wiggle">
                            <TbTrophy className="text-6xl text-yellow-400 drop-shadow-lg" />
                            <div className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-pulse" />
                        </div>
                        <div>
                            <h1 className="text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2">
                                🏆 LEADERBOARD
                            </h1>
                            <div className="flex items-center justify-center gap-2 text-white/80">
                                <TbStar className="text-yellow-400" />
                                <span className="text-lg font-medium">Battle for Glory</span>
                                <TbStar className="text-yellow-400" />
                            </div>
                        </div>
                    </div>

                    {/* Stats Bar */}
                    <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto animate-scaleIn">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                            <div className="flex items-center justify-center gap-2">
                                <TbFlame className="text-orange-400 text-xl" />
                                <span className="text-white font-semibold">
                                    {currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'}
                                </span>
                            </div>
                            <div className="flex items-center justify-center gap-2">
                                <TbAward className="text-purple-400 text-xl" />
                                <span className="text-white font-semibold">
                                    {rankingData.length} Competitors
                                </span>
                            </div>
                            <div className="flex items-center justify-center gap-2">
                                <TbMedal className="text-yellow-400 text-xl" />
                                <span className="text-white font-semibold">
                                    Live Rankings
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
                {rankingData.length === 0 ? (
                    <div className="text-center py-16 bg-white/80 backdrop-blur-lg rounded-2xl border border-white/50 shadow-2xl animate-fadeInUp">
                        <div className="animate-wiggle">
                            <TbMedal className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
                        </div>
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">No Rankings Available</h3>
                        <p className="text-gray-600 mb-6 text-lg">Complete some quizzes to join the leaderboard!</p>
                        <button
                            onClick={handleRefresh}
                            disabled={refreshing}
                            className={`bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg hover:scale-105 active:scale-95 ${refreshing ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                            <TbRefresh className={`w-5 h-5 inline mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                            {refreshing ? 'Refreshing...' : 'Refresh Rankings'}
                        </button>
                    </div>
                ) : (
                    <div className="bg-white/80 backdrop-blur-xl rounded-2xl border border-white/50 p-8 shadow-2xl animate-fadeInUp">
                        {/* Debug Info */}
                        <div className="mb-4 p-4 bg-blue-100 rounded-lg">
                            <h3 className="font-bold text-blue-800">Debug Info:</h3>
                            <p className="text-blue-700">Ranking Data Length: {rankingData.length}</p>
                            <p className="text-blue-700">Loading: {loading.toString()}</p>
                            <p className="text-blue-700">Error: {error || 'None'}</p>
                            <p className="text-blue-700">User ID: {user?._id || 'None'}</p>
                            {rankingData.length > 0 && (
                                <div className="mt-2">
                                    <p className="text-blue-700">Sample User: {rankingData[0]?.name || 'No name'}</p>
                                    <p className="text-blue-700">Sample XP: {rankingData[0]?.totalXP || 'No XP'}</p>
                                </div>
                            )}
                        </div>

                        {/* Simple List for Testing */}
                        {rankingData.length > 0 && (
                            <div className="mb-6">
                                <h3 className="text-lg font-bold mb-4">Simple User List (Testing):</h3>
                                {rankingData.slice(0, 5).map((user, index) => (
                                    <div key={user._id || index} className="p-2 border-b border-gray-200">
                                        <span className="font-medium">#{index + 1} {user.name || 'Unknown'}</span>
                                        <span className="ml-4 text-blue-600">{user.totalXP || 0} XP</span>
                                        <span className="ml-4 text-gray-500">Class {user.class || 'Unknown'}</span>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* Achievement Banner for Top Users */}
                        {currentUserRank && currentUserRank.rank <= 10 && (
                            <div className="mb-6 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-xl p-4 text-white animate-bounce">
                                <div className="flex items-center justify-center space-x-2">
                                    <TbTrophy className="w-6 h-6" />
                                    <span className="font-bold text-lg">
                                        🎉 Congratulations! You're in the Top 10! 🎉
                                    </span>
                                    <TbTrophy className="w-6 h-6" />
                                </div>
                            </div>
                        )}
                        <UserRankingList
                            users={rankingData}
                            currentUserId={user?._id || null}
                            layout="horizontal"
                            size="medium"
                            showStats={true}
                            className="space-y-4"
                            currentUserRef={currentUserRef}
                            showFindMe={showFindMe}
                            lastUpdated={lastUpdated}
                            autoRefresh={autoRefresh}
                            onAutoRefreshToggle={() => setAutoRefresh(!autoRefresh)}
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default Ranking;