import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { message } from 'antd';
import Confetti from 'react-confetti';
import useWindowSize from 'react-use/lib/useWindowSize';
import {
  TbArrowLeft,
  TbCheck,
  TbX,
  TbTrophy,
  TbBrain,
  TbTarget,
  TbRefresh,
  TbEye,
  TbBulb
} from 'react-icons/tb';
import { getExamById } from '../../../apicalls/exams';
import { chatWithChatGPTToExplainAns } from '../../../apicalls/chat';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import ContentRenderer from '../../../components/ContentRenderer';
import XPResultDisplay from '../../../components/modern/XPResultDisplay';
import './responsive.css';

const QuizResult = () => {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [explanations, setExplanations] = useState({});
  const [showReview, setShowReview] = useState(false);
  
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { width, height } = useWindowSize();
  
  const result = location.state?.result;

  useEffect(() => {
    const fetchExamData = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getExamById({ examId: id });
        dispatch(HideLoading());
        
        if (response.success) {
          setExamData(response.data);
          setQuestions(response.data?.questions || []);
        } else {
          message.error(response.message);
          navigate('/user/quiz');
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
        navigate('/user/quiz');
      }
    };

    if (id) {
      fetchExamData();
    }
  }, [id, dispatch, navigate]);

  useEffect(() => {
    if (result) {
      // Sound playing functionality can be added here if needed
      console.log(`Quiz ${result.verdict === "Pass" ? "passed" : "failed"}!`);
    }
  }, [result]);

  // Add quiz-fullscreen class for fullscreen experience
  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');

    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {
    try {
      dispatch(ShowLoading());
      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });
      dispatch(HideLoading());

      if (response.success) {
        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));
      } else {
        message.error(response.error || "Failed to fetch explanation.");
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  if (!result || !examData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  if (showReview) {
    return (
      <div className="min-h-screen bg-gray-50 py-4 sm:py-8">
        <div className="max-w-4xl mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 rounded-2xl p-6 sm:p-8 shadow-2xl border border-blue-200">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
                <TbEye className="w-8 h-8 text-white" />
              </div>
              <h2 className="text-2xl sm:text-3xl font-black text-white mb-3 drop-shadow-lg">
                Review Your Answers
              </h2>
              <p className="text-white/90 text-base sm:text-lg font-medium drop-shadow-md">
                Detailed breakdown of your quiz performance
              </p>
            </div>
          </div>

          {/* Questions Review */}
          <div className="space-y-4 sm:space-y-6 mb-6 sm:mb-8">
            {questions.map((question, index) => {
              const userAnswer = result.correctAnswers.find(q => q._id === question._id)?.userAnswer ||
                                result.wrongAnswers.find(q => q._id === question._id)?.userAnswer || "";
              const isCorrect = result.correctAnswers.some(q => q._id === question._id);

              return (
                <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                  {/* Question Header */}
                  <div className={`px-4 sm:px-6 py-4 border-b-2 ${
                    isCorrect
                      ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-300'
                      : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-300'
                  }`}>
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg sm:text-xl font-bold text-gray-900 flex items-center">
                        <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full mr-3 ${
                          isCorrect ? 'bg-green-100' : 'bg-red-100'
                        }`}>
                          {isCorrect ? (
                            <TbCheck className="w-5 h-5 text-green-600" />
                          ) : (
                            <TbX className="w-5 h-5 text-red-600" />
                          )}
                        </span>
                        Question {index + 1}
                      </h3>
                      <span className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold shadow-lg border-2 ${
                        isCorrect
                          ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white border-green-300'
                          : 'bg-gradient-to-r from-red-500 to-pink-500 text-white border-red-300'
                      }`}>
                        {isCorrect ? (
                          <>
                            <TbCheck className="w-4 h-4 mr-1" />
                            Correct
                          </>
                        ) : (
                          <>
                            <TbX className="w-4 h-4 mr-1" />
                            Incorrect
                          </>
                        )}
                      </span>
                    </div>
                  </div>

                  {/* Question Content */}
                  <div className="p-4 sm:p-6">
                    <h4 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">{question.name}</h4>

                    {(question.image || question.imageUrl) && (
                      <div className="mb-3 sm:mb-4">
                        <img
                          src={question.image || question.imageUrl}
                          alt="Question"
                          className="max-w-full h-auto rounded-lg border border-gray-200 mx-auto max-h-[300px]"
                        />
                      </div>
                    )}

                    {/* Answer Comparison */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div className={`p-4 sm:p-6 rounded-2xl border-2 shadow-lg ${
                        isCorrect
                          ? 'border-green-300 bg-gradient-to-br from-green-50 to-emerald-50'
                          : 'border-red-300 bg-gradient-to-br from-red-50 to-pink-50'
                      }`}>
                        <div className="flex items-center mb-3">
                          {isCorrect ? (
                            <TbCheck className="w-5 h-5 text-green-600 mr-2" />
                          ) : (
                            <TbX className="w-5 h-5 text-red-600 mr-2" />
                          )}
                          <h5 className="font-bold text-gray-800 text-base">Your Answer</h5>
                        </div>
                        <p className={`text-base font-semibold px-3 py-2 rounded-lg ${
                          isCorrect
                            ? 'text-green-800 bg-green-100 border border-green-200'
                            : 'text-red-800 bg-red-100 border border-red-200'
                        }`}>
                          {question.type === "mcq" || question.answerType === "Options"
                            ? question.options?.[userAnswer] || "Not answered"
                            : userAnswer || "Not answered"}
                        </p>
                      </div>

                      <div className="p-4 sm:p-6 rounded-2xl border-2 border-green-300 bg-gradient-to-br from-green-50 to-emerald-50 shadow-lg">
                        <div className="flex items-center mb-3">
                          <TbCheck className="w-5 h-5 text-green-600 mr-2" />
                          <h5 className="font-bold text-gray-800 text-base">Correct Answer</h5>
                        </div>
                        <p className="text-green-800 text-base font-semibold bg-green-100 px-3 py-2 rounded-lg border border-green-200">
                          {question.type === "mcq" || question.answerType === "Options"
                            ? question.options?.[question.correctOption || question.correctAnswer]
                            : (question.correctAnswer || question.correctOption)}
                        </p>
                      </div>
                    </div>

                    {/* Explanation Section */}
                    {!isCorrect && (
                      <div className="mt-6 p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 shadow-lg">
                        <button
                          className="group flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg text-sm sm:text-base"
                          onClick={() => fetchExplanation(
                            question.name,
                            question.type === "mcq" || question.answerType === "Options"
                              ? question.options?.[question.correctOption || question.correctAnswer]
                              : (question.correctAnswer || question.correctOption),
                            question.type === "mcq" || question.answerType === "Options"
                              ? question.options?.[userAnswer] || "Not answered"
                              : userAnswer || "Not answered",
                            question.image || question.imageUrl
                          )}
                        >
                          <TbBulb className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                          Get AI Explanation
                        </button>

                        {explanations[question.name] && (
                          <div className="mt-4 p-4 sm:p-6 bg-white rounded-xl border-2 border-blue-200 shadow-md">
                            <div className="flex items-center mb-3">
                              <TbBulb className="w-5 h-5 text-blue-600 mr-2" />
                              <h6 className="font-bold text-blue-800 text-base">AI Explanation</h6>
                            </div>
                            <div className="text-sm sm:text-base text-gray-700 leading-relaxed">
                              <ContentRenderer text={explanations[question.name]} />
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Back Buttons */}
          <div className="text-center space-y-4 sm:space-y-0 sm:space-x-6 sm:flex sm:justify-center">
            <button
              className="group flex items-center justify-center w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-gray-600 to-slate-600 text-white rounded-2xl font-bold hover:from-gray-700 hover:to-slate-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base"
              onClick={() => setShowReview(false)}
            >
              <TbArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
              Back to Results
            </button>
            <button
              className="group flex items-center justify-center w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base"
              onClick={() => navigate('/user/hub')}
            >
              <TbTrophy className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
              Hub
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-4 sm:py-8">
      {result.verdict === "Pass" && <Confetti width={width} height={height} />}

      {/* Back to Quiz Tab - Top Left */}
      <div className="absolute top-4 left-4 z-10">
        <button
          onClick={() => navigate("/quiz")}
          className="group flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl font-bold transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 shadow-lg hover:shadow-2xl hover:shadow-blue-500/25 border border-blue-400/20 relative overflow-hidden"
        >
          <TbArrowLeft className="w-4 h-4 group-hover:-translate-x-1 transition-transform duration-300" />
          <span className="text-sm tracking-wide">Take Quiz</span>
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
        </button>
      </div>

      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          {/* Header */}
          <div className={`px-4 sm:px-6 py-8 sm:py-12 text-center relative overflow-hidden ${
            result.verdict === "Pass"
              ? "bg-gradient-to-br from-green-50 via-emerald-50 to-green-100"
              : "bg-gradient-to-br from-red-50 via-pink-50 to-red-100"
          }`}>
            {/* Background decoration */}
            <div className="absolute inset-0 overflow-hidden">
              <div className={`absolute -top-20 -right-20 w-40 h-40 rounded-full blur-3xl opacity-20 ${
                result.verdict === "Pass" ? "bg-green-400" : "bg-red-400"
              }`}></div>
              <div className={`absolute -bottom-20 -left-20 w-40 h-40 rounded-full blur-3xl opacity-20 ${
                result.verdict === "Pass" ? "bg-emerald-400" : "bg-pink-400"
              }`}></div>
            </div>

            <div className="relative z-10">
              <div className={`inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 rounded-full mb-6 shadow-2xl ${
                result.verdict === "Pass"
                  ? "bg-gradient-to-br from-green-400 to-emerald-500"
                  : "bg-gradient-to-br from-red-400 to-pink-500"
              }`}>
                {result.verdict === "Pass" ? (
                  <TbCheck className="w-12 h-12 sm:w-16 sm:h-16 text-white" />
                ) : (
                  <TbX className="w-12 h-12 sm:w-16 sm:h-16 text-white" />
                )}
              </div>

              <h1 className={`text-3xl sm:text-4xl md:text-5xl font-black mb-4 ${
                result.verdict === "Pass" ? "text-green-800" : "text-red-800"
              }`}>
                {result.verdict === "Pass" ? "🎉 Congratulations!" : "💪 Better Luck Next Time!"}
              </h1>

              <p className={`text-lg sm:text-xl font-semibold px-2 ${
                result.verdict === "Pass" ? "text-green-700" : "text-red-700"
              }`}>
                {result.verdict === "Pass"
                  ? "You've successfully passed the quiz! Amazing work! 🌟"
                  : "Keep practicing and try again! You've got this! 🚀"}
              </p>
            </div>
          </div>

          {/* Statistics */}
          <div className="p-4 sm:p-6 md:p-8">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8">
              <div className="text-center p-4 sm:p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border-2 border-green-200 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mb-3">
                  <TbCheck className="w-6 h-6 text-green-600" />
                </div>
                <div className="text-3xl sm:text-4xl font-black text-green-700 mb-1">
                  {result.correctAnswers?.length || 0}
                </div>
                <div className="text-sm font-bold text-green-600">Correct Answers</div>
              </div>

              <div className="text-center p-4 sm:p-6 bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl border-2 border-red-200 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mb-3">
                  <TbX className="w-6 h-6 text-red-600" />
                </div>
                <div className="text-3xl sm:text-4xl font-black text-red-700 mb-1">
                  {result.wrongAnswers?.length || 0}
                </div>
                <div className="text-sm font-bold text-red-600">Wrong Answers</div>
              </div>

              <div className="text-center p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border-2 border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mb-3">
                  <TbBrain className="w-6 h-6 text-blue-600" />
                </div>
                <div className="text-3xl sm:text-4xl font-black text-blue-700 mb-1">
                  {questions.length}
                </div>
                <div className="text-sm font-bold text-blue-600">Total Questions</div>
              </div>

              <div className="text-center p-4 sm:p-6 bg-gradient-to-br from-purple-50 to-violet-50 rounded-2xl border-2 border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mb-3">
                  <TbTarget className="w-6 h-6 text-purple-600" />
                </div>
                <div className="text-3xl sm:text-4xl font-black text-purple-700 mb-1">
                  {examData.passingMarks}%
                </div>
                <div className="text-sm font-bold text-purple-600">Pass Mark</div>
              </div>
            </div>

            {/* Score Percentage */}
            <div className="mb-8">
              <div className="text-center mb-6">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full mb-4 shadow-lg">
                  <TbTarget className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl sm:text-3xl font-black text-gray-800 mb-2">Your Final Score</h3>
                <p className="text-gray-600 font-medium">See how well you performed</p>
              </div>

              <div className="relative">
                <div className="w-full bg-gray-300 rounded-full h-6 sm:h-8 overflow-hidden shadow-inner">
                  <div
                    className={`h-full rounded-full transition-all duration-1000 ease-out relative ${
                      result.verdict === "Pass"
                        ? "bg-gradient-to-r from-green-500 via-emerald-500 to-green-600"
                        : "bg-gradient-to-r from-red-500 via-pink-500 to-red-600"
                    }`}
                    style={{
                      width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%`
                    }}
                  >
                    <div className="absolute inset-0 bg-white bg-opacity-30 rounded-full"></div>
                  </div>
                </div>

                <div className="text-center mt-4">
                  <span className={`text-4xl sm:text-5xl font-black ${
                    result.verdict === "Pass" ? "text-green-700" : "text-red-700"
                  }`}>
                    {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%
                  </span>
                  <p className="text-gray-600 font-semibold mt-2">
                    {result.correctAnswers?.length || 0} out of {questions.length} questions correct
                  </p>
                </div>
              </div>
            </div>

            {/* XP Display */}
            {result.xpData && (
              <div className="mb-8">
                <XPResultDisplay xpData={result.xpData} />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                className="group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-bold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base"
                onClick={() => setShowReview(true)}
              >
                <TbEye className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                Review Answers
              </button>

              <button
                className="group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-2xl font-bold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base"
                onClick={() => navigate(`/quiz/${id}/play`)}
              >
                <TbRefresh className="w-5 h-5 mr-2 group-hover:rotate-180 transition-transform duration-300" />
                Retake Quiz
              </button>

              <button
                className="group flex items-center justify-center px-8 py-4 bg-gradient-to-r from-purple-600 to-violet-600 text-white rounded-2xl font-bold hover:from-purple-700 hover:to-violet-700 transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl text-sm sm:text-base"
                onClick={() => navigate('/quiz')}
              >
                <TbBrain className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                Take Quiz Page
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Back Navigation Arrow */}
      <div className="fixed bottom-4 left-4 z-50">
        <button
          onClick={() => navigate('/quiz')}
          className="flex items-center justify-center w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg transition-all duration-200 hover:scale-105"
          title="Back to Quiz Page"
        >
          <TbArrowLeft className="w-6 h-6" />
        </button>
      </div>
    </div>
  );
};

export default QuizResult;
