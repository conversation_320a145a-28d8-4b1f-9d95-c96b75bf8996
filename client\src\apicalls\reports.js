const { default: axiosInstance } = require(".");

// add report
export const addReport = async (payload) => {
    try {
        const response = await axiosInstance.post("/api/reports/add-report", payload);
        return response.data;
    } catch (error) {
        return error.response.data;
    }
}

// get all reports
export const getAllReports = async (filters) => {
    try {
        const response = await axiosInstance.post("/api/reports/get-all-reports", filters);
        return response.data;
    } catch (error) {
        return error.response.data;
    }
}

// get all reports by user
export const getAllReportsByUser = async (payload) => {
    try {
        const response = await axiosInstance.post("/api/reports/get-all-reports-by-user", payload);
        return response.data;
    } catch (error) {
        return error.response.data;
    }
}

// get all reports for ranking
export const getAllReportsForRanking = async (filters) => {
    try {
        const response = await axiosInstance.get("/api/reports/get-all-reports-for-ranking");
        return response.data;
    } catch (error) {
        return error.response.data;
    }
} 
