const mongoose = require("mongoose");
const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    school: {
      type: String,
      required: true,
    },
    level: {
      type: String,
      enum: ["primary", "secondary", "advance", "Primary", "Secondary", "Advance"],
      default: "Primary",
      required: false,
    },
    class: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
      unique: true,
    },
    phoneNumber: {
      type: String,
      required: true,
      unique: true,
    },
    paymentRequired: {
      type: Boolean,
      required: false,
      default: false,
    },
    // Enhanced subscription tracking
    subscriptionStatus: {
      type: String,
      enum: ["free", "premium", "active", "expired"],
      default: "free",
    },
    subscriptionStartDate: {
      type: Date,
    },
    subscriptionEndDate: {
      type: Date,
    },
    subscriptionPlan: {
      type: String,
      enum: ["basic", "premium", "pro"],
    },
    profileImage: {
      type: String,
    },
    // User statistics for ranking
    totalQuizzesTaken: {
      type: Number,
      default: 0,
    },
    totalPointsEarned: {
      type: Number,
      default: 0,
    },
    averageScore: {
      type: Number,
      default: 0,
    },
    bestStreak: {
      type: Number,
      default: 0,
    },
    currentStreak: {
      type: Number,
      default: 0,
    },

    // New XP System Fields
    totalXP: {
      type: Number,
      default: 0,
    },
    currentLevel: {
      type: Number,
      default: 1,
    },
    xpToNextLevel: {
      type: Number,
      default: 100,
    },
    lifetimeXP: {
      type: Number,
      default: 0,
    },
    seasonXP: {
      type: Number,
      default: 0,
    },
    currentSeason: {
      type: String,
      default: "2024-S1",
    },

    // Enhanced Achievement System
    achievements: [{
      id: {
        type: String,
        required: true,
      },
      name: {
        type: String,
        required: true,
      },
      description: String,
      icon: String,
      xpReward: {
        type: Number,
        default: 0,
      },
      rarity: {
        type: String,
        enum: ["common", "rare", "epic", "legendary"],
        default: "common",
      },
      category: {
        type: String,
        enum: ["learning", "streak", "subject", "social", "special"],
        default: "learning",
      },
      earnedAt: {
        type: Date,
        default: Date.now,
      },
      subject: String, // For subject-specific achievements
      metadata: Object, // Additional achievement data
    }],

    // Level Progress Tracking
    levelHistory: [{
      level: Number,
      reachedAt: {
        type: Date,
        default: Date.now,
      },
      xpAtLevel: Number,
    }],

    // XP Statistics
    xpStats: {
      dailyXP: {
        type: Number,
        default: 0,
      },
      weeklyXP: {
        type: Number,
        default: 0,
      },
      monthlyXP: {
        type: Number,
        default: 0,
      },
      lastXPGain: {
        type: Date,
        default: Date.now,
      },
      averageXPPerQuiz: {
        type: Number,
        default: 0,
      },
      bestXPGain: {
        type: Number,
        default: 0,
      },
    },
    password: {
      type: String,
      required: true,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    isBlocked: {
      type: Boolean,
      default: false,
    }
  },
  {
    timestamps: true,
  }
);

const userModel = mongoose.model("users", userSchema);

module.exports = userModel;