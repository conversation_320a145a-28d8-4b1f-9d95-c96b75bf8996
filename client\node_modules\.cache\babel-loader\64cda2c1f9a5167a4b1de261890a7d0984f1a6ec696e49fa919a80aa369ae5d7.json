{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\WriteExam\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { getExamById } from \"../../../apicalls/exams\";\nimport { addReport } from \"../../../apicalls/reports\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport Instructions from \"./Instructions\";\nimport Pass from \"../../../assets/pass.gif\";\nimport Fail from \"../../../assets/fail.gif\";\nimport Confetti from \"react-confetti\";\nimport useWindowSize from \"react-use/lib/useWindowSize\";\nimport PassSound from \"../../../assets/pass.mp3\";\nimport { QuizQuestion, QuizTimer, QuizTimerOverlay, Card, Button, Loading } from \"../../../components/modern\";\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\nimport { TbClock, TbQuestionMark, TbCheck, TbX, TbFlag, TbArrowLeft, TbArrowRight, TbBrain } from \"react-icons/tb\";\nimport FailSound from \"../../../assets/fail.mp3\";\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\nimport ContentRenderer from \"../../../components/ContentRenderer\";\nimport QuizRenderer from \"../../../components/QuizRenderer\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction WriteExam() {\n  _s();\n  var _questions$selectedQu, _questions$selectedQu2, _questions$selectedQu3, _questions$selectedQu4, _result$correctAnswer, _result$correctAnswer2, _result$correctAnswer3, _result$correctAnswer4;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [result, setResult] = useState({});\n  const params = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [view, setView] = useState(\"instructions\");\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [startTime, setStartTime] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const [explanations, setExplanations] = useState({});\n  const getExamData = useCallback(async () => {\n    try {\n      setIsLoading(true);\n      dispatch(ShowLoading());\n      console.log(\"Fetching exam data for ID:\", params.id);\n      const response = await getExamById({\n        examId: params.id\n      });\n      console.log(\"Exam API Response:\", response);\n      dispatch(HideLoading());\n      setIsLoading(false);\n      if (response.success) {\n        const examData = response.data;\n\n        // Check different possible question locations\n        let questions = [];\n        if (examData !== null && examData !== void 0 && examData.questions && Array.isArray(examData.questions)) {\n          questions = examData.questions;\n        } else if (examData !== null && examData !== void 0 && examData.question && Array.isArray(examData.question)) {\n          questions = examData.question;\n        } else if (examData && Array.isArray(examData)) {\n          questions = examData;\n        }\n        console.log(\"Exam Data:\", examData);\n        console.log(\"Questions found:\", questions.length);\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\n        setQuestions(questions);\n        setExamData(examData);\n        setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n        if (questions.length === 0) {\n          console.warn(\"No questions found in exam data\");\n          console.log(\"Full response for debugging:\", response);\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\n        }\n      } else {\n        console.error(\"API Error:\", response.message);\n        console.log(\"Full error response:\", response);\n        message.error(response.message || \"Failed to load exam data\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      setIsLoading(false);\n      console.error(\"Exception in getExamData:\", error);\n      message.error(error.message || \"Failed to load exam. Please try again.\");\n    }\n  }, [params.id, dispatch]);\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      // Check if user is available\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      const indexMap = [];\n      questions.forEach((q, idx) => {\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          indexMap.push(idx);\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      const wrongPayload = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: q.correctAnswer || q.correctOption,\n              userAnswer: userAnswerKey\n            });\n          }\n        } else if (q.answerType === \"Options\") {\n          const correctKey = q.correctOption;\n          const correctValue = q.options && q.options[correctKey] || correctKey;\n          const userValue = q.options && q.options[userAnswerKey] || userAnswerKey || \"\";\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: correctValue,\n              userAnswer: userValue\n            });\n          }\n        }\n      });\n\n      // Calculate time spent\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60; // Convert minutes to seconds\n\n      // Calculate score and points\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round(correctCount / totalQuestions * 100);\n      const points = correctCount * 10; // 10 points per correct answer\n\n      // Determine pass/fail based on percentage\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers: correctAnswers || [],\n        wrongAnswers: wrongAnswers || [],\n        verdict: verdict || \"Fail\",\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n      setResult(tempResult);\n      const response = await addReport({\n        exam: params.id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        // Include XP data in the result\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n        setResult(resultWithXP);\n        setView(\"result\");\n        window.scrollTo(0, 0);\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const startTimer = () => {\n    const totalSeconds = (examData === null || examData === void 0 ? void 0 : examData.duration) || 0;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now()); // Record start time for XP calculation\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  };\n  useEffect(() => {\n    if (timeUp && view === \"questions\") {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, view, intervalId, calculateResult]);\n  useEffect(() => {\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\n    if (params.id) {\n      getExamData();\n    } else {\n      console.error(\"No exam ID provided in URL parameters\");\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\n      navigate('/user/quiz');\n    }\n  }, [params.id, getExamData, navigate]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n\n  // Add fullscreen class for all quiz views (instructions, questions, results)\n  useEffect(() => {\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\n      document.body.classList.add(\"quiz-fullscreen\");\n    } else {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    };\n  }, [view]);\n\n  // Repair function for fixing orphaned questions\n  const repairExamQuestions = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await fetch('/api/exams/repair-exam-questions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          examId: params.id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        message.success(data.message);\n        // Reload the exam data\n        getExamData();\n      } else {\n        message.error(data.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to repair exam questions\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Check if user is authenticated\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-10 h-10 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-8\",\n          children: \"Please log in to access the exam and start your learning journey.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n          onClick: () => navigate(\"/login\"),\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this);\n  }\n  return examData ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [view === \"instructions\" && /*#__PURE__*/_jsxDEV(Instructions, {\n      examData: examData,\n      setView: setView,\n      startTimer: startTimer,\n      questions: questions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 9\n    }, this), view === \"questions\" && (isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white animate-spin\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              className: \"opacity-25\",\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\",\n              stroke: \"currentColor\",\n              strokeWidth: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              className: \"opacity-75\",\n              fill: \"currentColor\",\n              d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-blue-800 mb-4\",\n          children: \"Loading Quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-600 text-lg\",\n          children: \"Please wait while we prepare your questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 11\n    }, this) : questions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-amber-800 mb-4\",\n          children: \"No Questions Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-amber-700 mb-6 text-lg leading-relaxed\",\n          children: \"This exam appears to have no questions. This could be due to:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-left text-amber-700 mb-8 space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Questions not properly linked to this exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Database connection issues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Exam configuration problems\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: repairExamQuestions,\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD27 Repair Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              console.log(\"Retrying exam data fetch...\");\n              getExamData();\n            },\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD04 Retry Loading\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/user/quiz'),\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\u2190 Back to Quiz List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 389,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"container-modern\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between h-16\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-8 h-8 text-primary-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-lg font-semibold text-gray-900\",\n                  children: (examData === null || examData === void 0 ? void 0 : examData.name) || \"Quiz\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(QuizTimer, {\n              duration: (examData === null || examData === void 0 ? void 0 : examData.duration) || 0,\n              onTimeUp: () => {\n                setTimeUp(true);\n                calculateResult();\n              },\n              isActive: !timeUp,\n              showWarning: true,\n              warningThreshold: 300\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 433,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container-modern py-8\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: 0.2\n          },\n          children: questions[selectedQuestionIndex] && typeof questions[selectedQuestionIndex] === 'object' ? /*#__PURE__*/_jsxDEV(QuizQuestion, {\n            question: {\n              // Only pass safe string properties to avoid object rendering\n              _id: String(questions[selectedQuestionIndex]._id || ''),\n              name: String(questions[selectedQuestionIndex].name || ''),\n              answerType: String(questions[selectedQuestionIndex].answerType || ''),\n              correctOption: String(questions[selectedQuestionIndex].correctOption || ''),\n              correctAnswer: String(questions[selectedQuestionIndex].correctAnswer || ''),\n              image: questions[selectedQuestionIndex].image,\n              imageUrl: questions[selectedQuestionIndex].imageUrl,\n              type: ((_questions$selectedQu = questions[selectedQuestionIndex]) === null || _questions$selectedQu === void 0 ? void 0 : _questions$selectedQu.answerType) === \"Options\" ? \"mcq\" : ((_questions$selectedQu2 = questions[selectedQuestionIndex]) === null || _questions$selectedQu2 === void 0 ? void 0 : _questions$selectedQu2.answerType) === \"Free Text\" || ((_questions$selectedQu3 = questions[selectedQuestionIndex]) === null || _questions$selectedQu3 === void 0 ? void 0 : _questions$selectedQu3.answerType) === \"Fill in the Blank\" ? \"fill\" : (_questions$selectedQu4 = questions[selectedQuestionIndex]) !== null && _questions$selectedQu4 !== void 0 && _questions$selectedQu4.imageUrl ? \"image\" : \"mcq\",\n              options: (() => {\n                try {\n                  var _questions$selectedQu5;\n                  if (!((_questions$selectedQu5 = questions[selectedQuestionIndex]) !== null && _questions$selectedQu5 !== void 0 && _questions$selectedQu5.options)) return [];\n                  const opts = questions[selectedQuestionIndex].options;\n                  if (Array.isArray(opts)) {\n                    return opts.filter(option => option && typeof option === 'string').map(option => String(option).trim()).filter(option => option.length > 0);\n                  } else if (typeof opts === 'object' && opts !== null) {\n                    return Object.values(opts).filter(option => option && typeof option === 'string').map(option => String(option).trim()).filter(option => option.length > 0);\n                  }\n                  return [];\n                } catch (error) {\n                  console.error('Error processing options:', error);\n                  return [];\n                }\n              })()\n            },\n            questionNumber: selectedQuestionIndex + 1,\n            totalQuestions: questions.length,\n            selectedAnswer: selectedOptions[selectedQuestionIndex],\n            onAnswerSelect: answer => setSelectedOptions({\n              ...selectedOptions,\n              [selectedQuestionIndex]: answer\n            }),\n            onNext: () => {\n              if (selectedQuestionIndex === questions.length - 1) {\n                calculateResult();\n              } else {\n                setSelectedQuestionIndex(selectedQuestionIndex + 1);\n              }\n            },\n            onPrevious: () => {\n              if (selectedQuestionIndex > 0) {\n                setSelectedQuestionIndex(selectedQuestionIndex - 1);\n              }\n            },\n            timeRemaining: secondsLeft,\n            isLastQuestion: selectedQuestionIndex === questions.length - 1\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-8 bg-red-50 rounded-lg border border-red-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"ri-error-warning-line text-3xl text-red-600 mb-4 block\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold text-red-800 mb-2\",\n              children: \"Question Not Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 535,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-600\",\n              children: \"This question could not be loaded. Please try refreshing the page.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.reload(),\n              className: \"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\",\n              children: \"Refresh Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 19\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(QuizTimerOverlay, {\n        timeRemaining: secondsLeft,\n        onClose: () => {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 11\n    }, this)), view === \"result\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\",\n      children: [result.verdict === \"Pass\" && /*#__PURE__*/_jsxDEV(Confetti, {\n        width: width,\n        height: height\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 559,\n        columnNumber: 41\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-8 py-10 text-center relative ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\" : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500 to-green-600\" : \"bg-gradient-to-br from-amber-500 to-orange-600\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: result.verdict === \"Pass\" ? Pass : Fail,\n                  alt: result.verdict,\n                  className: \"w-12 h-12 object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: `text-4xl font-black mb-4 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"}`,\n                children: result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\",\n                children: result.verdict === \"Pass\" ? \"You've mastered this exam with flying colors!\" : \"Every challenge makes you stronger. Try again!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 569,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-blue-600 mb-2 tracking-tight\",\n                    children: [Math.round((((_result$correctAnswer = result.correctAnswers) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.length) || 0) / questions.length * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-blue-700/80 uppercase tracking-wider\",\n                    children: \"Your Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 610,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-emerald-600 mb-2 tracking-tight\",\n                    children: [((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0, \"/\", questions.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\",\n                    children: \"Correct\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 609,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\" : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"} to-transparent`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-4xl font-black mb-2 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                    children: result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-sm font-bold uppercase tracking-wider ${result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"}`,\n                    children: result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 634,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative bg-slate-100 rounded-2xl p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-bold text-slate-700 mb-1\",\n                    children: \"Performance Overview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 647,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-slate-500\",\n                    children: \"Your achievement level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 648,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 646,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${result.verdict === \"Pass\" ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\" : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"}`,\n                      style: {\n                        width: `${(((_result$correctAnswer3 = result.correctAnswers) === null || _result$correctAnswer3 === void 0 ? void 0 : _result$correctAnswer3.length) || 0) / questions.length * 100}%`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 652,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 651,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"0%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 664,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-lg font-black tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                      children: [Math.round((((_result$correctAnswer4 = result.correctAnswers) === null || _result$correctAnswer4 === void 0 ? void 0 : _result$correctAnswer4.length) || 0) / questions.length * 100), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 665,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"100%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 670,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 650,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 644,\n              columnNumber: 17\n            }, this), result.xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(XPResultDisplay, {\n                xpData: result.xpData\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 678,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                onClick: () => setView(\"review\"),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 689,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative\",\n                  children: \"Review Answers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 562,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 9\n    }, this), view === \"review\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2\",\n              children: \"Answer Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-slate-600\",\n              children: \"Quick overview of your answers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3 mb-6\",\n          children: questions && Array.isArray(questions) ? questions.map((question, index) => {\n            // Ensure question is a valid object\n            if (!question || typeof question !== 'object') {\n              return null;\n            }\n            const correctAnswer = question.answerType === \"Options\" ? question.correctOption : question.correctAnswer;\n            const isCorrect = correctAnswer === selectedOptions[index];\n            const userAnswer = selectedOptions[index];\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"backdrop-blur-md rounded-lg shadow-md border-2 p-4\",\n              style: {\n                backgroundColor: isCorrect ? '#bbf7d0' : '#fecaca',\n                borderColor: isCorrect ? '#22c55e' : '#ef4444'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\",\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 740,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-slate-800 font-medium leading-relaxed\",\n                      children: question.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 744,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 743,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-semibold text-slate-600\",\n                  children: \"Your Answer: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 751,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`,\n                  children: (() => {\n                    if (question.answerType === \"Options\") {\n                      if (question.options && userAnswer !== undefined && userAnswer !== null) {\n                        // Handle both object and array options\n                        if (typeof question.options === 'object' && !Array.isArray(question.options)) {\n                          const optionValue = question.options[userAnswer];\n                          return typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\n                        } else if (Array.isArray(question.options)) {\n                          const optionValue = question.options[userAnswer];\n                          return typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\n                        }\n                      }\n                      return String(userAnswer || \"Not answered\");\n                    } else {\n                      return String(userAnswer || \"Not answered\");\n                    }\n                  })()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 23\n                }, this), isCorrect ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-green-600 text-2xl font-black\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-red-600 text-2xl font-black\",\n                  children: \"\\u2717\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 21\n              }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-semibold text-slate-600\",\n                  children: \"Correct Answer: \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 781,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-green-700\",\n                  children: (() => {\n                    if (question.answerType === \"Options\") {\n                      if (question.options && question.correctOption !== undefined && question.correctOption !== null) {\n                        // Handle both object and array options\n                        if (typeof question.options === 'object' && !Array.isArray(question.options)) {\n                          const optionValue = question.options[question.correctOption];\n                          return typeof optionValue === 'string' ? optionValue : String(optionValue || question.correctOption || \"Unknown\");\n                        } else if (Array.isArray(question.options)) {\n                          const optionValue = question.options[question.correctOption];\n                          return typeof optionValue === 'string' ? optionValue : String(optionValue || question.correctOption || \"Unknown\");\n                        }\n                      }\n                      return String(question.correctOption || \"Unknown\");\n                    } else {\n                      return String(question.correctAnswer || question.correctOption || \"Unknown\");\n                    }\n                  })()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 782,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-3 text-green-500 text-2xl font-black\",\n                  children: \"\\u2713\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 801,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 780,\n                columnNumber: 23\n              }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\",\n                  onClick: () => {\n                    console.log('Fetching explanation for:', question.name);\n\n                    // Safely get expected answer\n                    let expectedAnswer = \"\";\n                    if (question.answerType === \"Options\") {\n                      if (question.options && question.correctOption !== undefined) {\n                        const optionValue = question.options[question.correctOption];\n                        expectedAnswer = typeof optionValue === 'string' ? optionValue : String(optionValue || question.correctOption || \"\");\n                      } else {\n                        expectedAnswer = String(question.correctOption || \"\");\n                      }\n                    } else {\n                      expectedAnswer = String(question.correctAnswer || question.correctOption || \"\");\n                    }\n\n                    // Safely get user answer\n                    let userAnswerText = \"\";\n                    if (question.answerType === \"Options\") {\n                      if (question.options && userAnswer !== undefined) {\n                        const optionValue = question.options[userAnswer];\n                        userAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\n                      } else {\n                        userAnswerText = String(userAnswer || \"Not answered\");\n                      }\n                    } else {\n                      userAnswerText = String(userAnswer || \"Not answered\");\n                    }\n                    fetchExplanation(String(question.name || \"\"), expectedAnswer, userAnswerText, question.image || question.imageUrl);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"\\uD83D\\uDCA1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 847,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Get Explanation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 848,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 23\n              }, this), explanations[question.name] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-blue-600 text-lg mr-2\",\n                    children: \"\\uD83D\\uDCA1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"font-bold text-gray-800 text-base\",\n                    children: \"Explanation\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 858,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 25\n                }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3 p-2 bg-gray-50 rounded border border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-700 text-sm font-medium\",\n                      children: \"\\uD83D\\uDCCA Reference Diagram:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 867,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 866,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: question.image || question.imageUrl,\n                      alt: \"Question diagram\",\n                      className: \"max-w-full max-h-48 object-contain rounded border border-gray-300\",\n                      style: {\n                        maxWidth: '350px'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 870,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 869,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded\",\n                  children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n                    text: explanations[question.name]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 881,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 855,\n                columnNumber: 23\n              }, this)]\n            }, question._id || index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 19\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center p-8 bg-gray-50 rounded-lg border border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"No questions available for review.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 888,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n            onClick: () => setView(\"result\"),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative\",\n              children: \"\\u2190 Back to Results\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n            onClick: () => {\n              // Reset exam state and restart\n              setView(\"instructions\");\n              setSelectedQuestionIndex(0);\n              setSelectedOptions({});\n              setResult({});\n              setTimeUp(false);\n              setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n              setExplanations({});\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 917,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"relative\",\n              children: \"\\uD83D\\uDD04 Retake Quiz\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 918,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 703,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 702,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 361,\n    columnNumber: 5\n  }, this) : null;\n}\n_s(WriteExam, \"O6w+2GWMw4fZ1y7nA7sammn5z5g=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useWindowSize];\n});\n_c = WriteExam;\nexport default WriteExam;\nvar _c;\n$RefreshReg$(_c, \"WriteExam\");", "map": {"version": 3, "names": ["message", "React", "useCallback", "useEffect", "useState", "useDispatch", "useSelector", "useNavigate", "useParams", "motion", "AnimatePresence", "getExamById", "addReport", "HideLoading", "ShowLoading", "Instructions", "Pass", "Fail", "Confetti", "useWindowSize", "PassSound", "QuizQuestion", "QuizTimer", "QuizTimer<PERSON><PERSON>lay", "Card", "<PERSON><PERSON>", "Loading", "XPResultDisplay", "TbClock", "TbQuestionMark", "TbCheck", "TbX", "TbFlag", "TbArrowLeft", "TbArrowRight", "TbBrain", "FailSound", "chatWithChatGPTToGetAns", "chatWithChatGPTToExplainAns", "Content<PERSON><PERSON><PERSON>", "Quiz<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "WriteExam", "_s", "_questions$selectedQu", "_questions$selectedQu2", "_questions$selectedQu3", "_questions$selectedQu4", "_result$correctAnswer", "_result$correctAnswer2", "_result$correctAnswer3", "_result$correctAnswer4", "examData", "setExamData", "questions", "setQuestions", "selectedQuestionIndex", "setSelectedQuestionIndex", "selectedOptions", "setSelectedOptions", "result", "setResult", "params", "dispatch", "navigate", "view", "<PERSON><PERSON><PERSON><PERSON>", "secondsLeft", "setSecondsLeft", "timeUp", "setTimeUp", "intervalId", "setIntervalId", "isLoading", "setIsLoading", "startTime", "setStartTime", "user", "state", "width", "height", "explanations", "setExplanations", "getExamData", "console", "log", "id", "response", "examId", "success", "data", "Array", "isArray", "question", "length", "Object", "keys", "duration", "warn", "warning", "error", "checkFreeTextAnswers", "payload", "calculateResult", "_id", "freeTextPayload", "indexMap", "for<PERSON>ach", "q", "idx", "answerType", "push", "name", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "userAnswer", "gptResults", "gptMap", "r", "isCorrect", "reason", "correctAnswers", "wrongAnswers", "wrongPayload", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "correctValue", "options", "userValue", "timeSpent", "Math", "floor", "Date", "now", "totalTimeAllowed", "totalQuestions", "correctCount", "scorePercentage", "round", "points", "passingPercentage", "passingMarks", "verdict", "tempResult", "score", "exam", "resultWithXP", "xpData", "window", "scrollTo", "Audio", "play", "fetchExplanation", "imageUrl", "prev", "explanation", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "repairExamQuestions", "fetch", "method", "headers", "localStorage", "getItem", "JSON", "stringify", "json", "className", "children", "fill", "viewBox", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "cx", "cy", "stroke", "strokeWidth", "div", "initial", "opacity", "y", "animate", "onTimeUp", "isActive", "showWarning", "warningThreshold", "transition", "delay", "String", "image", "type", "_questions$selectedQu5", "opts", "filter", "option", "map", "trim", "values", "questionNumber", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerSelect", "answer", "onNext", "onPrevious", "timeRemaining", "isLastQuestion", "location", "reload", "onClose", "src", "alt", "style", "index", "backgroundColor", "borderColor", "undefined", "optionValue", "userAnswerText", "max<PERSON><PERSON><PERSON>", "text", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/WriteExam/index.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport { QuizQuestion, QuizTimer, QuizTimer<PERSON><PERSON><PERSON>, Card, But<PERSON>, Loading } from \"../../../components/modern\";\r\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\r\nimport { TbClock, TbQuestionMark, TbCheck, TbX, TbFlag, TbArrowLeft, TbArrowRight, TbBrain } from \"react-icons/tb\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\r\nimport ContentRenderer from \"../../../components/ContentRenderer\";\r\n\r\nimport QuizRenderer from \"../../../components/QuizRenderer\";\r\n\r\nfunction WriteExam() {\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [startTime, setStartTime] = useState(null);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      dispatch(ShowLoading());\r\n      console.log(\"Fetching exam data for ID:\", params.id);\r\n\r\n      const response = await getExamById({ examId: params.id });\r\n      console.log(\"Exam API Response:\", response);\r\n\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n\r\n      if (response.success) {\r\n        const examData = response.data;\r\n\r\n        // Check different possible question locations\r\n        let questions = [];\r\n        if (examData?.questions && Array.isArray(examData.questions)) {\r\n          questions = examData.questions;\r\n        } else if (examData?.question && Array.isArray(examData.question)) {\r\n          questions = examData.question;\r\n        } else if (examData && Array.isArray(examData)) {\r\n          questions = examData;\r\n        }\r\n\r\n        console.log(\"Exam Data:\", examData);\r\n        console.log(\"Questions found:\", questions.length);\r\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\r\n\r\n        setQuestions(questions);\r\n        setExamData(examData);\r\n        setSecondsLeft(examData?.duration || 0);\r\n\r\n        if (questions.length === 0) {\r\n          console.warn(\"No questions found in exam data\");\r\n          console.log(\"Full response for debugging:\", response);\r\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\r\n        }\r\n      } else {\r\n        console.error(\"API Error:\", response.message);\r\n        console.log(\"Full error response:\", response);\r\n        message.error(response.message || \"Failed to load exam data\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n      console.error(\"Exception in getExamData:\", error);\r\n      message.error(error.message || \"Failed to load exam. Please try again.\");\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = (q.options && q.options[correctKey]) || correctKey;\r\n          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      // Calculate time spent\r\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\r\n      const totalTimeAllowed = (examData?.duration || 0) * 60; // Convert minutes to seconds\r\n\r\n      // Calculate score and points\r\n      const totalQuestions = questions.length;\r\n      const correctCount = correctAnswers.length;\r\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\r\n      const points = correctCount * 10; // 10 points per correct answer\r\n\r\n      // Determine pass/fail based on percentage\r\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\r\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\r\n\r\n      const tempResult = {\r\n        correctAnswers: correctAnswers || [],\r\n        wrongAnswers: wrongAnswers || [],\r\n        verdict: verdict || \"Fail\",\r\n        score: scorePercentage,\r\n        points: points,\r\n        totalQuestions: totalQuestions,\r\n        timeSpent: timeSpent,\r\n        totalTimeAllowed: totalTimeAllowed\r\n      };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        // Include XP data in the result\r\n        const resultWithXP = {\r\n          ...tempResult,\r\n          xpData: response.xpData\r\n        };\r\n        setResult(resultWithXP);\r\n\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0;\r\n    setSecondsLeft(totalSeconds);\r\n    setStartTime(Date.now()); // Record start time for XP calculation\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\r\n    if (params.id) {\r\n      getExamData();\r\n    } else {\r\n      console.error(\"No exam ID provided in URL parameters\");\r\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\r\n      navigate('/user/quiz');\r\n    }\r\n  }, [params.id, getExamData, navigate]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Add fullscreen class for all quiz views (instructions, questions, results)\r\n  useEffect(() => {\r\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\r\n      document.body.classList.add(\"quiz-fullscreen\");\r\n    } else {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    };\r\n  }, [view]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n          questions={questions}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        isLoading ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\">\r\n                <svg className=\"w-12 h-12 text-white animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-blue-800 mb-4\">Loading Quiz...</h3>\r\n              <p className=\"text-blue-600 text-lg\">\r\n                Please wait while we prepare your questions.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-6 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. This could be due to:\r\n              </p>\r\n              <ul className=\"text-left text-amber-700 mb-8 space-y-2\">\r\n                <li>• Questions not properly linked to this exam</li>\r\n                <li>• Database connection issues</li>\r\n                <li>• Exam configuration problems</li>\r\n              </ul>\r\n              <div className=\"space-y-3\">\r\n                <button\r\n                  onClick={repairExamQuestions}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔧 Repair Questions\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    console.log(\"Retrying exam data fetch...\");\r\n                    getExamData();\r\n                  }}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔄 Retry Loading\r\n                </button>\r\n                <button\r\n                  onClick={() => navigate('/user/quiz')}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  ← Back to Quiz List\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50\">\r\n            {/* Modern Quiz Header */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              className=\"bg-white/95 backdrop-blur-md border-b border-gray-100 sticky top-0 z-50\"\r\n            >\r\n              <div className=\"container-modern\">\r\n                <div className=\"flex items-center justify-between h-16\">\r\n                  {/* Quiz Info */}\r\n                  <div className=\"flex items-center space-x-4\">\r\n                    <TbBrain className=\"w-8 h-8 text-primary-600\" />\r\n                    <div>\r\n                      <h1 className=\"text-lg font-semibold text-gray-900\">{examData?.name || \"Quiz\"}</h1>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Timer */}\r\n                  <QuizTimer\r\n                    duration={examData?.duration || 0}\r\n                    onTimeUp={() => {\r\n                      setTimeUp(true);\r\n                      calculateResult();\r\n                    }}\r\n                    isActive={!timeUp}\r\n                    showWarning={true}\r\n                    warningThreshold={300}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Quiz Content */}\r\n            <div className=\"container-modern py-8\">\r\n              <motion.div\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ delay: 0.2 }}\r\n              >\r\n                {questions[selectedQuestionIndex] && typeof questions[selectedQuestionIndex] === 'object' ? (\r\n                  <QuizQuestion\r\n                    question={{\r\n                      // Only pass safe string properties to avoid object rendering\r\n                      _id: String(questions[selectedQuestionIndex]._id || ''),\r\n                      name: String(questions[selectedQuestionIndex].name || ''),\r\n                      answerType: String(questions[selectedQuestionIndex].answerType || ''),\r\n                      correctOption: String(questions[selectedQuestionIndex].correctOption || ''),\r\n                      correctAnswer: String(questions[selectedQuestionIndex].correctAnswer || ''),\r\n                      image: questions[selectedQuestionIndex].image,\r\n                      imageUrl: questions[selectedQuestionIndex].imageUrl,\r\n                      type: questions[selectedQuestionIndex]?.answerType === \"Options\" ? \"mcq\" :\r\n                            questions[selectedQuestionIndex]?.answerType === \"Free Text\" ||\r\n                            questions[selectedQuestionIndex]?.answerType === \"Fill in the Blank\" ? \"fill\" :\r\n                            questions[selectedQuestionIndex]?.imageUrl ? \"image\" : \"mcq\",\r\n                      options: (() => {\r\n                        try {\r\n                          if (!questions[selectedQuestionIndex]?.options) return [];\r\n\r\n                          const opts = questions[selectedQuestionIndex].options;\r\n                          if (Array.isArray(opts)) {\r\n                            return opts\r\n                              .filter(option => option && typeof option === 'string')\r\n                              .map(option => String(option).trim())\r\n                              .filter(option => option.length > 0);\r\n                          } else if (typeof opts === 'object' && opts !== null) {\r\n                            return Object.values(opts)\r\n                              .filter(option => option && typeof option === 'string')\r\n                              .map(option => String(option).trim())\r\n                              .filter(option => option.length > 0);\r\n                          }\r\n                          return [];\r\n                        } catch (error) {\r\n                          console.error('Error processing options:', error);\r\n                          return [];\r\n                        }\r\n                      })()\r\n                    }}\r\n                  questionNumber={selectedQuestionIndex + 1}\r\n                  totalQuestions={questions.length}\r\n                  selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n                  onAnswerSelect={(answer) =>\r\n                    setSelectedOptions({\r\n                      ...selectedOptions,\r\n                      [selectedQuestionIndex]: answer,\r\n                    })\r\n                  }\r\n                  onNext={() => {\r\n                    if (selectedQuestionIndex === questions.length - 1) {\r\n                      calculateResult();\r\n                    } else {\r\n                      setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n                    }\r\n                  }}\r\n                    onPrevious={() => {\r\n                      if (selectedQuestionIndex > 0) {\r\n                        setSelectedQuestionIndex(selectedQuestionIndex - 1);\r\n                      }\r\n                    }}\r\n                    timeRemaining={secondsLeft}\r\n                    isLastQuestion={selectedQuestionIndex === questions.length - 1}\r\n                  />\r\n                ) : (\r\n                  <div className=\"text-center p-8 bg-red-50 rounded-lg border border-red-200\">\r\n                    <i className=\"ri-error-warning-line text-3xl text-red-600 mb-4 block\"></i>\r\n                    <h3 className=\"text-lg font-semibold text-red-800 mb-2\">Question Not Available</h3>\r\n                    <p className=\"text-red-600\">This question could not be loaded. Please try refreshing the page.</p>\r\n                    <button\r\n                      onClick={() => window.location.reload()}\r\n                      className=\"mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\r\n                    >\r\n                      Refresh Page\r\n                    </button>\r\n                  </div>\r\n                )}\r\n              </motion.div>\r\n            </div>\r\n\r\n            {/* Timer Overlay for Critical Moments */}\r\n            <QuizTimerOverlay\r\n              timeRemaining={secondsLeft}\r\n              onClose={() => {}}\r\n            />\r\n          </div>\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\">\r\n              {/* Modern Header */}\r\n              <div className={`px-8 py-10 text-center relative ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\"\r\n                  : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"\r\n              }`}>\r\n                <div className=\"relative\">\r\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500 to-green-600\"\r\n                      : \"bg-gradient-to-br from-amber-500 to-orange-600\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-12 h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${\r\n                    result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"}\r\n                  </h1>\r\n                  <p className=\"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've mastered this exam with flying colors!\"\r\n                      : \"Every challenge makes you stronger. Try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modern Statistics Cards */}\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                  {/* Score Card */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-blue-600 mb-2 tracking-tight\">\r\n                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-blue-700/80 uppercase tracking-wider\">Your Score</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-emerald-600 mb-2 tracking-tight\">\r\n                        {result.correctAnswers?.length || 0}/{questions.length}\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\">Correct</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\"\r\n                      : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"\r\n                  }`}>\r\n                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${\r\n                      result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"\r\n                    } to-transparent`}></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className={`text-4xl font-black mb-2 tracking-tight ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                      </div>\r\n                      <div className={`text-sm font-bold uppercase tracking-wider ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Progress Visualization */}\r\n                <div className=\"mb-8\">\r\n                  <div className=\"relative bg-slate-100 rounded-2xl p-6\">\r\n                    <div className=\"text-center mb-4\">\r\n                      <h3 className=\"text-lg font-bold text-slate-700 mb-1\">Performance Overview</h3>\r\n                      <p className=\"text-sm text-slate-500\">Your achievement level</p>\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\">\r\n                        <div\r\n                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${\r\n                            result.verdict === \"Pass\"\r\n                              ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\"\r\n                              : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"\r\n                          }`}\r\n                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                        >\r\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"></div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center mt-3\">\r\n                        <span className=\"text-xs font-medium text-slate-500\">0%</span>\r\n                        <span className={`text-lg font-black tracking-tight ${\r\n                          result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                        }`}>\r\n                          {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                        </span>\r\n                        <span className=\"text-xs font-medium text-slate-500\">100%</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* XP Display */}\r\n                {result.xpData && (\r\n                  <div className=\"mb-8\">\r\n                    <XPResultDisplay xpData={result.xpData} />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Modern Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Review Answers</span>\r\n                  </button>\r\n\r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\">\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            {/* Simple Header */}\r\n            <div className=\"text-center mb-6\">\r\n              <div className=\"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\">\r\n                <h2 className=\"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2\">\r\n                  Answer Review\r\n                </h2>\r\n                <p className=\"text-slate-600\">Quick overview of your answers</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Compact Questions Review */}\r\n            <div className=\"space-y-3 mb-6\">\r\n              {questions && Array.isArray(questions) ? questions.map((question, index) => {\r\n                // Ensure question is a valid object\r\n                if (!question || typeof question !== 'object') {\r\n                  return null;\r\n                }\r\n\r\n                const correctAnswer = question.answerType === \"Options\"\r\n                  ? question.correctOption\r\n                  : question.correctAnswer;\r\n                const isCorrect = correctAnswer === selectedOptions[index];\r\n                const userAnswer = selectedOptions[index];\r\n\r\n                return (\r\n                  <div\r\n                    key={question._id || index}\r\n                    className=\"backdrop-blur-md rounded-lg shadow-md border-2 p-4\"\r\n                    style={{\r\n                      backgroundColor: isCorrect ? '#bbf7d0' : '#fecaca',\r\n                      borderColor: isCorrect ? '#22c55e' : '#ef4444'\r\n                    }}\r\n                  >\r\n                    {/* Question */}\r\n                    <div className=\"mb-3\">\r\n                      <div className=\"flex items-start space-x-3\">\r\n                        <div className=\"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\">\r\n                          {index + 1}\r\n                        </div>\r\n                        <div className=\"flex-1\">\r\n                          <p className=\"text-slate-800 font-medium leading-relaxed\">{question.name}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    {/* Your Answer with Visual Indicator */}\r\n                    <div className=\"mb-2\">\r\n                      <span className=\"text-sm font-semibold text-slate-600\">Your Answer: </span>\r\n                      <span className={`font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\r\n                        {(() => {\r\n                          if (question.answerType === \"Options\") {\r\n                            if (question.options && userAnswer !== undefined && userAnswer !== null) {\r\n                              // Handle both object and array options\r\n                              if (typeof question.options === 'object' && !Array.isArray(question.options)) {\r\n                                const optionValue = question.options[userAnswer];\r\n                                return typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\r\n                              } else if (Array.isArray(question.options)) {\r\n                                const optionValue = question.options[userAnswer];\r\n                                return typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\r\n                              }\r\n                            }\r\n                            return String(userAnswer || \"Not answered\");\r\n                          } else {\r\n                            return String(userAnswer || \"Not answered\");\r\n                          }\r\n                        })()}\r\n                      </span>\r\n                      {isCorrect ? (\r\n                        <span className=\"ml-3 text-green-600 text-2xl font-black\">✓</span>\r\n                      ) : (\r\n                        <span className=\"ml-3 text-red-600 text-2xl font-black\">✗</span>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Correct Answer (only for wrong answers) */}\r\n                    {!isCorrect && (\r\n                      <div className=\"mb-2\">\r\n                        <span className=\"text-sm font-semibold text-slate-600\">Correct Answer: </span>\r\n                        <span className=\"font-medium text-green-700\">\r\n                          {(() => {\r\n                            if (question.answerType === \"Options\") {\r\n                              if (question.options && question.correctOption !== undefined && question.correctOption !== null) {\r\n                                // Handle both object and array options\r\n                                if (typeof question.options === 'object' && !Array.isArray(question.options)) {\r\n                                  const optionValue = question.options[question.correctOption];\r\n                                  return typeof optionValue === 'string' ? optionValue : String(optionValue || question.correctOption || \"Unknown\");\r\n                                } else if (Array.isArray(question.options)) {\r\n                                  const optionValue = question.options[question.correctOption];\r\n                                  return typeof optionValue === 'string' ? optionValue : String(optionValue || question.correctOption || \"Unknown\");\r\n                                }\r\n                              }\r\n                              return String(question.correctOption || \"Unknown\");\r\n                            } else {\r\n                              return String(question.correctAnswer || question.correctOption || \"Unknown\");\r\n                            }\r\n                          })()}\r\n                        </span>\r\n                        <span className=\"ml-3 text-green-500 text-2xl font-black\">✓</span>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* See Explanation Button (only for wrong answers) */}\r\n                    {!isCorrect && (\r\n                      <div className=\"mt-2\">\r\n                        <button\r\n                          className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\"\r\n                          onClick={() => {\r\n                            console.log('Fetching explanation for:', question.name);\r\n\r\n                            // Safely get expected answer\r\n                            let expectedAnswer = \"\";\r\n                            if (question.answerType === \"Options\") {\r\n                              if (question.options && question.correctOption !== undefined) {\r\n                                const optionValue = question.options[question.correctOption];\r\n                                expectedAnswer = typeof optionValue === 'string' ? optionValue : String(optionValue || question.correctOption || \"\");\r\n                              } else {\r\n                                expectedAnswer = String(question.correctOption || \"\");\r\n                              }\r\n                            } else {\r\n                              expectedAnswer = String(question.correctAnswer || question.correctOption || \"\");\r\n                            }\r\n\r\n                            // Safely get user answer\r\n                            let userAnswerText = \"\";\r\n                            if (question.answerType === \"Options\") {\r\n                              if (question.options && userAnswer !== undefined) {\r\n                                const optionValue = question.options[userAnswer];\r\n                                userAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\r\n                              } else {\r\n                                userAnswerText = String(userAnswer || \"Not answered\");\r\n                              }\r\n                            } else {\r\n                              userAnswerText = String(userAnswer || \"Not answered\");\r\n                            }\r\n\r\n                            fetchExplanation(\r\n                              String(question.name || \"\"),\r\n                              expectedAnswer,\r\n                              userAnswerText,\r\n                              question.image || question.imageUrl\r\n                            );\r\n                          }}\r\n                        >\r\n                          <span>💡</span>\r\n                          <span>Get Explanation</span>\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Explanation */}\r\n                    {explanations[question.name] && (\r\n                      <div className=\"mt-2 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200\">\r\n                        <div className=\"flex items-center mb-2\">\r\n                          <span className=\"text-blue-600 text-lg mr-2\">💡</span>\r\n                          <h6 className=\"font-bold text-gray-800 text-base\">\r\n                            Explanation\r\n                          </h6>\r\n                        </div>\r\n\r\n                        {/* Show diagram/image for image-based questions */}\r\n                        {(question.image || question.imageUrl) && (\r\n                          <div className=\"mb-3 p-2 bg-gray-50 rounded border border-gray-200\">\r\n                            <div className=\"flex items-center mb-1\">\r\n                              <span className=\"text-gray-700 text-sm font-medium\">📊 Reference Diagram:</span>\r\n                            </div>\r\n                            <div className=\"flex justify-center\">\r\n                              <img\r\n                                src={question.image || question.imageUrl}\r\n                                alt=\"Question diagram\"\r\n                                className=\"max-w-full max-h-48 object-contain rounded border border-gray-300\"\r\n                                style={{ maxWidth: '350px' }}\r\n                              />\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n\r\n                        <div className=\"text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded\">\r\n                          <ContentRenderer text={explanations[question.name]} />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                );\r\n              }) : (\r\n                <div className=\"text-center p-8 bg-gray-50 rounded-lg border border-gray-200\">\r\n                  <p className=\"text-gray-600\">No questions available for review.</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Modern Navigation */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\r\n              <button\r\n                className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                onClick={() => setView(\"result\")}\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <span className=\"relative\">← Back to Results</span>\r\n              </button>\r\n\r\n              <button\r\n                className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                onClick={() => {\r\n                  // Reset exam state and restart\r\n                  setView(\"instructions\");\r\n                  setSelectedQuestionIndex(0);\r\n                  setSelectedOptions({});\r\n                  setResult({});\r\n                  setTimeUp(false);\r\n                  setSecondsLeft(examData?.duration || 0);\r\n                  setExplanations({});\r\n                }}\r\n              >\r\n                <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                <span className=\"relative\">🔄 Retake Quiz</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;\r\n"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,YAAY,EAAEC,SAAS,EAAEC,gBAAgB,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,4BAA4B;AAC7G,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,OAAO,EAAEC,cAAc,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,WAAW,EAAEC,YAAY,EAAEC,OAAO,QAAQ,gBAAgB;AAClH,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,uBAAuB,EAAEC,2BAA2B,QAAQ,wBAAwB;AAC7F,OAAOC,eAAe,MAAM,qCAAqC;AAEjE,OAAOC,YAAY,MAAM,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACnB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACmD,SAAS,EAAEC,YAAY,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACqD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGtD,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAACuD,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACyD,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM2D,MAAM,GAAGvD,SAAS,CAAC,CAAC;EAC1B,MAAMwD,QAAQ,GAAG3D,WAAW,CAAC,CAAC;EAC9B,MAAM4D,QAAQ,GAAG1D,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2D,IAAI,EAAEC,OAAO,CAAC,GAAG/D,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAM,CAACgE,WAAW,EAAEC,cAAc,CAAC,GAAGjE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkE,MAAM,EAAEC,SAAS,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACoE,UAAU,EAAEC,aAAa,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsE,SAAS,EAAEC,YAAY,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACwE,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM;IAAE0E;EAAK,CAAC,GAAGxE,WAAW,CAAEyE,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM;IAAEE,KAAK;IAAEC;EAAO,CAAC,GAAG9D,aAAa,CAAC,CAAC;EACzC,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEpD,MAAMgF,WAAW,GAAGlF,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFyE,YAAY,CAAC,IAAI,CAAC;MAClBX,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;MACvBuE,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEvB,MAAM,CAACwB,EAAE,CAAC;MAEpD,MAAMC,QAAQ,GAAG,MAAM7E,WAAW,CAAC;QAAE8E,MAAM,EAAE1B,MAAM,CAACwB;MAAG,CAAC,CAAC;MACzDF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;MAE3CxB,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MACvB8D,YAAY,CAAC,KAAK,CAAC;MAEnB,IAAIa,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMrC,QAAQ,GAAGmC,QAAQ,CAACG,IAAI;;QAE9B;QACA,IAAIpC,SAAS,GAAG,EAAE;QAClB,IAAIF,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEE,SAAS,IAAIqC,KAAK,CAACC,OAAO,CAACxC,QAAQ,CAACE,SAAS,CAAC,EAAE;UAC5DA,SAAS,GAAGF,QAAQ,CAACE,SAAS;QAChC,CAAC,MAAM,IAAIF,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEyC,QAAQ,IAAIF,KAAK,CAACC,OAAO,CAACxC,QAAQ,CAACyC,QAAQ,CAAC,EAAE;UACjEvC,SAAS,GAAGF,QAAQ,CAACyC,QAAQ;QAC/B,CAAC,MAAM,IAAIzC,QAAQ,IAAIuC,KAAK,CAACC,OAAO,CAACxC,QAAQ,CAAC,EAAE;UAC9CE,SAAS,GAAGF,QAAQ;QACtB;QAEAgC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEjC,QAAQ,CAAC;QACnCgC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE/B,SAAS,CAACwC,MAAM,CAAC;QACjDV,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEU,MAAM,CAACC,IAAI,CAAC5C,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhEG,YAAY,CAACD,SAAS,CAAC;QACvBD,WAAW,CAACD,QAAQ,CAAC;QACrBgB,cAAc,CAAC,CAAAhB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,QAAQ,KAAI,CAAC,CAAC;QAEvC,IAAI3C,SAAS,CAACwC,MAAM,KAAK,CAAC,EAAE;UAC1BV,OAAO,CAACc,IAAI,CAAC,iCAAiC,CAAC;UAC/Cd,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEE,QAAQ,CAAC;UACrDxF,OAAO,CAACoG,OAAO,CAAC,6DAA6D,CAAC;QAChF;MACF,CAAC,MAAM;QACLf,OAAO,CAACgB,KAAK,CAAC,YAAY,EAAEb,QAAQ,CAACxF,OAAO,CAAC;QAC7CqF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAAC;QAC7CxF,OAAO,CAACqG,KAAK,CAACb,QAAQ,CAACxF,OAAO,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOqG,KAAK,EAAE;MACdrC,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MACvB8D,YAAY,CAAC,KAAK,CAAC;MACnBU,OAAO,CAACgB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDrG,OAAO,CAACqG,KAAK,CAACA,KAAK,CAACrG,OAAO,IAAI,wCAAwC,CAAC;IAC1E;EACF,CAAC,EAAE,CAAC+D,MAAM,CAACwB,EAAE,EAAEvB,QAAQ,CAAC,CAAC;EAEzB,MAAMsC,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACR,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAEJ;IAAK,CAAC,GAAG,MAAMtD,uBAAuB,CAACkE,OAAO,CAAC;IACvD,OAAOZ,IAAI;EACb,CAAC;EAED,MAAMa,eAAe,GAAGtG,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF;MACA,IAAI,CAAC4E,IAAI,IAAI,CAACA,IAAI,CAAC2B,GAAG,EAAE;QACtBzG,OAAO,CAACqG,KAAK,CAAC,sCAAsC,CAAC;QACrDpC,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAD,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM4F,eAAe,GAAG,EAAE;MAC1B,MAAMC,QAAQ,GAAG,EAAE;MAEnBpD,SAAS,CAACqD,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAACE,UAAU,KAAK,WAAW,IAAIF,CAAC,CAACE,UAAU,KAAK,mBAAmB,EAAE;UACxEJ,QAAQ,CAACK,IAAI,CAACF,GAAG,CAAC;UAClBJ,eAAe,CAACM,IAAI,CAAC;YACnBlB,QAAQ,EAAEe,CAAC,CAACI,IAAI;YAChBC,cAAc,EAAEL,CAAC,CAACM,aAAa,IAAIN,CAAC,CAACO,aAAa;YAClDC,UAAU,EAAE1D,eAAe,CAACmD,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMQ,UAAU,GAAG,MAAMhB,oBAAoB,CAACI,eAAe,CAAC;MAC9D,MAAMa,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACV,OAAO,CAAEY,CAAC,IAAK;QACxB,IAAIA,CAAC,CAAC3D,MAAM,IAAI,OAAO2D,CAAC,CAAC3D,MAAM,CAAC4D,SAAS,KAAK,SAAS,EAAE;UACvDF,MAAM,CAACC,CAAC,CAAC1B,QAAQ,CAAC,GAAG0B,CAAC,CAAC3D,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAO2D,CAAC,CAACC,SAAS,KAAK,SAAS,EAAE;UAC3CF,MAAM,CAACC,CAAC,CAAC1B,QAAQ,CAAC,GAAG;YAAE2B,SAAS,EAAED,CAAC,CAACC,SAAS;YAAEC,MAAM,EAAEF,CAAC,CAACE,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,YAAY,GAAG,EAAE;MAEvBtE,SAAS,CAACqD,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMgB,aAAa,GAAGnE,eAAe,CAACmD,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAACE,UAAU,KAAK,WAAW,IAAIF,CAAC,CAACE,UAAU,KAAK,mBAAmB,EAAE;UACxE,MAAM;YAAEU,SAAS,GAAG,KAAK;YAAEC,MAAM,GAAG;UAAG,CAAC,GAAGH,MAAM,CAACV,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAMc,QAAQ,GAAG;YAAE,GAAGlB,CAAC;YAAEQ,UAAU,EAAES,aAAa;YAAEJ;UAAO,CAAC;UAE5D,IAAID,SAAS,EAAE;YACbE,cAAc,CAACX,IAAI,CAACe,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACZ,IAAI,CAACe,QAAQ,CAAC;YAC3BF,YAAY,CAACb,IAAI,CAAC;cAChBlB,QAAQ,EAAEe,CAAC,CAACI,IAAI;cAChBC,cAAc,EAAEL,CAAC,CAACM,aAAa,IAAIN,CAAC,CAACO,aAAa;cAClDC,UAAU,EAAES;YACd,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IAAIjB,CAAC,CAACE,UAAU,KAAK,SAAS,EAAE;UACrC,MAAMiB,UAAU,GAAGnB,CAAC,CAACO,aAAa;UAClC,MAAMa,YAAY,GAAIpB,CAAC,CAACqB,OAAO,IAAIrB,CAAC,CAACqB,OAAO,CAACF,UAAU,CAAC,IAAKA,UAAU;UACvE,MAAMG,SAAS,GAAItB,CAAC,CAACqB,OAAO,IAAIrB,CAAC,CAACqB,OAAO,CAACJ,aAAa,CAAC,IAAKA,aAAa,IAAI,EAAE;UAEhF,MAAML,SAAS,GAAGO,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGlB,CAAC;YAAEQ,UAAU,EAAES;UAAc,CAAC;UAEpD,IAAIL,SAAS,EAAE;YACbE,cAAc,CAACX,IAAI,CAACe,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACZ,IAAI,CAACe,QAAQ,CAAC;YAC3BF,YAAY,CAACb,IAAI,CAAC;cAChBlB,QAAQ,EAAEe,CAAC,CAACI,IAAI;cAChBC,cAAc,EAAEe,YAAY;cAC5BZ,UAAU,EAAEc;YACd,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,SAAS,GAAGxD,SAAS,GAAGyD,IAAI,CAACC,KAAK,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG5D,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC;MAC7E,MAAM6D,gBAAgB,GAAG,CAAC,CAAApF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,QAAQ,KAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;MAEzD;MACA,MAAMwC,cAAc,GAAGnF,SAAS,CAACwC,MAAM;MACvC,MAAM4C,YAAY,GAAGhB,cAAc,CAAC5B,MAAM;MAC1C,MAAM6C,eAAe,GAAGP,IAAI,CAACQ,KAAK,CAAEF,YAAY,GAAGD,cAAc,GAAI,GAAG,CAAC;MACzE,MAAMI,MAAM,GAAGH,YAAY,GAAG,EAAE,CAAC,CAAC;;MAElC;MACA,MAAMI,iBAAiB,GAAG1F,QAAQ,CAAC2F,YAAY,IAAI,EAAE,CAAC,CAAC;MACvD,MAAMC,OAAO,GAAGL,eAAe,IAAIG,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEtE,MAAMG,UAAU,GAAG;QACjBvB,cAAc,EAAEA,cAAc,IAAI,EAAE;QACpCC,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCqB,OAAO,EAAEA,OAAO,IAAI,MAAM;QAC1BE,KAAK,EAAEP,eAAe;QACtBE,MAAM,EAAEA,MAAM;QACdJ,cAAc,EAAEA,cAAc;QAC9BN,SAAS,EAAEA,SAAS;QACpBK,gBAAgB,EAAEA;MACpB,CAAC;MAED3E,SAAS,CAACoF,UAAU,CAAC;MAErB,MAAM1D,QAAQ,GAAG,MAAM5E,SAAS,CAAC;QAC/BwI,IAAI,EAAErF,MAAM,CAACwB,EAAE;QACf1B,MAAM,EAAEqF,UAAU;QAClBpE,IAAI,EAAEA,IAAI,CAAC2B;MACb,CAAC,CAAC;MAEF,IAAIjB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAM2D,YAAY,GAAG;UACnB,GAAGH,UAAU;UACbI,MAAM,EAAE9D,QAAQ,CAAC8D;QACnB,CAAC;QACDxF,SAAS,CAACuF,YAAY,CAAC;QAEvBlF,OAAO,CAAC,QAAQ,CAAC;QACjBoF,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,IAAIC,KAAK,CAACR,OAAO,KAAK,MAAM,GAAG7H,SAAS,GAAGgB,SAAS,CAAC,CAACsH,IAAI,CAAC,CAAC;MAC9D,CAAC,MAAM;QACL1J,OAAO,CAACqG,KAAK,CAACb,QAAQ,CAACxF,OAAO,CAAC;MACjC;MACAgE,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOwF,KAAK,EAAE;MACdrC,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MACvBb,OAAO,CAACqG,KAAK,CAACA,KAAK,CAACrG,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACuD,SAAS,EAAEI,eAAe,EAAEN,QAAQ,EAAEU,MAAM,CAACwB,EAAE,EAAET,IAAI,EAAEb,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE/E,MAAM2F,gBAAgB,GAAG,MAAAA,CAAO7D,QAAQ,EAAEoB,cAAc,EAAEG,UAAU,EAAEuC,QAAQ,KAAK;IACjF,IAAI;MACF5F,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM0E,QAAQ,GAAG,MAAMlD,2BAA2B,CAAC;QAAEwD,QAAQ;QAAEoB,cAAc;QAAEG,UAAU;QAAEuC;MAAS,CAAC,CAAC;MACtG5F,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAI2E,QAAQ,CAACE,OAAO,EAAE;QACpBP,eAAe,CAAE0E,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAAC/D,QAAQ,GAAGN,QAAQ,CAACsE;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACL9J,OAAO,CAACqG,KAAK,CAACb,QAAQ,CAACa,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdrC,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;MACvBb,OAAO,CAACqG,KAAK,CAACA,KAAK,CAACrG,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAM+J,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAG,CAAA3G,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,QAAQ,KAAI,CAAC;IAC5C7B,cAAc,CAAC2F,YAAY,CAAC;IAC5BnF,YAAY,CAAC0D,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1B,MAAMyB,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtC7F,cAAc,CAAE8F,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACL5F,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRE,aAAa,CAACwF,aAAa,CAAC;EAC9B,CAAC;EAED9J,SAAS,CAAC,MAAM;IACd,IAAImE,MAAM,IAAIJ,IAAI,KAAK,WAAW,EAAE;MAClCkG,aAAa,CAAC5F,UAAU,CAAC;MACzBgC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAClC,MAAM,EAAEJ,IAAI,EAAEM,UAAU,EAAEgC,eAAe,CAAC,CAAC;EAE/CrG,SAAS,CAAC,MAAM;IACdkF,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEvB,MAAM,CAACwB,EAAE,CAAC;IAC1D,IAAIxB,MAAM,CAACwB,EAAE,EAAE;MACbH,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLC,OAAO,CAACgB,KAAK,CAAC,uCAAuC,CAAC;MACtDrG,OAAO,CAACqG,KAAK,CAAC,sDAAsD,CAAC;MACrEpC,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,MAAM,CAACwB,EAAE,EAAEH,WAAW,EAAEnB,QAAQ,CAAC,CAAC;EAEtC9D,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIqE,UAAU,EAAE;QACd4F,aAAa,CAAC5F,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACArE,SAAS,CAAC,MAAM;IACd,IAAI+D,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACxEmG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChD,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD;;IAEA;IACA,OAAO,MAAM;MACXJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,CAACvG,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMwG,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF1G,QAAQ,CAAClD,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM0E,QAAQ,GAAG,MAAMmF,KAAK,CAAC,kCAAkC,EAAE;QAC/DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDT,IAAI,EAAEU,IAAI,CAACC,SAAS,CAAC;UAAExF,MAAM,EAAE1B,MAAM,CAACwB;QAAG,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMI,IAAI,GAAG,MAAMH,QAAQ,CAAC0F,IAAI,CAAC,CAAC;MAClC,IAAIvF,IAAI,CAACD,OAAO,EAAE;QAChB1F,OAAO,CAAC0F,OAAO,CAACC,IAAI,CAAC3F,OAAO,CAAC;QAC7B;QACAoF,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACLpF,OAAO,CAACqG,KAAK,CAACV,IAAI,CAAC3F,OAAO,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOqG,KAAK,EAAE;MACdrG,OAAO,CAACqG,KAAK,CAAC,iCAAiC,CAAC;IAClD,CAAC,SAAS;MACRrC,QAAQ,CAACnD,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,IAAI,CAACiE,IAAI,EAAE;IACT,oBACEpC,OAAA;MAAKyI,SAAS,EAAC,qGAAqG;MAAAC,QAAA,eAClH1I,OAAA;QAAKyI,SAAS,EAAC,2GAA2G;QAAAC,QAAA,gBACxH1I,OAAA;UAAKyI,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/F1I,OAAA;YAAKyI,SAAS,EAAC,sBAAsB;YAACE,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAF,QAAA,eAC3E1I,OAAA;cAAM6I,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,8JAA8J;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnJ,OAAA;UAAIyI,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAuB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFnJ,OAAA;UAAGyI,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvGnJ,OAAA;UACEyI,SAAS,EAAC,mNAAmN;UAC7NW,OAAO,EAAEA,CAAA,KAAM7H,QAAQ,CAAC,QAAQ,CAAE;UAAAmH,QAAA,EACnC;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAOxI,QAAQ,gBACbX,OAAA;IAAKyI,SAAS,EAAC,oEAAoE;IAAAC,QAAA,GAEhFlH,IAAI,KAAK,cAAc,iBACtBxB,OAAA,CAAC3B,YAAY;MACXsC,QAAQ,EAAEA,QAAS;MACnBc,OAAO,EAAEA,OAAQ;MACjB4F,UAAU,EAAEA,UAAW;MACvBxG,SAAS,EAAEA;IAAU;MAAAmI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACF,EAEA3H,IAAI,KAAK,WAAW,KACnBQ,SAAS,gBACPhC,OAAA;MAAKyI,SAAS,EAAC,qGAAqG;MAAAC,QAAA,eAClH1I,OAAA;QAAKyI,SAAS,EAAC,2GAA2G;QAAAC,QAAA,gBACxH1I,OAAA;UAAKyI,SAAS,EAAC,2IAA2I;UAAAC,QAAA,eACxJ1I,OAAA;YAAKyI,SAAS,EAAC,mCAAmC;YAACE,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAAAF,QAAA,gBAChF1I,OAAA;cAAQyI,SAAS,EAAC,YAAY;cAACY,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACxE,CAAC,EAAC,IAAI;cAACyE,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC;YAAG;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACrGnJ,OAAA;cAAMyI,SAAS,EAAC,YAAY;cAACE,IAAI,EAAC,cAAc;cAACG,CAAC,EAAC;YAAiH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnJ,OAAA;UAAIyI,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAe;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EnJ,OAAA;UAAGyI,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJtI,SAAS,CAACwC,MAAM,KAAK,CAAC,gBACxBrD,OAAA;MAAKyI,SAAS,EAAC,sGAAsG;MAAAC,QAAA,eACnH1I,OAAA;QAAKyI,SAAS,EAAC,4GAA4G;QAAAC,QAAA,gBACzH1I,OAAA;UAAKyI,SAAS,EAAC,8HAA8H;UAAAC,QAAA,eAC3I1I,OAAA;YAAKyI,SAAS,EAAC,sBAAsB;YAACE,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAF,QAAA,eAC3E1I,OAAA;cAAM6I,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,mNAAmN;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNnJ,OAAA;UAAIyI,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EnJ,OAAA;UAAGyI,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAC;QAE3D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJnJ,OAAA;UAAIyI,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACrD1I,OAAA;YAAA0I,QAAA,EAAI;UAA4C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDnJ,OAAA;YAAA0I,QAAA,EAAI;UAA4B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCnJ,OAAA;YAAA0I,QAAA,EAAI;UAA6B;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACLnJ,OAAA;UAAKyI,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB1I,OAAA;YACEoJ,OAAO,EAAEpB,mBAAoB;YAC7BS,SAAS,EAAC,iNAAiN;YAAAC,QAAA,EAC5N;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnJ,OAAA;YACEoJ,OAAO,EAAEA,CAAA,KAAM;cACbzG,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;cAC1CF,WAAW,CAAC,CAAC;YACf,CAAE;YACF+F,SAAS,EAAC,2MAA2M;YAAAC,QAAA,EACtN;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnJ,OAAA;YACEoJ,OAAO,EAAEA,CAAA,KAAM7H,QAAQ,CAAC,YAAY,CAAE;YACtCkH,SAAS,EAAC,2MAA2M;YAAAC,QAAA,EACtN;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENnJ,OAAA;MAAKyI,SAAS,EAAC,wDAAwD;MAAAC,QAAA,gBAErE1I,OAAA,CAACjC,MAAM,CAAC0L,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BnB,SAAS,EAAC,yEAAyE;QAAAC,QAAA,eAEnF1I,OAAA;UAAKyI,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B1I,OAAA;YAAKyI,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBAErD1I,OAAA;cAAKyI,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1C1I,OAAA,CAACP,OAAO;gBAACgJ,SAAS,EAAC;cAA0B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChDnJ,OAAA;gBAAA0I,QAAA,eACE1I,OAAA;kBAAIyI,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAE,CAAA/H,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4D,IAAI,KAAI;gBAAM;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnJ,OAAA,CAACpB,SAAS;cACR4E,QAAQ,EAAE,CAAA7C,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,QAAQ,KAAI,CAAE;cAClCsG,QAAQ,EAAEA,CAAA,KAAM;gBACdjI,SAAS,CAAC,IAAI,CAAC;gBACfiC,eAAe,CAAC,CAAC;cACnB,CAAE;cACFiG,QAAQ,EAAE,CAACnI,MAAO;cAClBoI,WAAW,EAAE,IAAK;cAClBC,gBAAgB,EAAE;YAAI;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbnJ,OAAA;QAAKyI,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC1I,OAAA,CAACjC,MAAM,CAAC0L,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BM,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAI,CAAE;UAAAzB,QAAA,EAE1B7H,SAAS,CAACE,qBAAqB,CAAC,IAAI,OAAOF,SAAS,CAACE,qBAAqB,CAAC,KAAK,QAAQ,gBACvFf,OAAA,CAACrB,YAAY;YACXyE,QAAQ,EAAE;cACR;cACAW,GAAG,EAAEqG,MAAM,CAACvJ,SAAS,CAACE,qBAAqB,CAAC,CAACgD,GAAG,IAAI,EAAE,CAAC;cACvDQ,IAAI,EAAE6F,MAAM,CAACvJ,SAAS,CAACE,qBAAqB,CAAC,CAACwD,IAAI,IAAI,EAAE,CAAC;cACzDF,UAAU,EAAE+F,MAAM,CAACvJ,SAAS,CAACE,qBAAqB,CAAC,CAACsD,UAAU,IAAI,EAAE,CAAC;cACrEK,aAAa,EAAE0F,MAAM,CAACvJ,SAAS,CAACE,qBAAqB,CAAC,CAAC2D,aAAa,IAAI,EAAE,CAAC;cAC3ED,aAAa,EAAE2F,MAAM,CAACvJ,SAAS,CAACE,qBAAqB,CAAC,CAAC0D,aAAa,IAAI,EAAE,CAAC;cAC3E4F,KAAK,EAAExJ,SAAS,CAACE,qBAAqB,CAAC,CAACsJ,KAAK;cAC7CnD,QAAQ,EAAErG,SAAS,CAACE,qBAAqB,CAAC,CAACmG,QAAQ;cACnDoD,IAAI,EAAE,EAAAnK,qBAAA,GAAAU,SAAS,CAACE,qBAAqB,CAAC,cAAAZ,qBAAA,uBAAhCA,qBAAA,CAAkCkE,UAAU,MAAK,SAAS,GAAG,KAAK,GAClE,EAAAjE,sBAAA,GAAAS,SAAS,CAACE,qBAAqB,CAAC,cAAAX,sBAAA,uBAAhCA,sBAAA,CAAkCiE,UAAU,MAAK,WAAW,IAC5D,EAAAhE,sBAAA,GAAAQ,SAAS,CAACE,qBAAqB,CAAC,cAAAV,sBAAA,uBAAhCA,sBAAA,CAAkCgE,UAAU,MAAK,mBAAmB,GAAG,MAAM,GAC7E,CAAA/D,sBAAA,GAAAO,SAAS,CAACE,qBAAqB,CAAC,cAAAT,sBAAA,eAAhCA,sBAAA,CAAkC4G,QAAQ,GAAG,OAAO,GAAG,KAAK;cAClE1B,OAAO,EAAE,CAAC,MAAM;gBACd,IAAI;kBAAA,IAAA+E,sBAAA;kBACF,IAAI,GAAAA,sBAAA,GAAC1J,SAAS,CAACE,qBAAqB,CAAC,cAAAwJ,sBAAA,eAAhCA,sBAAA,CAAkC/E,OAAO,GAAE,OAAO,EAAE;kBAEzD,MAAMgF,IAAI,GAAG3J,SAAS,CAACE,qBAAqB,CAAC,CAACyE,OAAO;kBACrD,IAAItC,KAAK,CAACC,OAAO,CAACqH,IAAI,CAAC,EAAE;oBACvB,OAAOA,IAAI,CACRC,MAAM,CAACC,MAAM,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,CAAC,CACtDC,GAAG,CAACD,MAAM,IAAIN,MAAM,CAACM,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CACpCH,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACrH,MAAM,GAAG,CAAC,CAAC;kBACxC,CAAC,MAAM,IAAI,OAAOmH,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;oBACpD,OAAOlH,MAAM,CAACuH,MAAM,CAACL,IAAI,CAAC,CACvBC,MAAM,CAACC,MAAM,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,CAAC,CACtDC,GAAG,CAACD,MAAM,IAAIN,MAAM,CAACM,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CACpCH,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACrH,MAAM,GAAG,CAAC,CAAC;kBACxC;kBACA,OAAO,EAAE;gBACX,CAAC,CAAC,OAAOM,KAAK,EAAE;kBACdhB,OAAO,CAACgB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;kBACjD,OAAO,EAAE;gBACX;cACF,CAAC,EAAE;YACL,CAAE;YACJmH,cAAc,EAAE/J,qBAAqB,GAAG,CAAE;YAC1CiF,cAAc,EAAEnF,SAAS,CAACwC,MAAO;YACjC0H,cAAc,EAAE9J,eAAe,CAACF,qBAAqB,CAAE;YACvDiK,cAAc,EAAGC,MAAM,IACrB/J,kBAAkB,CAAC;cACjB,GAAGD,eAAe;cAClB,CAACF,qBAAqB,GAAGkK;YAC3B,CAAC,CACF;YACDC,MAAM,EAAEA,CAAA,KAAM;cACZ,IAAInK,qBAAqB,KAAKF,SAAS,CAACwC,MAAM,GAAG,CAAC,EAAE;gBAClDS,eAAe,CAAC,CAAC;cACnB,CAAC,MAAM;gBACL9C,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;cACrD;YACF,CAAE;YACAoK,UAAU,EAAEA,CAAA,KAAM;cAChB,IAAIpK,qBAAqB,GAAG,CAAC,EAAE;gBAC7BC,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;cACrD;YACF,CAAE;YACFqK,aAAa,EAAE1J,WAAY;YAC3B2J,cAAc,EAAEtK,qBAAqB,KAAKF,SAAS,CAACwC,MAAM,GAAG;UAAE;YAAA2F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,gBAEFnJ,OAAA;YAAKyI,SAAS,EAAC,4DAA4D;YAAAC,QAAA,gBACzE1I,OAAA;cAAGyI,SAAS,EAAC;YAAwD;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EnJ,OAAA;cAAIyI,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAsB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnFnJ,OAAA;cAAGyI,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAkE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAClGnJ,OAAA;cACEoJ,OAAO,EAAEA,CAAA,KAAMvC,MAAM,CAACyE,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxC9C,SAAS,EAAC,oFAAoF;cAAAC,QAAA,EAC/F;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNnJ,OAAA,CAACnB,gBAAgB;QACfuM,aAAa,EAAE1J,WAAY;QAC3B8J,OAAO,EAAEA,CAAA,KAAM,CAAC;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN,CACF,EAEA3H,IAAI,KAAK,QAAQ,iBAChBxB,OAAA;MAAKyI,SAAS,EAAC,6EAA6E;MAAAC,QAAA,GACzFvH,MAAM,CAACoF,OAAO,KAAK,MAAM,iBAAIvG,OAAA,CAACxB,QAAQ;QAAC8D,KAAK,EAAEA,KAAM;QAACC,MAAM,EAAEA;MAAO;QAAAyG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAExEnJ,OAAA;QAAKyI,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrC1I,OAAA;UAAKyI,SAAS,EAAC,+FAA+F;UAAAC,QAAA,gBAE5G1I,OAAA;YAAKyI,SAAS,EAAG,mCACftH,MAAM,CAACoF,OAAO,KAAK,MAAM,GACrB,sEAAsE,GACtE,oEACL,EAAE;YAAAmC,QAAA,eACD1I,OAAA;cAAKyI,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvB1I,OAAA;gBAAKyI,SAAS,EAAG,iFACftH,MAAM,CAACoF,OAAO,KAAK,MAAM,GACrB,iDAAiD,GACjD,gDACL,EAAE;gBAAAmC,QAAA,eACD1I,OAAA;kBACEyL,GAAG,EAAEtK,MAAM,CAACoF,OAAO,KAAK,MAAM,GAAGjI,IAAI,GAAGC,IAAK;kBAC7CmN,GAAG,EAAEvK,MAAM,CAACoF,OAAQ;kBACpBkC,SAAS,EAAC;gBAA0B;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnJ,OAAA;gBAAIyI,SAAS,EAAG,2CACdtH,MAAM,CAACoF,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;gBAAAmC,QAAA,EACAvH,MAAM,CAACoF,OAAO,KAAK,MAAM,GAAG,iBAAiB,GAAG;cAAe;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACLnJ,OAAA;gBAAGyI,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAC/EvH,MAAM,CAACoF,OAAO,KAAK,MAAM,GACtB,+CAA+C,GAC/C;cAAgD;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNnJ,OAAA;YAAKyI,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClB1I,OAAA;cAAKyI,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzD1I,OAAA;gBAAKyI,SAAS,EAAC,yKAAyK;gBAAAC,QAAA,gBACtL1I,OAAA;kBAAKyI,SAAS,EAAC;gBAAqI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3JnJ,OAAA;kBAAKyI,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC1I,OAAA;oBAAKyI,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,GACnE/C,IAAI,CAACQ,KAAK,CAAE,CAAC,EAAA5F,qBAAA,GAAAY,MAAM,CAAC8D,cAAc,cAAA1E,qBAAA,uBAArBA,qBAAA,CAAuB8C,MAAM,KAAI,CAAC,IAAIxC,SAAS,CAACwC,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;kBAAA;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNnJ,OAAA;oBAAKyI,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,EAAC;kBAAU;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnJ,OAAA;gBAAKyI,SAAS,EAAC,8KAA8K;gBAAAC,QAAA,gBAC3L1I,OAAA;kBAAKyI,SAAS,EAAC;gBAAwI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9JnJ,OAAA;kBAAKyI,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC1I,OAAA;oBAAKyI,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,GACtE,EAAAlI,sBAAA,GAAAW,MAAM,CAAC8D,cAAc,cAAAzE,sBAAA,uBAArBA,sBAAA,CAAuB6C,MAAM,KAAI,CAAC,EAAC,GAAC,EAACxC,SAAS,CAACwC,MAAM;kBAAA;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNnJ,OAAA;oBAAKyI,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,EAAC;kBAAO;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnJ,OAAA;gBAAKyI,SAAS,EAAG,qGACftH,MAAM,CAACoF,OAAO,KAAK,MAAM,GACrB,4EAA4E,GAC5E,yEACL,EAAE;gBAAAmC,QAAA,gBACD1I,OAAA;kBAAKyI,SAAS,EAAG,wGACftH,MAAM,CAACoF,OAAO,KAAK,MAAM,GAAG,oBAAoB,GAAG,kBACpD;gBAAiB;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBnJ,OAAA;kBAAKyI,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC1I,OAAA;oBAAKyI,SAAS,EAAG,2CACftH,MAAM,CAACoF,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;oBAAAmC,QAAA,EACAvH,MAAM,CAACoF,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG;kBAAO;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACNnJ,OAAA;oBAAKyI,SAAS,EAAG,8CACftH,MAAM,CAACoF,OAAO,KAAK,MAAM,GAAG,qBAAqB,GAAG,mBACrD,EAAE;oBAAAmC,QAAA,EACAvH,MAAM,CAACoF,OAAO,KAAK,MAAM,GAAG,UAAU,GAAI,QAAO5F,QAAQ,CAAC2F,YAAa;kBAAC;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNnJ,OAAA;cAAKyI,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB1I,OAAA;gBAAKyI,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD1I,OAAA;kBAAKyI,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/B1I,OAAA;oBAAIyI,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/EnJ,OAAA;oBAAGyI,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNnJ,OAAA;kBAAKyI,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvB1I,OAAA;oBAAKyI,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChF1I,OAAA;sBACEyI,SAAS,EAAG,uFACVtH,MAAM,CAACoF,OAAO,KAAK,MAAM,GACrB,6DAA6D,GAC7D,2DACL,EAAE;sBACHoF,KAAK,EAAE;wBAAErJ,KAAK,EAAG,GAAG,CAAC,EAAA7B,sBAAA,GAAAU,MAAM,CAAC8D,cAAc,cAAAxE,sBAAA,uBAArBA,sBAAA,CAAuB4C,MAAM,KAAI,CAAC,IAAIxC,SAAS,CAACwC,MAAM,GAAI,GAAI;sBAAG,CAAE;sBAAAqF,QAAA,eAExF1I,OAAA;wBAAKyI,SAAS,EAAC;sBAAgE;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNnJ,OAAA;oBAAKyI,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrD1I,OAAA;sBAAMyI,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAE;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9DnJ,OAAA;sBAAMyI,SAAS,EAAG,qCAChBtH,MAAM,CAACoF,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;sBAAAmC,QAAA,GACA/C,IAAI,CAACQ,KAAK,CAAE,CAAC,EAAAzF,sBAAA,GAAAS,MAAM,CAAC8D,cAAc,cAAAvE,sBAAA,uBAArBA,sBAAA,CAAuB2C,MAAM,KAAI,CAAC,IAAIxC,SAAS,CAACwC,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;oBAAA;sBAAA2F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPnJ,OAAA;sBAAMyI,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLhI,MAAM,CAACyF,MAAM,iBACZ5G,OAAA;cAAKyI,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnB1I,OAAA,CAACf,eAAe;gBAAC2H,MAAM,EAAEzF,MAAM,CAACyF;cAAO;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACN,eAGDnJ,OAAA;cAAKyI,SAAS,EAAC,gDAAgD;cAAAC,QAAA,eAC7D1I,OAAA;gBACEyI,SAAS,EAAC,sPAAsP;gBAChQW,OAAO,EAAEA,CAAA,KAAM3H,OAAO,CAAC,QAAQ,CAAE;gBAAAiH,QAAA,gBAEjC1I,OAAA;kBAAKyI,SAAS,EAAC;gBAAkI;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxJnJ,OAAA;kBAAMyI,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAc;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA3H,IAAI,KAAK,QAAQ,iBAChBxB,OAAA;MAAKyI,SAAS,EAAC,6EAA6E;MAAAC,QAAA,eAC1F1I,OAAA;QAAKyI,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBAErC1I,OAAA;UAAKyI,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B1I,OAAA;YAAKyI,SAAS,EAAC,kFAAkF;YAAAC,QAAA,gBAC/F1I,OAAA;cAAIyI,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAAC;YAEnH;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLnJ,OAAA;cAAGyI,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAA8B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnJ,OAAA;UAAKyI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5B7H,SAAS,IAAIqC,KAAK,CAACC,OAAO,CAACtC,SAAS,CAAC,GAAGA,SAAS,CAAC8J,GAAG,CAAC,CAACvH,QAAQ,EAAEwI,KAAK,KAAK;YAC1E;YACA,IAAI,CAACxI,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;cAC7C,OAAO,IAAI;YACb;YAEA,MAAMqB,aAAa,GAAGrB,QAAQ,CAACiB,UAAU,KAAK,SAAS,GACnDjB,QAAQ,CAACsB,aAAa,GACtBtB,QAAQ,CAACqB,aAAa;YAC1B,MAAMM,SAAS,GAAGN,aAAa,KAAKxD,eAAe,CAAC2K,KAAK,CAAC;YAC1D,MAAMjH,UAAU,GAAG1D,eAAe,CAAC2K,KAAK,CAAC;YAEzC,oBACE5L,OAAA;cAEEyI,SAAS,EAAC,oDAAoD;cAC9DkD,KAAK,EAAE;gBACLE,eAAe,EAAE9G,SAAS,GAAG,SAAS,GAAG,SAAS;gBAClD+G,WAAW,EAAE/G,SAAS,GAAG,SAAS,GAAG;cACvC,CAAE;cAAA2D,QAAA,gBAGF1I,OAAA;gBAAKyI,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB1I,OAAA;kBAAKyI,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzC1I,OAAA;oBAAKyI,SAAS,EAAC,iHAAiH;oBAAAC,QAAA,EAC7HkD,KAAK,GAAG;kBAAC;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACNnJ,OAAA;oBAAKyI,SAAS,EAAC,QAAQ;oBAAAC,QAAA,eACrB1I,OAAA;sBAAGyI,SAAS,EAAC,4CAA4C;sBAAAC,QAAA,EAAEtF,QAAQ,CAACmB;oBAAI;sBAAAyE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNnJ,OAAA;gBAAKyI,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB1I,OAAA;kBAAMyI,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAa;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3EnJ,OAAA;kBAAMyI,SAAS,EAAG,eAAc1D,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;kBAAA2D,QAAA,EAC7E,CAAC,MAAM;oBACN,IAAItF,QAAQ,CAACiB,UAAU,KAAK,SAAS,EAAE;sBACrC,IAAIjB,QAAQ,CAACoC,OAAO,IAAIb,UAAU,KAAKoH,SAAS,IAAIpH,UAAU,KAAK,IAAI,EAAE;wBACvE;wBACA,IAAI,OAAOvB,QAAQ,CAACoC,OAAO,KAAK,QAAQ,IAAI,CAACtC,KAAK,CAACC,OAAO,CAACC,QAAQ,CAACoC,OAAO,CAAC,EAAE;0BAC5E,MAAMwG,WAAW,GAAG5I,QAAQ,CAACoC,OAAO,CAACb,UAAU,CAAC;0BAChD,OAAO,OAAOqH,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG5B,MAAM,CAAC4B,WAAW,IAAIrH,UAAU,IAAI,cAAc,CAAC;wBAC5G,CAAC,MAAM,IAAIzB,KAAK,CAACC,OAAO,CAACC,QAAQ,CAACoC,OAAO,CAAC,EAAE;0BAC1C,MAAMwG,WAAW,GAAG5I,QAAQ,CAACoC,OAAO,CAACb,UAAU,CAAC;0BAChD,OAAO,OAAOqH,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG5B,MAAM,CAAC4B,WAAW,IAAIrH,UAAU,IAAI,cAAc,CAAC;wBAC5G;sBACF;sBACA,OAAOyF,MAAM,CAACzF,UAAU,IAAI,cAAc,CAAC;oBAC7C,CAAC,MAAM;sBACL,OAAOyF,MAAM,CAACzF,UAAU,IAAI,cAAc,CAAC;oBAC7C;kBACF,CAAC,EAAE;gBAAC;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,EACNpE,SAAS,gBACR/E,OAAA;kBAAMyI,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAElEnJ,OAAA;kBAAMyI,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAChE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGL,CAACpE,SAAS,iBACT/E,OAAA;gBAAKyI,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB1I,OAAA;kBAAMyI,SAAS,EAAC,sCAAsC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9EnJ,OAAA;kBAAMyI,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EACzC,CAAC,MAAM;oBACN,IAAItF,QAAQ,CAACiB,UAAU,KAAK,SAAS,EAAE;sBACrC,IAAIjB,QAAQ,CAACoC,OAAO,IAAIpC,QAAQ,CAACsB,aAAa,KAAKqH,SAAS,IAAI3I,QAAQ,CAACsB,aAAa,KAAK,IAAI,EAAE;wBAC/F;wBACA,IAAI,OAAOtB,QAAQ,CAACoC,OAAO,KAAK,QAAQ,IAAI,CAACtC,KAAK,CAACC,OAAO,CAACC,QAAQ,CAACoC,OAAO,CAAC,EAAE;0BAC5E,MAAMwG,WAAW,GAAG5I,QAAQ,CAACoC,OAAO,CAACpC,QAAQ,CAACsB,aAAa,CAAC;0BAC5D,OAAO,OAAOsH,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG5B,MAAM,CAAC4B,WAAW,IAAI5I,QAAQ,CAACsB,aAAa,IAAI,SAAS,CAAC;wBACnH,CAAC,MAAM,IAAIxB,KAAK,CAACC,OAAO,CAACC,QAAQ,CAACoC,OAAO,CAAC,EAAE;0BAC1C,MAAMwG,WAAW,GAAG5I,QAAQ,CAACoC,OAAO,CAACpC,QAAQ,CAACsB,aAAa,CAAC;0BAC5D,OAAO,OAAOsH,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG5B,MAAM,CAAC4B,WAAW,IAAI5I,QAAQ,CAACsB,aAAa,IAAI,SAAS,CAAC;wBACnH;sBACF;sBACA,OAAO0F,MAAM,CAAChH,QAAQ,CAACsB,aAAa,IAAI,SAAS,CAAC;oBACpD,CAAC,MAAM;sBACL,OAAO0F,MAAM,CAAChH,QAAQ,CAACqB,aAAa,IAAIrB,QAAQ,CAACsB,aAAa,IAAI,SAAS,CAAC;oBAC9E;kBACF,CAAC,EAAE;gBAAC;kBAAAsE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACPnJ,OAAA;kBAAMyI,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAAC;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CACN,EAGA,CAACpE,SAAS,iBACT/E,OAAA;gBAAKyI,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB1I,OAAA;kBACEyI,SAAS,EAAC,iKAAiK;kBAC3KW,OAAO,EAAEA,CAAA,KAAM;oBACbzG,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEQ,QAAQ,CAACmB,IAAI,CAAC;;oBAEvD;oBACA,IAAIC,cAAc,GAAG,EAAE;oBACvB,IAAIpB,QAAQ,CAACiB,UAAU,KAAK,SAAS,EAAE;sBACrC,IAAIjB,QAAQ,CAACoC,OAAO,IAAIpC,QAAQ,CAACsB,aAAa,KAAKqH,SAAS,EAAE;wBAC5D,MAAMC,WAAW,GAAG5I,QAAQ,CAACoC,OAAO,CAACpC,QAAQ,CAACsB,aAAa,CAAC;wBAC5DF,cAAc,GAAG,OAAOwH,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG5B,MAAM,CAAC4B,WAAW,IAAI5I,QAAQ,CAACsB,aAAa,IAAI,EAAE,CAAC;sBACtH,CAAC,MAAM;wBACLF,cAAc,GAAG4F,MAAM,CAAChH,QAAQ,CAACsB,aAAa,IAAI,EAAE,CAAC;sBACvD;oBACF,CAAC,MAAM;sBACLF,cAAc,GAAG4F,MAAM,CAAChH,QAAQ,CAACqB,aAAa,IAAIrB,QAAQ,CAACsB,aAAa,IAAI,EAAE,CAAC;oBACjF;;oBAEA;oBACA,IAAIuH,cAAc,GAAG,EAAE;oBACvB,IAAI7I,QAAQ,CAACiB,UAAU,KAAK,SAAS,EAAE;sBACrC,IAAIjB,QAAQ,CAACoC,OAAO,IAAIb,UAAU,KAAKoH,SAAS,EAAE;wBAChD,MAAMC,WAAW,GAAG5I,QAAQ,CAACoC,OAAO,CAACb,UAAU,CAAC;wBAChDsH,cAAc,GAAG,OAAOD,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG5B,MAAM,CAAC4B,WAAW,IAAIrH,UAAU,IAAI,cAAc,CAAC;sBACtH,CAAC,MAAM;wBACLsH,cAAc,GAAG7B,MAAM,CAACzF,UAAU,IAAI,cAAc,CAAC;sBACvD;oBACF,CAAC,MAAM;sBACLsH,cAAc,GAAG7B,MAAM,CAACzF,UAAU,IAAI,cAAc,CAAC;oBACvD;oBAEAsC,gBAAgB,CACdmD,MAAM,CAAChH,QAAQ,CAACmB,IAAI,IAAI,EAAE,CAAC,EAC3BC,cAAc,EACdyH,cAAc,EACd7I,QAAQ,CAACiH,KAAK,IAAIjH,QAAQ,CAAC8D,QAC7B,CAAC;kBACH,CAAE;kBAAAwB,QAAA,gBAEF1I,OAAA;oBAAA0I,QAAA,EAAM;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACfnJ,OAAA;oBAAA0I,QAAA,EAAM;kBAAe;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN,EAGA3G,YAAY,CAACY,QAAQ,CAACmB,IAAI,CAAC,iBAC1BvE,OAAA;gBAAKyI,SAAS,EAAC,4FAA4F;gBAAAC,QAAA,gBACzG1I,OAAA;kBAAKyI,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrC1I,OAAA;oBAAMyI,SAAS,EAAC,4BAA4B;oBAAAC,QAAA,EAAC;kBAAE;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtDnJ,OAAA;oBAAIyI,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAElD;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,EAGL,CAAC/F,QAAQ,CAACiH,KAAK,IAAIjH,QAAQ,CAAC8D,QAAQ,kBACnClH,OAAA;kBAAKyI,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBACjE1I,OAAA;oBAAKyI,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,eACrC1I,OAAA;sBAAMyI,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAAqB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC,eACNnJ,OAAA;oBAAKyI,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,eAClC1I,OAAA;sBACEyL,GAAG,EAAErI,QAAQ,CAACiH,KAAK,IAAIjH,QAAQ,CAAC8D,QAAS;sBACzCwE,GAAG,EAAC,kBAAkB;sBACtBjD,SAAS,EAAC,mEAAmE;sBAC7EkD,KAAK,EAAE;wBAAEO,QAAQ,EAAE;sBAAQ;oBAAE;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,eAEDnJ,OAAA;kBAAKyI,SAAS,EAAC,8DAA8D;kBAAAC,QAAA,eAC3E1I,OAAA,CAACH,eAAe;oBAACsM,IAAI,EAAE3J,YAAY,CAACY,QAAQ,CAACmB,IAAI;kBAAE;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA,GA1JI/F,QAAQ,CAACW,GAAG,IAAI6H,KAAK;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2JvB,CAAC;UAEV,CAAC,CAAC,gBACAnJ,OAAA;YAAKyI,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3E1I,OAAA;cAAGyI,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAkC;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNnJ,OAAA;UAAKyI,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC1E1I,OAAA;YACEyI,SAAS,EAAC,sPAAsP;YAChQW,OAAO,EAAEA,CAAA,KAAM3H,OAAO,CAAC,QAAQ,CAAE;YAAAiH,QAAA,gBAEjC1I,OAAA;cAAKyI,SAAS,EAAC;YAAkI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxJnJ,OAAA;cAAMyI,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAiB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAETnJ,OAAA;YACEyI,SAAS,EAAC,0PAA0P;YACpQW,OAAO,EAAEA,CAAA,KAAM;cACb;cACA3H,OAAO,CAAC,cAAc,CAAC;cACvBT,wBAAwB,CAAC,CAAC,CAAC;cAC3BE,kBAAkB,CAAC,CAAC,CAAC,CAAC;cACtBE,SAAS,CAAC,CAAC,CAAC,CAAC;cACbS,SAAS,CAAC,KAAK,CAAC;cAChBF,cAAc,CAAC,CAAAhB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6C,QAAQ,KAAI,CAAC,CAAC;cACvCf,eAAe,CAAC,CAAC,CAAC,CAAC;YACrB,CAAE;YAAAiG,QAAA,gBAEF1I,OAAA;cAAKyI,SAAS,EAAC;YAAkI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxJnJ,OAAA;cAAMyI,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC,GACJ,IAAI;AACV;AAACjJ,EAAA,CAt4BQD,SAAS;EAAA,QAMDnC,SAAS,EACPH,WAAW,EACXE,WAAW,EAOXD,WAAW,EAEFa,aAAa;AAAA;AAAA2N,EAAA,GAjBhCnM,SAAS;AAw4BlB,eAAeA,SAAS;AAAC,IAAAmM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}