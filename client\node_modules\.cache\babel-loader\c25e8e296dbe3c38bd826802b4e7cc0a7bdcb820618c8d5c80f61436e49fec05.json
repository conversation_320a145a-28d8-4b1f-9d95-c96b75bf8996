{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Tb<PERSON>ser, TbUsers, TbTrophy, TbPlayerPlay, TbPlayerPause, TbClock, TbSearch, TbFilter } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingList = ({\n  users = [],\n  currentUserId = null,\n  layout = 'horizontal',\n  // 'horizontal', 'vertical', 'grid'\n  size = 'medium',\n  showStats = true,\n  className = '',\n  currentUserRef = null,\n  showFindMe = false,\n  lastUpdated = null,\n  autoRefresh = false,\n  onAutoRefreshToggle = null\n}) => {\n  _s();\n  // State for search and filtering\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'\n  const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'\n  const [viewMode, setViewMode] = useState('card'); // 'card', 'compact', 'detailed'\n  const [showOnlyMyClass, setShowOnlyMyClass] = useState(false);\n  const [localShowFindMe, setLocalShowFindMe] = useState(false);\n  const localCurrentUserRef = useRef(null);\n\n  // Use passed refs or local ones\n  const userRef = currentUserRef || localCurrentUserRef;\n  const findMeActive = showFindMe || localShowFindMe;\n\n  // Get current user's class for filtering\n  const currentUser = users.find(user => user.userId === currentUserId || user._id === currentUserId);\n  const currentUserClass = currentUser === null || currentUser === void 0 ? void 0 : currentUser.class;\n\n  // Filter and search users\n  const filteredUsers = users.filter(user => {\n    var _user$name, _user$email, _user$class, _user$normalizedSubsc, _user$subscriptionSta;\n    // Search filter\n    const matchesSearch = ((_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.toLowerCase().includes(searchTerm.toLowerCase())) || ((_user$class = user.class) === null || _user$class === void 0 ? void 0 : _user$class.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Class filter\n    const matchesClass = !showOnlyMyClass || user.class === currentUserClass;\n\n    // Subscription filter\n    const userStatus = ((_user$normalizedSubsc = user.normalizedSubscriptionStatus) === null || _user$normalizedSubsc === void 0 ? void 0 : _user$normalizedSubsc.toLowerCase()) || ((_user$subscriptionSta = user.subscriptionStatus) === null || _user$subscriptionSta === void 0 ? void 0 : _user$subscriptionSta.toLowerCase()) || 'free';\n    let matchesFilter = true;\n    switch (filterType) {\n      case 'premium':\n        matchesFilter = userStatus === 'premium' || userStatus === 'active';\n        break;\n      case 'expired':\n        matchesFilter = userStatus === 'expired';\n        break;\n      case 'free':\n        matchesFilter = userStatus === 'free';\n        break;\n      default:\n        matchesFilter = true;\n    }\n    return matchesSearch && matchesFilter && matchesClass;\n  }).sort((a, b) => {\n    switch (sortBy) {\n      case 'xp':\n        return (b.totalXP || 0) - (a.totalXP || 0);\n      case 'name':\n        return (a.name || '').localeCompare(b.name || '');\n      case 'score':\n        return (b.rankingScore || b.score || 0) - (a.rankingScore || a.score || 0);\n      case 'class':\n        return (a.class || '').localeCompare(b.class || '');\n      default:\n        return (a.rank || 0) - (b.rank || 0);\n    }\n  });\n\n  // Calculate class ranks for filtered users\n  const usersWithClassRank = filteredUsers.map(user => {\n    // Group users by class and calculate class rank\n    const sameClassUsers = filteredUsers.filter(u => u.class === user.class);\n    const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n    return {\n      ...user,\n      classRank\n    };\n  });\n\n  // Scroll to current user\n  const scrollToCurrentUser = () => {\n    if (userRef.current) {\n      userRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setLocalShowFindMe(true);\n      // Hide the highlight after 3 seconds\n      setTimeout(() => setLocalShowFindMe(false), 3000);\n    }\n  };\n\n  // Get layout classes based on view mode and layout\n  const getLayoutClasses = () => {\n    if (viewMode === 'compact') {\n      return 'space-y-2';\n    }\n    switch (layout) {\n      case 'vertical':\n        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n      case 'grid':\n        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';\n      case 'horizontal':\n      default:\n        return 'space-y-4';\n    }\n  };\n\n  // Stats summary with enhanced calculations\n  const totalUsers = users.length;\n  const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium' || u.normalizedSubscriptionStatus === 'premium').length;\n\n  // Use ranking score or XP as the primary metric\n  const topScore = users.length > 0 ? Math.max(...users.map(u => u.rankingScore || u.totalXP || u.totalPoints || 0)) : 0;\n\n  // Calculate additional stats\n  const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n  const averageXP = users.length > 0 ? Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200 animate-fadeInUp\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"ranking-search\",\n                name: \"ranking-search\",\n                type: \"text\",\n                placeholder: \"Search by name, email, or class...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                autoComplete: \"off\",\n                className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 37\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSearchTerm(''),\n                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowOnlyMyClass(!showOnlyMyClass),\n                className: `px-4 py-2 rounded-lg font-medium transition-all duration-200 ${showOnlyMyClass ? 'bg-blue-500 text-white shadow-md' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                children: \"My Class Only\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {\n                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                id: \"ranking-filter\",\n                name: \"ranking-filter\",\n                value: filterType,\n                onChange: e => setFilterType(e.target.value),\n                autoComplete: \"off\",\n                className: \"pl-10 pr-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Subscriptions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"premium\",\n                  children: \"\\uD83D\\uDC51 Premium Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"free\",\n                  children: \"\\uD83C\\uDD93 Free Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"expired\",\n                  children: \"\\u23F0 Expired Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              id: \"ranking-sort\",\n              name: \"ranking-sort\",\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              autoComplete: \"off\",\n              className: \"px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"rank\",\n                children: \"\\uD83C\\uDFC6 Sort by Rank\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"xp\",\n                children: \"\\u26A1 Sort by XP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"score\",\n                children: \"\\uD83D\\uDCCA Sort by Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"name\",\n                children: \"\\uD83D\\uDCDD Sort by Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"class\",\n                children: \"\\uD83C\\uDF93 Sort by Class\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex bg-gray-100 rounded-lg p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('card'),\n                className: `px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${viewMode === 'card' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-800'}`,\n                children: \"Cards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('compact'),\n                className: `px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${viewMode === 'compact' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-800'}`,\n                children: \"Compact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-gray-800\",\n              children: usersWithClassRank.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 33\n            }, this), \" of \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-gray-800\",\n              children: users.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 44\n            }, this), \" users\", searchTerm && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-1\",\n              children: [\"matching \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-blue-600\",\n                children: [\"\\\"\", searchTerm, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 50\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 37\n            }, this), filterType !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium\",\n              children: filterType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 37\n            }, this), showOnlyMyClass && currentUserClass && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium\",\n              children: [\"Class \", currentUserClass]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-4 text-xs text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\uD83C\\uDFC6 Top: \", Math.max(...usersWithClassRank.map(u => u.rankingScore || u.totalXP || 0)).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\uD83D\\uDCCA Avg: \", Math.round(usersWithClassRank.reduce((sum, u) => sum + (u.rankingScore || u.totalXP || 0), 0) / usersWithClassRank.length || 0).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-7 h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent\",\n                children: \"Leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: \"Top performers across all levels\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 29\n          }, this), lastUpdated && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-blue-700 font-medium\",\n              children: [\"Updated \", new Date(lastUpdated).toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [onAutoRefreshToggle && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onAutoRefreshToggle,\n            className: `px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 ${autoRefresh ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`,\n            children: [autoRefresh ? /*#__PURE__*/_jsxDEV(TbPlayerPause, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 52\n            }, this) : /*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 92\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: autoRefresh ? 'Auto' : 'Manual'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 33\n          }, this), currentUserId && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: scrollToCurrentUser,\n            className: \"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95\",\n            children: [/*#__PURE__*/_jsxDEV(TbUser, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Find Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 sm:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-blue-700\",\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-blue-900 mb-1\",\n            children: totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-blue-600 font-medium\",\n            children: [activeUsers, \" active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-yellow-700\",\n              children: \"Premium Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-yellow-900 mb-1\",\n            children: premiumUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-yellow-600 font-medium\",\n            children: [totalUsers > 0 ? Math.round(premiumUsers / totalUsers * 100) : 0, \"% premium\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-green-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbUser, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-green-700\",\n              children: \"Top Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-green-900 mb-1\",\n            children: topScore.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-green-600 font-medium\",\n            children: \"ranking points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-purple-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-purple-700\",\n              children: \"Avg XP\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-purple-900 mb-1\",\n            children: averageXP.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: \"experience points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `animate-fadeInUp ${getLayoutClasses()}`,\n      children: usersWithClassRank.map((user, index) => {\n        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n        const rank = user.rank || index + 1;\n\n        // Render compact view for better performance with large lists\n        if (viewMode === 'compact') {\n          var _ref, _ref2, _ref3, _ref4, _ref5, _ref6;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: isCurrentUser ? userRef : null,\n            className: `animate-slideInLeft transition-all duration-200 p-3 rounded-lg border ${isCurrentUser && findMeActive ? 'find-me-highlight ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200' : isCurrentUser ? 'ring-2 ring-blue-400 bg-blue-50 border-blue-200' : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-md'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${rank <= 3 ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white' : rank <= 10 ? 'bg-gradient-to-r from-blue-400 to-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n                  children: rank\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: user.profilePicture || '/default-avatar.png',\n                    alt: user.name,\n                    className: \"w-8 h-8 rounded-full object-cover border-2 border-gray-200\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-semibold text-gray-900 text-sm\",\n                      children: user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [\"Class \", user.class]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-bold text-gray-900 text-sm\",\n                    children: (user.rankingScore || user.totalXP || 0).toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: user.rankingScore ? 'pts' : 'XP'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${((_ref = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref === void 0 ? void 0 : _ref.toLowerCase()) === 'premium' || ((_ref2 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref2 === void 0 ? void 0 : _ref2.toLowerCase()) === 'active' ? 'bg-yellow-100 text-yellow-800' : ((_ref3 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref3 === void 0 ? void 0 : _ref3.toLowerCase()) === 'expired' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`,\n                  children: ((_ref4 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref4 === void 0 ? void 0 : _ref4.toLowerCase()) === 'premium' || ((_ref5 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref5 === void 0 ? void 0 : _ref5.toLowerCase()) === 'active' ? '👑' : ((_ref6 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref6 === void 0 ? void 0 : _ref6.toLowerCase()) === 'expired' ? '⏰' : '🆓'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 33\n            }, this)\n          }, user.userId || user._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 29\n          }, this);\n        }\n\n        // Regular card view\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: isCurrentUser ? userRef : null,\n          className: `animate-slideInLeft transition-all duration-300 ${isCurrentUser && findMeActive ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl' : isCurrentUser ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(UserRankingCard, {\n            user: user,\n            rank: rank,\n            classRank: user.classRank,\n            isCurrentUser: isCurrentUser,\n            layout: layout,\n            size: size,\n            showStats: showStats\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 33\n          }, this)\n        }, user.userId || user._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 25\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 13\n    }, this), usersWithClassRank.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12 bg-white rounded-xl border border-gray-200 animate-fadeInUp\",\n      children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-2\",\n        children: \"No users found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: users.length === 0 ? 'No ranking data available.' : 'Try adjusting your search or filter criteria.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 488,\n      columnNumber: 17\n    }, this), currentUserId && usersWithClassRank.length > 10 && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: scrollToCurrentUser,\n      className: \"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 hover:scale-110 active:scale-95 animate-bounce\",\n      title: \"Find me in ranking\",\n      children: /*#__PURE__*/_jsxDEV(TbUser, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 504,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 138,\n    columnNumber: 9\n  }, this);\n};\n_s(UserRankingList, \"s9yOzICRFbNABlQU30hbh3VQGA4=\");\n_c = UserRankingList;\nexport default UserRankingList;\nvar _c;\n$RefreshReg$(_c, \"UserRankingList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "TbUser", "TbUsers", "TbTrophy", "TbPlayerPlay", "TbPlayerPause", "TbClock", "TbSearch", "Tb<PERSON><PERSON>er", "UserRankingCard", "jsxDEV", "_jsxDEV", "UserRankingList", "users", "currentUserId", "layout", "size", "showStats", "className", "currentUserRef", "showFindMe", "lastUpdated", "autoRefresh", "onAutoRefreshToggle", "_s", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "sortBy", "setSortBy", "viewMode", "setViewMode", "showOnlyMyClass", "setShowOnlyMyClass", "localShowFindMe", "setLocalShowFindMe", "localCurrentUserRef", "userRef", "findMeActive", "currentUser", "find", "user", "userId", "_id", "currentUserClass", "class", "filteredUsers", "filter", "_user$name", "_user$email", "_user$class", "_user$normalizedSubsc", "_user$subscriptionSta", "matchesSearch", "name", "toLowerCase", "includes", "email", "matchesClass", "userStatus", "normalizedSubscriptionStatus", "subscriptionStatus", "matchesFilter", "sort", "a", "b", "totalXP", "localeCompare", "rankingScore", "score", "rank", "usersWithClassRank", "map", "sameClassUsers", "u", "classRank", "findIndex", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "setTimeout", "getLayoutClasses", "totalUsers", "length", "premiumUsers", "topScore", "Math", "max", "totalPoints", "activeUsers", "totalQuizzesTaken", "averageXP", "round", "reduce", "sum", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "type", "placeholder", "value", "onChange", "e", "target", "autoComplete", "onClick", "toLocaleString", "Date", "toLocaleTimeString", "index", "isCurrentUser", "_ref", "_ref2", "_ref3", "_ref4", "_ref5", "_ref6", "ref", "src", "profilePicture", "alt", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingList.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { TbUser, TbUsers, Tb<PERSON>rophy, TbPlayerPlay, TbPlayerPause, Tb<PERSON>lock, TbSearch, TbFilter } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\n\nconst UserRankingList = ({\n    users = [],\n    currentUserId = null,\n    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'\n    size = 'medium',\n    showStats = true,\n    className = '',\n    currentUserRef = null,\n    showFindMe = false,\n    lastUpdated = null,\n    autoRefresh = false,\n    onAutoRefreshToggle = null\n}) => {\n    // State for search and filtering\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'\n    const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'\n    const [viewMode, setViewMode] = useState('card'); // 'card', 'compact', 'detailed'\n    const [showOnlyMyClass, setShowOnlyMyClass] = useState(false);\n    const [localShowFindMe, setLocalShowFindMe] = useState(false);\n    const localCurrentUserRef = useRef(null);\n\n    // Use passed refs or local ones\n    const userRef = currentUserRef || localCurrentUserRef;\n    const findMeActive = showFindMe || localShowFindMe;\n\n    // Get current user's class for filtering\n    const currentUser = users.find(user => user.userId === currentUserId || user._id === currentUserId);\n    const currentUserClass = currentUser?.class;\n\n    // Filter and search users\n    const filteredUsers = users.filter(user => {\n        // Search filter\n        const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            user.class?.toLowerCase().includes(searchTerm.toLowerCase());\n\n        // Class filter\n        const matchesClass = !showOnlyMyClass || user.class === currentUserClass;\n\n        // Subscription filter\n        const userStatus = user.normalizedSubscriptionStatus?.toLowerCase() || user.subscriptionStatus?.toLowerCase() || 'free';\n        let matchesFilter = true;\n\n        switch (filterType) {\n            case 'premium':\n                matchesFilter = userStatus === 'premium' || userStatus === 'active';\n                break;\n            case 'expired':\n                matchesFilter = userStatus === 'expired';\n                break;\n            case 'free':\n                matchesFilter = userStatus === 'free';\n                break;\n            default:\n                matchesFilter = true;\n        }\n\n        return matchesSearch && matchesFilter && matchesClass;\n    }).sort((a, b) => {\n        switch (sortBy) {\n            case 'xp':\n                return (b.totalXP || 0) - (a.totalXP || 0);\n            case 'name':\n                return (a.name || '').localeCompare(b.name || '');\n            case 'score':\n                return (b.rankingScore || b.score || 0) - (a.rankingScore || a.score || 0);\n            case 'class':\n                return (a.class || '').localeCompare(b.class || '');\n            default:\n                return (a.rank || 0) - (b.rank || 0);\n        }\n    });\n\n    // Calculate class ranks for filtered users\n    const usersWithClassRank = filteredUsers.map(user => {\n        // Group users by class and calculate class rank\n        const sameClassUsers = filteredUsers.filter(u => u.class === user.class);\n        const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n        return { ...user, classRank };\n    });\n\n    // Scroll to current user\n    const scrollToCurrentUser = () => {\n        if (userRef.current) {\n            userRef.current.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n            setLocalShowFindMe(true);\n            // Hide the highlight after 3 seconds\n            setTimeout(() => setLocalShowFindMe(false), 3000);\n        }\n    };\n\n    // Get layout classes based on view mode and layout\n    const getLayoutClasses = () => {\n        if (viewMode === 'compact') {\n            return 'space-y-2';\n        }\n\n        switch (layout) {\n            case 'vertical':\n                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n            case 'grid':\n                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';\n            case 'horizontal':\n            default:\n                return 'space-y-4';\n        }\n    };\n\n\n\n    // Stats summary with enhanced calculations\n    const totalUsers = users.length;\n    const premiumUsers = users.filter(u =>\n        u.subscriptionStatus === 'active' ||\n        u.subscriptionStatus === 'premium' ||\n        u.normalizedSubscriptionStatus === 'premium'\n    ).length;\n\n    // Use ranking score or XP as the primary metric\n    const topScore = users.length > 0 ? Math.max(...users.map(u =>\n        u.rankingScore || u.totalXP || u.totalPoints || 0\n    )) : 0;\n\n    // Calculate additional stats\n    const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n    const averageXP = users.length > 0 ?\n        Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n\n    return (\n        <div className={`space-y-6 ${className}`}>\n            {/* Header with Stats */}\n            {showStats && (\n                <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200 animate-fadeInUp\">\n                    {/* Enhanced Search and Filter Section */}\n                    <div className=\"mb-6 bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-lg\">\n                        <div className=\"flex flex-col gap-4\">\n                            {/* Top Row - Search and Quick Filters */}\n                            <div className=\"flex flex-col sm:flex-row gap-4\">\n                                {/* Search Input */}\n                                <div className=\"flex-1 relative\">\n                                    <TbSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                                    <input\n                                        id=\"ranking-search\"\n                                        name=\"ranking-search\"\n                                        type=\"text\"\n                                        placeholder=\"Search by name, email, or class...\"\n                                        value={searchTerm}\n                                        onChange={(e) => setSearchTerm(e.target.value)}\n                                        autoComplete=\"off\"\n                                        className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n                                    />\n                                    {searchTerm && (\n                                        <button\n                                            onClick={() => setSearchTerm('')}\n                                            className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                                        >\n                                            ✕\n                                        </button>\n                                    )}\n                                </div>\n\n                                {/* Quick Filter Buttons */}\n                                <div className=\"flex gap-2\">\n                                    <button\n                                        onClick={() => setShowOnlyMyClass(!showOnlyMyClass)}\n                                        className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${\n                                            showOnlyMyClass\n                                                ? 'bg-blue-500 text-white shadow-md'\n                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                                        }`}\n                                    >\n                                        My Class Only\n                                    </button>\n                                </div>\n                            </div>\n\n                            {/* Bottom Row - Dropdowns and View Mode */}\n                            <div className=\"flex flex-col sm:flex-row gap-4\">\n                                {/* Filter Dropdown */}\n                                <div className=\"relative\">\n                                    <TbFilter className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                                    <select\n                                        id=\"ranking-filter\"\n                                        name=\"ranking-filter\"\n                                        value={filterType}\n                                        onChange={(e) => setFilterType(e.target.value)}\n                                        autoComplete=\"off\"\n                                        className=\"pl-10 pr-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n                                    >\n                                        <option value=\"all\">All Subscriptions</option>\n                                        <option value=\"premium\">👑 Premium Users</option>\n                                        <option value=\"free\">🆓 Free Users</option>\n                                        <option value=\"expired\">⏰ Expired Users</option>\n                                    </select>\n                                </div>\n\n                                {/* Sort Dropdown */}\n                                <select\n                                    id=\"ranking-sort\"\n                                    name=\"ranking-sort\"\n                                    value={sortBy}\n                                    onChange={(e) => setSortBy(e.target.value)}\n                                    autoComplete=\"off\"\n                                    className=\"px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n                                >\n                                    <option value=\"rank\">🏆 Sort by Rank</option>\n                                    <option value=\"xp\">⚡ Sort by XP</option>\n                                    <option value=\"score\">📊 Sort by Score</option>\n                                    <option value=\"name\">📝 Sort by Name</option>\n                                    <option value=\"class\">🎓 Sort by Class</option>\n                                </select>\n\n                                {/* View Mode Toggle */}\n                                <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                                    <button\n                                        onClick={() => setViewMode('card')}\n                                        className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${\n                                            viewMode === 'card'\n                                                ? 'bg-white text-blue-600 shadow-sm'\n                                                : 'text-gray-600 hover:text-gray-800'\n                                        }`}\n                                    >\n                                        Cards\n                                    </button>\n                                    <button\n                                        onClick={() => setViewMode('compact')}\n                                        className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${\n                                            viewMode === 'compact'\n                                                ? 'bg-white text-blue-600 shadow-sm'\n                                                : 'text-gray-600 hover:text-gray-800'\n                                        }`}\n                                    >\n                                        Compact\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n\n                        {/* Enhanced Results Count and Stats */}\n                        <div className=\"mt-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2\">\n                            <div className=\"text-sm text-gray-600\">\n                                <span className=\"font-semibold text-gray-800\">\n                                    {usersWithClassRank.length}\n                                </span> of <span className=\"font-semibold text-gray-800\">{users.length}</span> users\n                                {searchTerm && (\n                                    <span className=\"ml-1\">\n                                        matching <span className=\"font-medium text-blue-600\">\"{searchTerm}\"</span>\n                                    </span>\n                                )}\n                                {filterType !== 'all' && (\n                                    <span className=\"ml-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium\">\n                                        {filterType}\n                                    </span>\n                                )}\n                                {showOnlyMyClass && currentUserClass && (\n                                    <span className=\"ml-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium\">\n                                        Class {currentUserClass}\n                                    </span>\n                                )}\n                            </div>\n\n                            {/* Quick Stats */}\n                            <div className=\"flex gap-4 text-xs text-gray-500\">\n                                <span>🏆 Top: {Math.max(...usersWithClassRank.map(u => u.rankingScore || u.totalXP || 0)).toLocaleString()}</span>\n                                <span>📊 Avg: {Math.round(usersWithClassRank.reduce((sum, u) => sum + (u.rankingScore || u.totalXP || 0), 0) / usersWithClassRank.length || 0).toLocaleString()}</span>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"flex items-center justify-between mb-6\">\n                        <div className=\"flex items-center space-x-4\">\n                            <div className=\"flex items-center space-x-3\">\n                                <div className=\"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg\">\n                                    <TbTrophy className=\"w-7 h-7 text-white\" />\n                                </div>\n                                <div>\n                                    <h2 className=\"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent\">\n                                        Leaderboard\n                                    </h2>\n                                    <p className=\"text-sm text-gray-600 font-medium\">Top performers across all levels</p>\n                                </div>\n                            </div>\n\n                            {lastUpdated && (\n                                <div className=\"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200\">\n                                    <TbClock className=\"w-4 h-4 text-blue-600\" />\n                                    <span className=\"text-sm text-blue-700 font-medium\">\n                                        Updated {new Date(lastUpdated).toLocaleTimeString()}\n                                    </span>\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"flex items-center space-x-2\">\n                            {/* Auto-refresh toggle */}\n                            {onAutoRefreshToggle && (\n                                <button\n                                    onClick={onAutoRefreshToggle}\n                                    className={`px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 ${\n                                        autoRefresh\n                                            ? 'bg-green-500 hover:bg-green-600 text-white'\n                                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'\n                                    }`}\n                                >\n                                    {autoRefresh ? <TbPlayerPause className=\"w-4 h-4\" /> : <TbPlayerPlay className=\"w-4 h-4\" />}\n                                    <span className=\"hidden sm:inline\">\n                                        {autoRefresh ? 'Auto' : 'Manual'}\n                                    </span>\n                                </button>\n                            )}\n\n\n\n                            {/* Find Me button */}\n                            {currentUserId && (\n                                <button\n                                    onClick={scrollToCurrentUser}\n                                    className=\"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95\"\n                                >\n                                    <TbUser className=\"w-4 h-4\" />\n                                    <span className=\"hidden sm:inline\">Find Me</span>\n                                </button>\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4\">\n                        <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-blue-500 rounded-lg\">\n                                    <TbUsers className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-blue-700\">Total Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-blue-900 mb-1\">{totalUsers}</div>\n                            <div className=\"text-xs text-blue-600 font-medium\">{activeUsers} active</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-yellow-700\">Premium Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-yellow-900 mb-1\">{premiumUsers}</div>\n                            <div className=\"text-xs text-yellow-600 font-medium\">\n                                {totalUsers > 0 ? Math.round((premiumUsers / totalUsers) * 100) : 0}% premium\n                            </div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-green-500 rounded-lg\">\n                                    <TbUser className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-green-700\">Top Score</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-green-900 mb-1\">{topScore.toLocaleString()}</div>\n                            <div className=\"text-xs text-green-600 font-medium\">ranking points</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-purple-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-purple-700\">Avg XP</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-purple-900 mb-1\">{averageXP.toLocaleString()}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">experience points</div>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* User List */}\n            <div className={`animate-fadeInUp ${getLayoutClasses()}`}>\n                {usersWithClassRank.map((user, index) => {\n                    const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n                    const rank = user.rank || index + 1;\n\n                    // Render compact view for better performance with large lists\n                    if (viewMode === 'compact') {\n                        return (\n                            <div\n                                key={user.userId || user._id}\n                                ref={isCurrentUser ? userRef : null}\n                                className={`animate-slideInLeft transition-all duration-200 p-3 rounded-lg border ${\n                                    isCurrentUser && findMeActive\n                                        ? 'find-me-highlight ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200'\n                                        : isCurrentUser\n                                        ? 'ring-2 ring-blue-400 bg-blue-50 border-blue-200'\n                                        : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-md'\n                                }`}\n                            >\n                                <div className=\"flex items-center justify-between\">\n                                    <div className=\"flex items-center space-x-3\">\n                                        {/* Rank Badge */}\n                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${\n                                            rank <= 3 ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white' :\n                                            rank <= 10 ? 'bg-gradient-to-r from-blue-400 to-blue-500 text-white' :\n                                            'bg-gray-100 text-gray-600'\n                                        }`}>\n                                            {rank}\n                                        </div>\n\n                                        {/* User Info */}\n                                        <div className=\"flex items-center space-x-2\">\n                                            <img\n                                                src={user.profilePicture || '/default-avatar.png'}\n                                                alt={user.name}\n                                                className=\"w-8 h-8 rounded-full object-cover border-2 border-gray-200\"\n                                            />\n                                            <div>\n                                                <div className=\"font-semibold text-gray-900 text-sm\">{user.name}</div>\n                                                <div className=\"text-xs text-gray-500\">Class {user.class}</div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    {/* Score and Badge */}\n                                    <div className=\"flex items-center space-x-3\">\n                                        <div className=\"text-right\">\n                                            <div className=\"font-bold text-gray-900 text-sm\">\n                                                {(user.rankingScore || user.totalXP || 0).toLocaleString()}\n                                            </div>\n                                            <div className=\"text-xs text-gray-500\">\n                                                {user.rankingScore ? 'pts' : 'XP'}\n                                            </div>\n                                        </div>\n\n                                        {/* Subscription Badge */}\n                                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${\n                                            (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'premium' ||\n                                            (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'active'\n                                                ? 'bg-yellow-100 text-yellow-800'\n                                                : (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'expired'\n                                                ? 'bg-red-100 text-red-800'\n                                                : 'bg-blue-100 text-blue-800'\n                                        }`}>\n                                            {(user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'premium' ||\n                                             (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'active' ? '👑' :\n                                             (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'expired' ? '⏰' : '🆓'}\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        );\n                    }\n\n                    // Regular card view\n                    return (\n                        <div\n                            key={user.userId || user._id}\n                            ref={isCurrentUser ? userRef : null}\n                            className={`animate-slideInLeft transition-all duration-300 ${\n                                isCurrentUser && findMeActive\n                                    ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl'\n                                    : isCurrentUser\n                                    ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg'\n                                        : ''\n                                }`}\n                            >\n                                <UserRankingCard\n                                    user={user}\n                                    rank={rank}\n                                    classRank={user.classRank}\n                                    isCurrentUser={isCurrentUser}\n                                    layout={layout}\n                                    size={size}\n                                    showStats={showStats}\n                                />\n                            </div>\n                        );\n                    })}\n            </div>\n\n            {/* Empty State */}\n            {usersWithClassRank.length === 0 && (\n                <div className=\"text-center py-12 bg-white rounded-xl border border-gray-200 animate-fadeInUp\">\n                    <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No users found</h3>\n                    <p className=\"text-gray-500\">\n                        {users.length === 0 ? 'No ranking data available.' : 'Try adjusting your search or filter criteria.'}\n                    </p>\n                </div>\n            )}\n\n            {/* Floating Action Button for Current User */}\n            {currentUserId && usersWithClassRank.length > 10 && (\n                <button\n                    onClick={scrollToCurrentUser}\n                    className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 hover:scale-110 active:scale-95 animate-bounce\"\n                    title=\"Find me in ranking\"\n                >\n                    <TbUser className=\"w-6 h-6\" />\n                </button>\n            )}\n        </div>\n    );\n};\n\nexport default UserRankingList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACpH,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,eAAe,GAAGA,CAAC;EACrBC,KAAK,GAAG,EAAE;EACVC,aAAa,GAAG,IAAI;EACpBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG,EAAE;EACdC,cAAc,GAAG,IAAI;EACrBC,UAAU,GAAG,KAAK;EAClBC,WAAW,GAAG,IAAI;EAClBC,WAAW,GAAG,KAAK;EACnBC,mBAAmB,GAAG;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAClD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMsC,mBAAmB,GAAGrC,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMsC,OAAO,GAAGnB,cAAc,IAAIkB,mBAAmB;EACrD,MAAME,YAAY,GAAGnB,UAAU,IAAIe,eAAe;;EAElD;EACA,MAAMK,WAAW,GAAG3B,KAAK,CAAC4B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK7B,aAAa,IAAI4B,IAAI,CAACE,GAAG,KAAK9B,aAAa,CAAC;EACnG,MAAM+B,gBAAgB,GAAGL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,KAAK;;EAE3C;EACA,MAAMC,aAAa,GAAGlC,KAAK,CAACmC,MAAM,CAACN,IAAI,IAAI;IAAA,IAAAO,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACvC;IACA,MAAMC,aAAa,GAAG,EAAAL,UAAA,GAAAP,IAAI,CAACa,IAAI,cAAAN,UAAA,uBAATA,UAAA,CAAWO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC,OAAAN,WAAA,GAC7DR,IAAI,CAACgB,KAAK,cAAAR,WAAA,uBAAVA,WAAA,CAAYM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC,OAAAL,WAAA,GAC5DT,IAAI,CAACI,KAAK,cAAAK,WAAA,uBAAVA,WAAA,CAAYK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC;;IAEhF;IACA,MAAMG,YAAY,GAAG,CAAC1B,eAAe,IAAIS,IAAI,CAACI,KAAK,KAAKD,gBAAgB;;IAExE;IACA,MAAMe,UAAU,GAAG,EAAAR,qBAAA,GAAAV,IAAI,CAACmB,4BAA4B,cAAAT,qBAAA,uBAAjCA,qBAAA,CAAmCI,WAAW,CAAC,CAAC,OAAAH,qBAAA,GAAIX,IAAI,CAACoB,kBAAkB,cAAAT,qBAAA,uBAAvBA,qBAAA,CAAyBG,WAAW,CAAC,CAAC,KAAI,MAAM;IACvH,IAAIO,aAAa,GAAG,IAAI;IAExB,QAAQpC,UAAU;MACd,KAAK,SAAS;QACVoC,aAAa,GAAGH,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,QAAQ;QACnE;MACJ,KAAK,SAAS;QACVG,aAAa,GAAGH,UAAU,KAAK,SAAS;QACxC;MACJ,KAAK,MAAM;QACPG,aAAa,GAAGH,UAAU,KAAK,MAAM;QACrC;MACJ;QACIG,aAAa,GAAG,IAAI;IAC5B;IAEA,OAAOT,aAAa,IAAIS,aAAa,IAAIJ,YAAY;EACzD,CAAC,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACd,QAAQrC,MAAM;MACV,KAAK,IAAI;QACL,OAAO,CAACqC,CAAC,CAACC,OAAO,IAAI,CAAC,KAAKF,CAAC,CAACE,OAAO,IAAI,CAAC,CAAC;MAC9C,KAAK,MAAM;QACP,OAAO,CAACF,CAAC,CAACV,IAAI,IAAI,EAAE,EAAEa,aAAa,CAACF,CAAC,CAACX,IAAI,IAAI,EAAE,CAAC;MACrD,KAAK,OAAO;QACR,OAAO,CAACW,CAAC,CAACG,YAAY,IAAIH,CAAC,CAACI,KAAK,IAAI,CAAC,KAAKL,CAAC,CAACI,YAAY,IAAIJ,CAAC,CAACK,KAAK,IAAI,CAAC,CAAC;MAC9E,KAAK,OAAO;QACR,OAAO,CAACL,CAAC,CAACnB,KAAK,IAAI,EAAE,EAAEsB,aAAa,CAACF,CAAC,CAACpB,KAAK,IAAI,EAAE,CAAC;MACvD;QACI,OAAO,CAACmB,CAAC,CAACM,IAAI,IAAI,CAAC,KAAKL,CAAC,CAACK,IAAI,IAAI,CAAC,CAAC;IAC5C;EACJ,CAAC,CAAC;;EAEF;EACA,MAAMC,kBAAkB,GAAGzB,aAAa,CAAC0B,GAAG,CAAC/B,IAAI,IAAI;IACjD;IACA,MAAMgC,cAAc,GAAG3B,aAAa,CAACC,MAAM,CAAC2B,CAAC,IAAIA,CAAC,CAAC7B,KAAK,KAAKJ,IAAI,CAACI,KAAK,CAAC;IACxE,MAAM8B,SAAS,GAAGF,cAAc,CAACG,SAAS,CAACF,CAAC,IAAIA,CAAC,CAAC/B,GAAG,KAAKF,IAAI,CAACE,GAAG,IAAI+B,CAAC,CAAChC,MAAM,KAAKD,IAAI,CAACC,MAAM,CAAC,GAAG,CAAC;IACnG,OAAO;MAAE,GAAGD,IAAI;MAAEkC;IAAU,CAAC;EACjC,CAAC,CAAC;;EAEF;EACA,MAAME,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIxC,OAAO,CAACyC,OAAO,EAAE;MACjBzC,OAAO,CAACyC,OAAO,CAACC,cAAc,CAAC;QAC3BC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACF9C,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACA+C,UAAU,CAAC,MAAM/C,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACrD;EACJ,CAAC;;EAED;EACA,MAAMgD,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAIrD,QAAQ,KAAK,SAAS,EAAE;MACxB,OAAO,WAAW;IACtB;IAEA,QAAQhB,MAAM;MACV,KAAK,UAAU;QACX,OAAO,qEAAqE;MAChF,KAAK,MAAM;QACP,OAAO,sDAAsD;MACjE,KAAK,YAAY;MACjB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;;EAID;EACA,MAAMsE,UAAU,GAAGxE,KAAK,CAACyE,MAAM;EAC/B,MAAMC,YAAY,GAAG1E,KAAK,CAACmC,MAAM,CAAC2B,CAAC,IAC/BA,CAAC,CAACb,kBAAkB,KAAK,QAAQ,IACjCa,CAAC,CAACb,kBAAkB,KAAK,SAAS,IAClCa,CAAC,CAACd,4BAA4B,KAAK,SACvC,CAAC,CAACyB,MAAM;;EAER;EACA,MAAME,QAAQ,GAAG3E,KAAK,CAACyE,MAAM,GAAG,CAAC,GAAGG,IAAI,CAACC,GAAG,CAAC,GAAG7E,KAAK,CAAC4D,GAAG,CAACE,CAAC,IACvDA,CAAC,CAACN,YAAY,IAAIM,CAAC,CAACR,OAAO,IAAIQ,CAAC,CAACgB,WAAW,IAAI,CACpD,CAAC,CAAC,GAAG,CAAC;;EAEN;EACA,MAAMC,WAAW,GAAG/E,KAAK,CAACmC,MAAM,CAAC2B,CAAC,IAAI,CAACA,CAAC,CAACkB,iBAAiB,IAAI,CAAC,IAAI,CAAC,CAAC,CAACP,MAAM;EAC5E,MAAMQ,SAAS,GAAGjF,KAAK,CAACyE,MAAM,GAAG,CAAC,GAC9BG,IAAI,CAACM,KAAK,CAAClF,KAAK,CAACmF,MAAM,CAAC,CAACC,GAAG,EAAEtB,CAAC,KAAKsB,GAAG,IAAItB,CAAC,CAACR,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGtD,KAAK,CAACyE,MAAM,CAAC,GAAG,CAAC;EAEtF,oBACI3E,OAAA;IAAKO,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAAgF,QAAA,GAEpCjF,SAAS,iBACNN,OAAA;MAAKO,SAAS,EAAC,mGAAmG;MAAAgF,QAAA,gBAE9GvF,OAAA;QAAKO,SAAS,EAAC,mFAAmF;QAAAgF,QAAA,gBAC9FvF,OAAA;UAAKO,SAAS,EAAC,qBAAqB;UAAAgF,QAAA,gBAEhCvF,OAAA;YAAKO,SAAS,EAAC,iCAAiC;YAAAgF,QAAA,gBAE5CvF,OAAA;cAAKO,SAAS,EAAC,iBAAiB;cAAAgF,QAAA,gBAC5BvF,OAAA,CAACJ,QAAQ;gBAACW,SAAS,EAAC;cAA0E;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjG3F,OAAA;gBACI4F,EAAE,EAAC,gBAAgB;gBACnBhD,IAAI,EAAC,gBAAgB;gBACrBiD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oCAAoC;gBAChDC,KAAK,EAAEjF,UAAW;gBAClBkF,QAAQ,EAAGC,CAAC,IAAKlF,aAAa,CAACkF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CI,YAAY,EAAC,KAAK;gBAClB5F,SAAS,EAAC;cAAmK;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChL,CAAC,EACD7E,UAAU,iBACPd,OAAA;gBACIoG,OAAO,EAAEA,CAAA,KAAMrF,aAAa,CAAC,EAAE,CAAE;gBACjCR,SAAS,EAAC,uFAAuF;gBAAAgF,QAAA,EACpG;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGN3F,OAAA;cAAKO,SAAS,EAAC,YAAY;cAAAgF,QAAA,eACvBvF,OAAA;gBACIoG,OAAO,EAAEA,CAAA,KAAM7E,kBAAkB,CAAC,CAACD,eAAe,CAAE;gBACpDf,SAAS,EAAG,gEACRe,eAAe,GACT,kCAAkC,GAClC,6CACT,EAAE;gBAAAiE,QAAA,EACN;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN3F,OAAA;YAAKO,SAAS,EAAC,iCAAiC;YAAAgF,QAAA,gBAE5CvF,OAAA;cAAKO,SAAS,EAAC,UAAU;cAAAgF,QAAA,gBACrBvF,OAAA,CAACH,QAAQ;gBAACU,SAAS,EAAC;cAA0E;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjG3F,OAAA;gBACI4F,EAAE,EAAC,gBAAgB;gBACnBhD,IAAI,EAAC,gBAAgB;gBACrBmD,KAAK,EAAE/E,UAAW;gBAClBgF,QAAQ,EAAGC,CAAC,IAAKhF,aAAa,CAACgF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CI,YAAY,EAAC,KAAK;gBAClB5F,SAAS,EAAC,4JAA4J;gBAAAgF,QAAA,gBAEtKvF,OAAA;kBAAQ+F,KAAK,EAAC,KAAK;kBAAAR,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9C3F,OAAA;kBAAQ+F,KAAK,EAAC,SAAS;kBAAAR,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjD3F,OAAA;kBAAQ+F,KAAK,EAAC,MAAM;kBAAAR,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C3F,OAAA;kBAAQ+F,KAAK,EAAC,SAAS;kBAAAR,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGN3F,OAAA;cACI4F,EAAE,EAAC,cAAc;cACjBhD,IAAI,EAAC,cAAc;cACnBmD,KAAK,EAAE7E,MAAO;cACd8E,QAAQ,EAAGC,CAAC,IAAK9E,SAAS,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC3CI,YAAY,EAAC,KAAK;cAClB5F,SAAS,EAAC,sJAAsJ;cAAAgF,QAAA,gBAEhKvF,OAAA;gBAAQ+F,KAAK,EAAC,MAAM;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C3F,OAAA;gBAAQ+F,KAAK,EAAC,IAAI;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC3F,OAAA;gBAAQ+F,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/C3F,OAAA;gBAAQ+F,KAAK,EAAC,MAAM;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C3F,OAAA;gBAAQ+F,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eAGT3F,OAAA;cAAKO,SAAS,EAAC,iCAAiC;cAAAgF,QAAA,gBAC5CvF,OAAA;gBACIoG,OAAO,EAAEA,CAAA,KAAM/E,WAAW,CAAC,MAAM,CAAE;gBACnCd,SAAS,EAAG,wEACRa,QAAQ,KAAK,MAAM,GACb,kCAAkC,GAClC,mCACT,EAAE;gBAAAmE,QAAA,EACN;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT3F,OAAA;gBACIoG,OAAO,EAAEA,CAAA,KAAM/E,WAAW,CAAC,SAAS,CAAE;gBACtCd,SAAS,EAAG,wEACRa,QAAQ,KAAK,SAAS,GAChB,kCAAkC,GAClC,mCACT,EAAE;gBAAAmE,QAAA,EACN;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN3F,OAAA;UAAKO,SAAS,EAAC,kFAAkF;UAAAgF,QAAA,gBAC7FvF,OAAA;YAAKO,SAAS,EAAC,uBAAuB;YAAAgF,QAAA,gBAClCvF,OAAA;cAAMO,SAAS,EAAC,6BAA6B;cAAAgF,QAAA,EACxC1B,kBAAkB,CAACc;YAAM;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,QAAI,eAAA3F,OAAA;cAAMO,SAAS,EAAC,6BAA6B;cAAAgF,QAAA,EAAErF,KAAK,CAACyE;YAAM;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,UAC9E,EAAC7E,UAAU,iBACPd,OAAA;cAAMO,SAAS,EAAC,MAAM;cAAAgF,QAAA,GAAC,WACV,eAAAvF,OAAA;gBAAMO,SAAS,EAAC,2BAA2B;gBAAAgF,QAAA,GAAC,IAAC,EAACzE,UAAU,EAAC,IAAC;cAAA;gBAAA0E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CACT,EACA3E,UAAU,KAAK,KAAK,iBACjBhB,OAAA;cAAMO,SAAS,EAAC,2EAA2E;cAAAgF,QAAA,EACtFvE;YAAU;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACT,EACArE,eAAe,IAAIY,gBAAgB,iBAChClC,OAAA;cAAMO,SAAS,EAAC,6EAA6E;cAAAgF,QAAA,GAAC,QACpF,EAACrD,gBAAgB;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGN3F,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAgF,QAAA,gBAC7CvF,OAAA;cAAAuF,QAAA,GAAM,oBAAQ,EAACT,IAAI,CAACC,GAAG,CAAC,GAAGlB,kBAAkB,CAACC,GAAG,CAACE,CAAC,IAAIA,CAAC,CAACN,YAAY,IAAIM,CAAC,CAACR,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC6C,cAAc,CAAC,CAAC;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClH3F,OAAA;cAAAuF,QAAA,GAAM,oBAAQ,EAACT,IAAI,CAACM,KAAK,CAACvB,kBAAkB,CAACwB,MAAM,CAAC,CAACC,GAAG,EAAEtB,CAAC,KAAKsB,GAAG,IAAItB,CAAC,CAACN,YAAY,IAAIM,CAAC,CAACR,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGK,kBAAkB,CAACc,MAAM,IAAI,CAAC,CAAC,CAAC0B,cAAc,CAAC,CAAC;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN3F,OAAA;QAAKO,SAAS,EAAC,wCAAwC;QAAAgF,QAAA,gBACnDvF,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAAgF,QAAA,gBACxCvF,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAgF,QAAA,gBACxCvF,OAAA;cAAKO,SAAS,EAAC,yFAAyF;cAAAgF,QAAA,eACpGvF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN3F,OAAA;cAAAuF,QAAA,gBACIvF,OAAA;gBAAIO,SAAS,EAAC,2HAA2H;gBAAAgF,QAAA,EAAC;cAE1I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3F,OAAA;gBAAGO,SAAS,EAAC,mCAAmC;gBAAAgF,QAAA,EAAC;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAELjF,WAAW,iBACRV,OAAA;YAAKO,SAAS,EAAC,oFAAoF;YAAAgF,QAAA,gBAC/FvF,OAAA,CAACL,OAAO;cAACY,SAAS,EAAC;YAAuB;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C3F,OAAA;cAAMO,SAAS,EAAC,mCAAmC;cAAAgF,QAAA,GAAC,UACxC,EAAC,IAAIe,IAAI,CAAC5F,WAAW,CAAC,CAAC6F,kBAAkB,CAAC,CAAC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEN3F,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAAgF,QAAA,GAEvC3E,mBAAmB,iBAChBZ,OAAA;YACIoG,OAAO,EAAExF,mBAAoB;YAC7BL,SAAS,EAAG,4HACRI,WAAW,GACL,4CAA4C,GAC5C,6CACT,EAAE;YAAA4E,QAAA,GAEF5E,WAAW,gBAAGX,OAAA,CAACN,aAAa;cAACa,SAAS,EAAC;YAAS;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG3F,OAAA,CAACP,YAAY;cAACc,SAAS,EAAC;YAAS;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3F3F,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAAgF,QAAA,EAC7B5E,WAAW,GAAG,MAAM,GAAG;YAAQ;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACX,EAKAxF,aAAa,iBACVH,OAAA;YACIoG,OAAO,EAAEjC,mBAAoB;YAC7B5D,SAAS,EAAC,uKAAuK;YAAAgF,QAAA,gBAEjLvF,OAAA,CAACV,MAAM;cAACiB,SAAS,EAAC;YAAS;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B3F,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAAgF,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN3F,OAAA;QAAKO,SAAS,EAAC,uCAAuC;QAAAgF,QAAA,gBAClDvF,OAAA;UAAKO,SAAS,EAAC,yJAAyJ;UAAAgF,QAAA,gBACpKvF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAgF,QAAA,gBAC7CvF,OAAA;cAAKO,SAAS,EAAC,4BAA4B;cAAAgF,QAAA,eACvCvF,OAAA,CAACT,OAAO;gBAACgB,SAAS,EAAC;cAAoB;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACN3F,OAAA;cAAMO,SAAS,EAAC,qCAAqC;cAAAgF,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACN3F,OAAA;YAAKO,SAAS,EAAC,wCAAwC;YAAAgF,QAAA,EAAEb;UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1E3F,OAAA;YAAKO,SAAS,EAAC,mCAAmC;YAAAgF,QAAA,GAAEN,WAAW,EAAC,SAAO;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAEN3F,OAAA;UAAKO,SAAS,EAAC,6JAA6J;UAAAgF,QAAA,gBACxKvF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAgF,QAAA,gBAC7CvF,OAAA;cAAKO,SAAS,EAAC,+DAA+D;cAAAgF,QAAA,eAC1EvF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN3F,OAAA;cAAMO,SAAS,EAAC,uCAAuC;cAAAgF,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACN3F,OAAA;YAAKO,SAAS,EAAC,0CAA0C;YAAAgF,QAAA,EAAEX;UAAY;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9E3F,OAAA;YAAKO,SAAS,EAAC,qCAAqC;YAAAgF,QAAA,GAC/Cb,UAAU,GAAG,CAAC,GAAGI,IAAI,CAACM,KAAK,CAAER,YAAY,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,WACxE;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN3F,OAAA;UAAKO,SAAS,EAAC,4JAA4J;UAAAgF,QAAA,gBACvKvF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAgF,QAAA,gBAC7CvF,OAAA;cAAKO,SAAS,EAAC,6BAA6B;cAAAgF,QAAA,eACxCvF,OAAA,CAACV,MAAM;gBAACiB,SAAS,EAAC;cAAoB;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN3F,OAAA;cAAMO,SAAS,EAAC,sCAAsC;cAAAgF,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACN3F,OAAA;YAAKO,SAAS,EAAC,yCAAyC;YAAAgF,QAAA,EAAEV,QAAQ,CAACwB,cAAc,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1F3F,OAAA;YAAKO,SAAS,EAAC,oCAAoC;YAAAgF,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAEN3F,OAAA;UAAKO,SAAS,EAAC,2JAA2J;UAAAgF,QAAA,gBACtKvF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAgF,QAAA,gBAC7CvF,OAAA;cAAKO,SAAS,EAAC,8BAA8B;cAAAgF,QAAA,eACzCvF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN3F,OAAA;cAAMO,SAAS,EAAC,uCAAuC;cAAAgF,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACN3F,OAAA;YAAKO,SAAS,EAAC,0CAA0C;YAAAgF,QAAA,EAAEJ,SAAS,CAACkB,cAAc,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5F3F,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAAgF,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAGD3F,OAAA;MAAKO,SAAS,EAAG,oBAAmBkE,gBAAgB,CAAC,CAAE,EAAE;MAAAc,QAAA,EACpD1B,kBAAkB,CAACC,GAAG,CAAC,CAAC/B,IAAI,EAAEyE,KAAK,KAAK;QACrC,MAAMC,aAAa,GAAG1E,IAAI,CAACC,MAAM,KAAK7B,aAAa,IAAI4B,IAAI,CAACE,GAAG,KAAK9B,aAAa;QACjF,MAAMyD,IAAI,GAAG7B,IAAI,CAAC6B,IAAI,IAAI4C,KAAK,GAAG,CAAC;;QAEnC;QACA,IAAIpF,QAAQ,KAAK,SAAS,EAAE;UAAA,IAAAsF,IAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA;UACxB,oBACI/G,OAAA;YAEIgH,GAAG,EAAEP,aAAa,GAAG9E,OAAO,GAAG,IAAK;YACpCpB,SAAS,EAAG,yEACRkG,aAAa,IAAI7E,YAAY,GACvB,yGAAyG,GACzG6E,aAAa,GACb,iDAAiD,GACjD,gEACT,EAAE;YAAAlB,QAAA,eAEHvF,OAAA;cAAKO,SAAS,EAAC,mCAAmC;cAAAgF,QAAA,gBAC9CvF,OAAA;gBAAKO,SAAS,EAAC,6BAA6B;gBAAAgF,QAAA,gBAExCvF,OAAA;kBAAKO,SAAS,EAAG,2EACbqD,IAAI,IAAI,CAAC,GAAG,2DAA2D,GACvEA,IAAI,IAAI,EAAE,GAAG,uDAAuD,GACpE,2BACH,EAAE;kBAAA2B,QAAA,EACE3B;gBAAI;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAGN3F,OAAA;kBAAKO,SAAS,EAAC,6BAA6B;kBAAAgF,QAAA,gBACxCvF,OAAA;oBACIiH,GAAG,EAAElF,IAAI,CAACmF,cAAc,IAAI,qBAAsB;oBAClDC,GAAG,EAAEpF,IAAI,CAACa,IAAK;oBACfrC,SAAS,EAAC;kBAA4D;oBAAAiF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACF3F,OAAA;oBAAAuF,QAAA,gBACIvF,OAAA;sBAAKO,SAAS,EAAC,qCAAqC;sBAAAgF,QAAA,EAAExD,IAAI,CAACa;oBAAI;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtE3F,OAAA;sBAAKO,SAAS,EAAC,uBAAuB;sBAAAgF,QAAA,GAAC,QAAM,EAACxD,IAAI,CAACI,KAAK;oBAAA;sBAAAqD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN3F,OAAA;gBAAKO,SAAS,EAAC,6BAA6B;gBAAAgF,QAAA,gBACxCvF,OAAA;kBAAKO,SAAS,EAAC,YAAY;kBAAAgF,QAAA,gBACvBvF,OAAA;oBAAKO,SAAS,EAAC,iCAAiC;oBAAAgF,QAAA,EAC3C,CAACxD,IAAI,CAAC2B,YAAY,IAAI3B,IAAI,CAACyB,OAAO,IAAI,CAAC,EAAE6C,cAAc,CAAC;kBAAC;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACN3F,OAAA;oBAAKO,SAAS,EAAC,uBAAuB;oBAAAgF,QAAA,EACjCxD,IAAI,CAAC2B,YAAY,GAAG,KAAK,GAAG;kBAAI;oBAAA8B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAGN3F,OAAA;kBAAKO,SAAS,EAAG,8CACb,EAAAmG,IAAA,GAAC3E,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAAuD,IAAA,uBAA7DA,IAAA,CAAgE7D,WAAW,CAAC,CAAC,MAAK,SAAS,IAC3F,EAAA8D,KAAA,GAAC5E,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAAwD,KAAA,uBAA7DA,KAAA,CAAgE9D,WAAW,CAAC,CAAC,MAAK,QAAQ,GACpF,+BAA+B,GAC/B,EAAA+D,KAAA,GAAC7E,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAAyD,KAAA,uBAA7DA,KAAA,CAAgE/D,WAAW,CAAC,CAAC,MAAK,SAAS,GAC3F,yBAAyB,GACzB,2BACT,EAAE;kBAAA0C,QAAA,EACE,EAAAsB,KAAA,GAAC9E,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAA0D,KAAA,uBAA7DA,KAAA,CAAgEhE,WAAW,CAAC,CAAC,MAAK,SAAS,IAC3F,EAAAiE,KAAA,GAAC/E,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAA2D,KAAA,uBAA7DA,KAAA,CAAgEjE,WAAW,CAAC,CAAC,MAAK,QAAQ,GAAG,IAAI,GACjG,EAAAkE,KAAA,GAAChF,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAA4D,KAAA,uBAA7DA,KAAA,CAAgElE,WAAW,CAAC,CAAC,MAAK,SAAS,GAAG,GAAG,GAAG;gBAAI;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC,GA5DD5D,IAAI,CAACC,MAAM,IAAID,IAAI,CAACE,GAAG;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6D3B,CAAC;QAEd;;QAEA;QACA,oBACI3F,OAAA;UAEIgH,GAAG,EAAEP,aAAa,GAAG9E,OAAO,GAAG,IAAK;UACpCpB,SAAS,EAAG,mDACRkG,aAAa,IAAI7E,YAAY,GACvB,6GAA6G,GAC7G6E,aAAa,GACb,+CAA+C,GAC3C,EACT,EAAE;UAAAlB,QAAA,eAEHvF,OAAA,CAACF,eAAe;YACZiC,IAAI,EAAEA,IAAK;YACX6B,IAAI,EAAEA,IAAK;YACXK,SAAS,EAAElC,IAAI,CAACkC,SAAU;YAC1BwC,aAAa,EAAEA,aAAc;YAC7BrG,MAAM,EAAEA,MAAO;YACfC,IAAI,EAAEA,IAAK;YACXC,SAAS,EAAEA;UAAU;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC,GAlBD5D,IAAI,CAACC,MAAM,IAAID,IAAI,CAACE,GAAG;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBvB,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL9B,kBAAkB,CAACc,MAAM,KAAK,CAAC,iBAC5B3E,OAAA;MAAKO,SAAS,EAAC,+EAA+E;MAAAgF,QAAA,gBAC1FvF,OAAA,CAACT,OAAO;QAACgB,SAAS,EAAC;MAAsC;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5D3F,OAAA;QAAIO,SAAS,EAAC,0CAA0C;QAAAgF,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5E3F,OAAA;QAAGO,SAAS,EAAC,eAAe;QAAAgF,QAAA,EACvBrF,KAAK,CAACyE,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;MAA+C;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,EAGAxF,aAAa,IAAI0D,kBAAkB,CAACc,MAAM,GAAG,EAAE,iBAC5C3E,OAAA;MACIoG,OAAO,EAAEjC,mBAAoB;MAC7B5D,SAAS,EAAC,4LAA4L;MACtM6G,KAAK,EAAC,oBAAoB;MAAA7B,QAAA,eAE1BvF,OAAA,CAACV,MAAM;QAACiB,SAAS,EAAC;MAAS;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC9E,EAAA,CAxfIZ,eAAe;AAAAoH,EAAA,GAAfpH,eAAe;AA0frB,eAAeA,eAAe;AAAC,IAAAoH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}