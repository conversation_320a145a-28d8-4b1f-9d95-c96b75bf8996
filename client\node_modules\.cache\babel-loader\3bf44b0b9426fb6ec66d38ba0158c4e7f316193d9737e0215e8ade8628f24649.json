{"ast": null, "code": "import { message } from 'antd';\n\n/**\n * Check if the current session is valid\n * @returns {boolean} true if session is valid, false otherwise\n */\nexport const isSessionValid = () => {\n  const token = localStorage.getItem('token');\n  const userData = localStorage.getItem('user');\n  if (!token || !userData) {\n    return false;\n  }\n  try {\n    // Parse user data\n    const user = JSON.parse(userData);\n    if (!user._id) {\n      return false;\n    }\n\n    // Check token format and expiration\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    const currentTime = Date.now() / 1000;\n    if (payload.exp && payload.exp < currentTime) {\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error validating session:', error);\n    return false;\n  }\n};\n\n/**\n * Validate token format and content\n * @param {string} token - JWT token to validate\n * @returns {object} validation result with isValid and error\n */\nexport const validateTokenFormat = token => {\n  if (!token) {\n    return {\n      isValid: false,\n      error: 'No token provided'\n    };\n  }\n  try {\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n      return {\n        isValid: false,\n        error: 'Invalid token format - wrong number of parts'\n      };\n    }\n\n    // Try to decode the payload\n    const payload = JSON.parse(atob(parts[1]));\n    if (!payload.userId) {\n      return {\n        isValid: false,\n        error: 'Token missing userId'\n      };\n    }\n    const currentTime = Date.now() / 1000;\n    if (payload.exp && payload.exp < currentTime) {\n      return {\n        isValid: false,\n        error: 'Token expired'\n      };\n    }\n    return {\n      isValid: true,\n      payload,\n      expiresAt: payload.exp,\n      timeLeft: payload.exp - currentTime\n    };\n  } catch (error) {\n    return {\n      isValid: false,\n      error: `Token decode error: ${error.message}`\n    };\n  }\n};\n\n/**\n * Clean up invalid session data\n */\nexport const cleanupInvalidSession = () => {\n  console.log('🧹 Cleaning up invalid session data');\n  localStorage.removeItem('token');\n  localStorage.removeItem('user');\n};\n\n/**\n * Clear session data and redirect to login\n * @param {string} errorMessage - Optional error message to display\n */\nexport const clearSessionAndRedirect = (errorMessage = 'Your session has expired. Please login again.') => {\n  localStorage.removeItem('token');\n  localStorage.removeItem('user');\n  message.error(errorMessage);\n  setTimeout(() => {\n    window.location.href = '/login';\n  }, 1000);\n};\n\n/**\n * Validate session and redirect if invalid\n * @param {string} errorMessage - Optional error message to display\n * @returns {boolean} true if session is valid, false if redirected\n */\nexport const validateSessionOrRedirect = errorMessage => {\n  if (!isSessionValid()) {\n    clearSessionAndRedirect(errorMessage);\n    return false;\n  }\n  return true;\n};\n\n/**\n * Get current user data if session is valid\n * @returns {object|null} user data or null if session invalid\n */\nexport const getCurrentUser = () => {\n  if (!isSessionValid()) {\n    return null;\n  }\n  try {\n    return JSON.parse(localStorage.getItem('user'));\n  } catch (error) {\n    console.error('Error parsing user data:', error);\n    return null;\n  }\n};\n\n/**\n * Get current token if session is valid\n * @returns {string|null} token or null if session invalid\n */\nexport const getCurrentToken = () => {\n  if (!isSessionValid()) {\n    return null;\n  }\n  return localStorage.getItem('token');\n};\n\n/**\n * Check if token will expire soon (within next 5 minutes)\n * @returns {boolean} true if token expires soon\n */\nexport const isTokenExpiringSoon = () => {\n  const token = localStorage.getItem('token');\n  if (!token) {\n    return true;\n  }\n  try {\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    const currentTime = Date.now() / 1000;\n    const fiveMinutesFromNow = currentTime + 5 * 60; // 5 minutes\n\n    return payload.exp && payload.exp < fiveMinutesFromNow;\n  } catch (error) {\n    return true;\n  }\n};\n\n/**\n * Get token expiry information\n * @returns {object} token expiry details\n */\nexport const getTokenExpiryInfo = () => {\n  const token = localStorage.getItem('token');\n  if (!token) {\n    return {\n      expired: true,\n      timeLeft: 0\n    };\n  }\n  try {\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    const currentTime = Date.now() / 1000;\n    const timeLeft = payload.exp - currentTime;\n    return {\n      expired: timeLeft <= 0,\n      timeLeft: Math.max(0, timeLeft),\n      expiresAt: new Date(payload.exp * 1000),\n      needsRefresh: timeLeft < 3600,\n      // Less than 1 hour\n      formattedTimeLeft: formatTimeLeft(timeLeft)\n    };\n  } catch (error) {\n    return {\n      expired: true,\n      timeLeft: 0\n    };\n  }\n};\n\n/**\n * Format time left in human readable format\n * @param {number} seconds\n * @returns {string} formatted time\n */\nconst formatTimeLeft = seconds => {\n  if (seconds <= 0) return 'Expired';\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor(seconds % 3600 / 60);\n  if (hours > 0) {\n    return `${hours}h ${minutes}m`;\n  } else if (minutes > 0) {\n    return `${minutes}m`;\n  } else {\n    return 'Less than 1m';\n  }\n};", "map": {"version": 3, "names": ["message", "isSessionValid", "token", "localStorage", "getItem", "userData", "user", "JSON", "parse", "_id", "payload", "atob", "split", "currentTime", "Date", "now", "exp", "error", "console", "validateTokenFormat", "<PERSON><PERSON><PERSON><PERSON>", "parts", "length", "userId", "expiresAt", "timeLeft", "cleanupInvalidSession", "log", "removeItem", "clearSessionAndRedirect", "errorMessage", "setTimeout", "window", "location", "href", "validateSessionOrRedirect", "getCurrentUser", "getCurrentToken", "isTokenExpiringSoon", "fiveMinutesFromNow", "getTokenExpiryInfo", "expired", "Math", "max", "needsRefresh", "formattedTimeLeft", "formatTimeLeft", "seconds", "hours", "floor", "minutes"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/utils/authUtils.js"], "sourcesContent": ["import { message } from 'antd';\n\n/**\n * Check if the current session is valid\n * @returns {boolean} true if session is valid, false otherwise\n */\nexport const isSessionValid = () => {\n  const token = localStorage.getItem('token');\n  const userData = localStorage.getItem('user');\n\n  if (!token || !userData) {\n    return false;\n  }\n\n  try {\n    // Parse user data\n    const user = JSON.parse(userData);\n    if (!user._id) {\n      return false;\n    }\n\n    // Check token format and expiration\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    const currentTime = Date.now() / 1000;\n\n    if (payload.exp && payload.exp < currentTime) {\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error validating session:', error);\n    return false;\n  }\n};\n\n/**\n * Validate token format and content\n * @param {string} token - JWT token to validate\n * @returns {object} validation result with isValid and error\n */\nexport const validateTokenFormat = (token) => {\n  if (!token) {\n    return { isValid: false, error: 'No token provided' };\n  }\n\n  try {\n    const parts = token.split('.');\n    if (parts.length !== 3) {\n      return { isValid: false, error: 'Invalid token format - wrong number of parts' };\n    }\n\n    // Try to decode the payload\n    const payload = JSON.parse(atob(parts[1]));\n\n    if (!payload.userId) {\n      return { isValid: false, error: 'Token missing userId' };\n    }\n\n    const currentTime = Date.now() / 1000;\n    if (payload.exp && payload.exp < currentTime) {\n      return { isValid: false, error: 'Token expired' };\n    }\n\n    return {\n      isValid: true,\n      payload,\n      expiresAt: payload.exp,\n      timeLeft: payload.exp - currentTime\n    };\n  } catch (error) {\n    return { isValid: false, error: `Token decode error: ${error.message}` };\n  }\n};\n\n/**\n * Clean up invalid session data\n */\nexport const cleanupInvalidSession = () => {\n  console.log('🧹 Cleaning up invalid session data');\n  localStorage.removeItem('token');\n  localStorage.removeItem('user');\n};\n\n/**\n * Clear session data and redirect to login\n * @param {string} errorMessage - Optional error message to display\n */\nexport const clearSessionAndRedirect = (errorMessage = 'Your session has expired. Please login again.') => {\n  localStorage.removeItem('token');\n  localStorage.removeItem('user');\n  message.error(errorMessage);\n  \n  setTimeout(() => {\n    window.location.href = '/login';\n  }, 1000);\n};\n\n/**\n * Validate session and redirect if invalid\n * @param {string} errorMessage - Optional error message to display\n * @returns {boolean} true if session is valid, false if redirected\n */\nexport const validateSessionOrRedirect = (errorMessage) => {\n  if (!isSessionValid()) {\n    clearSessionAndRedirect(errorMessage);\n    return false;\n  }\n  return true;\n};\n\n/**\n * Get current user data if session is valid\n * @returns {object|null} user data or null if session invalid\n */\nexport const getCurrentUser = () => {\n  if (!isSessionValid()) {\n    return null;\n  }\n\n  try {\n    return JSON.parse(localStorage.getItem('user'));\n  } catch (error) {\n    console.error('Error parsing user data:', error);\n    return null;\n  }\n};\n\n/**\n * Get current token if session is valid\n * @returns {string|null} token or null if session invalid\n */\nexport const getCurrentToken = () => {\n  if (!isSessionValid()) {\n    return null;\n  }\n\n  return localStorage.getItem('token');\n};\n\n/**\n * Check if token will expire soon (within next 5 minutes)\n * @returns {boolean} true if token expires soon\n */\nexport const isTokenExpiringSoon = () => {\n  const token = localStorage.getItem('token');\n\n  if (!token) {\n    return true;\n  }\n\n  try {\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    const currentTime = Date.now() / 1000;\n    const fiveMinutesFromNow = currentTime + (5 * 60); // 5 minutes\n\n    return payload.exp && payload.exp < fiveMinutesFromNow;\n  } catch (error) {\n    return true;\n  }\n};\n\n/**\n * Get token expiry information\n * @returns {object} token expiry details\n */\nexport const getTokenExpiryInfo = () => {\n  const token = localStorage.getItem('token');\n\n  if (!token) {\n    return { expired: true, timeLeft: 0 };\n  }\n\n  try {\n    const payload = JSON.parse(atob(token.split('.')[1]));\n    const currentTime = Date.now() / 1000;\n    const timeLeft = payload.exp - currentTime;\n\n    return {\n      expired: timeLeft <= 0,\n      timeLeft: Math.max(0, timeLeft),\n      expiresAt: new Date(payload.exp * 1000),\n      needsRefresh: timeLeft < 3600, // Less than 1 hour\n      formattedTimeLeft: formatTimeLeft(timeLeft)\n    };\n  } catch (error) {\n    return { expired: true, timeLeft: 0 };\n  }\n};\n\n/**\n * Format time left in human readable format\n * @param {number} seconds\n * @returns {string} formatted time\n */\nconst formatTimeLeft = (seconds) => {\n  if (seconds <= 0) return 'Expired';\n\n  const hours = Math.floor(seconds / 3600);\n  const minutes = Math.floor((seconds % 3600) / 60);\n\n  if (hours > 0) {\n    return `${hours}h ${minutes}m`;\n  } else if (minutes > 0) {\n    return `${minutes}m`;\n  } else {\n    return 'Less than 1m';\n  }\n};\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,MAAM;;AAE9B;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAClC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EAE7C,IAAI,CAACF,KAAK,IAAI,CAACG,QAAQ,EAAE;IACvB,OAAO,KAAK;EACd;EAEA,IAAI;IACF;IACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;IACjC,IAAI,CAACC,IAAI,CAACG,GAAG,EAAE;MACb,OAAO,KAAK;IACd;;IAEA;IACA,MAAMC,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACG,IAAI,CAACT,KAAK,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;IAErC,IAAIL,OAAO,CAACM,GAAG,IAAIN,OAAO,CAACM,GAAG,GAAGH,WAAW,EAAE;MAC5C,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,KAAK;EACd;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,mBAAmB,GAAIjB,KAAK,IAAK;EAC5C,IAAI,CAACA,KAAK,EAAE;IACV,OAAO;MAAEkB,OAAO,EAAE,KAAK;MAAEH,KAAK,EAAE;IAAoB,CAAC;EACvD;EAEA,IAAI;IACF,MAAMI,KAAK,GAAGnB,KAAK,CAACU,KAAK,CAAC,GAAG,CAAC;IAC9B,IAAIS,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO;QAAEF,OAAO,EAAE,KAAK;QAAEH,KAAK,EAAE;MAA+C,CAAC;IAClF;;IAEA;IACA,MAAMP,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACG,IAAI,CAACU,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1C,IAAI,CAACX,OAAO,CAACa,MAAM,EAAE;MACnB,OAAO;QAAEH,OAAO,EAAE,KAAK;QAAEH,KAAK,EAAE;MAAuB,CAAC;IAC1D;IAEA,MAAMJ,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;IACrC,IAAIL,OAAO,CAACM,GAAG,IAAIN,OAAO,CAACM,GAAG,GAAGH,WAAW,EAAE;MAC5C,OAAO;QAAEO,OAAO,EAAE,KAAK;QAAEH,KAAK,EAAE;MAAgB,CAAC;IACnD;IAEA,OAAO;MACLG,OAAO,EAAE,IAAI;MACbV,OAAO;MACPc,SAAS,EAAEd,OAAO,CAACM,GAAG;MACtBS,QAAQ,EAAEf,OAAO,CAACM,GAAG,GAAGH;IAC1B,CAAC;EACH,CAAC,CAAC,OAAOI,KAAK,EAAE;IACd,OAAO;MAAEG,OAAO,EAAE,KAAK;MAAEH,KAAK,EAAG,uBAAsBA,KAAK,CAACjB,OAAQ;IAAE,CAAC;EAC1E;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM0B,qBAAqB,GAAGA,CAAA,KAAM;EACzCR,OAAO,CAACS,GAAG,CAAC,qCAAqC,CAAC;EAClDxB,YAAY,CAACyB,UAAU,CAAC,OAAO,CAAC;EAChCzB,YAAY,CAACyB,UAAU,CAAC,MAAM,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAGA,CAACC,YAAY,GAAG,+CAA+C,KAAK;EACzG3B,YAAY,CAACyB,UAAU,CAAC,OAAO,CAAC;EAChCzB,YAAY,CAACyB,UAAU,CAAC,MAAM,CAAC;EAC/B5B,OAAO,CAACiB,KAAK,CAACa,YAAY,CAAC;EAE3BC,UAAU,CAAC,MAAM;IACfC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC,CAAC,EAAE,IAAI,CAAC;AACV,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,yBAAyB,GAAIL,YAAY,IAAK;EACzD,IAAI,CAAC7B,cAAc,CAAC,CAAC,EAAE;IACrB4B,uBAAuB,CAACC,YAAY,CAAC;IACrC,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMM,cAAc,GAAGA,CAAA,KAAM;EAClC,IAAI,CAACnC,cAAc,CAAC,CAAC,EAAE;IACrB,OAAO,IAAI;EACb;EAEA,IAAI;IACF,OAAOM,IAAI,CAACC,KAAK,CAACL,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;EACjD,CAAC,CAAC,OAAOa,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMoB,eAAe,GAAGA,CAAA,KAAM;EACnC,IAAI,CAACpC,cAAc,CAAC,CAAC,EAAE;IACrB,OAAO,IAAI;EACb;EAEA,OAAOE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMkC,mBAAmB,GAAGA,CAAA,KAAM;EACvC,MAAMpC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAE3C,IAAI,CAACF,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,IAAI;IACF,MAAMQ,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACG,IAAI,CAACT,KAAK,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;IACrC,MAAMwB,kBAAkB,GAAG1B,WAAW,GAAI,CAAC,GAAG,EAAG,CAAC,CAAC;;IAEnD,OAAOH,OAAO,CAACM,GAAG,IAAIN,OAAO,CAACM,GAAG,GAAGuB,kBAAkB;EACxD,CAAC,CAAC,OAAOtB,KAAK,EAAE;IACd,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMuB,kBAAkB,GAAGA,CAAA,KAAM;EACtC,MAAMtC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAE3C,IAAI,CAACF,KAAK,EAAE;IACV,OAAO;MAAEuC,OAAO,EAAE,IAAI;MAAEhB,QAAQ,EAAE;IAAE,CAAC;EACvC;EAEA,IAAI;IACF,MAAMf,OAAO,GAAGH,IAAI,CAACC,KAAK,CAACG,IAAI,CAACT,KAAK,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;IACrC,MAAMU,QAAQ,GAAGf,OAAO,CAACM,GAAG,GAAGH,WAAW;IAE1C,OAAO;MACL4B,OAAO,EAAEhB,QAAQ,IAAI,CAAC;MACtBA,QAAQ,EAAEiB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAElB,QAAQ,CAAC;MAC/BD,SAAS,EAAE,IAAIV,IAAI,CAACJ,OAAO,CAACM,GAAG,GAAG,IAAI,CAAC;MACvC4B,YAAY,EAAEnB,QAAQ,GAAG,IAAI;MAAE;MAC/BoB,iBAAiB,EAAEC,cAAc,CAACrB,QAAQ;IAC5C,CAAC;EACH,CAAC,CAAC,OAAOR,KAAK,EAAE;IACd,OAAO;MAAEwB,OAAO,EAAE,IAAI;MAAEhB,QAAQ,EAAE;IAAE,CAAC;EACvC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMqB,cAAc,GAAIC,OAAO,IAAK;EAClC,IAAIA,OAAO,IAAI,CAAC,EAAE,OAAO,SAAS;EAElC,MAAMC,KAAK,GAAGN,IAAI,CAACO,KAAK,CAACF,OAAO,GAAG,IAAI,CAAC;EACxC,MAAMG,OAAO,GAAGR,IAAI,CAACO,KAAK,CAAEF,OAAO,GAAG,IAAI,GAAI,EAAE,CAAC;EAEjD,IAAIC,KAAK,GAAG,CAAC,EAAE;IACb,OAAQ,GAAEA,KAAM,KAAIE,OAAQ,GAAE;EAChC,CAAC,MAAM,IAAIA,OAAO,GAAG,CAAC,EAAE;IACtB,OAAQ,GAAEA,OAAQ,GAAE;EACtB,CAAC,MAAM;IACL,OAAO,cAAc;EACvB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}