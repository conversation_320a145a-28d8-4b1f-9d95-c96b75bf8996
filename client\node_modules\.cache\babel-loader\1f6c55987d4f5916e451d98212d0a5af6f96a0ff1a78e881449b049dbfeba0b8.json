{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\WriteExam\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { getExamById } from \"../../../apicalls/exams\";\nimport { addReport } from \"../../../apicalls/reports\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport Instructions from \"./Instructions\";\nimport Pass from \"../../../assets/pass.gif\";\nimport Fail from \"../../../assets/fail.gif\";\nimport Confetti from \"react-confetti\";\nimport useWindowSize from \"react-use/lib/useWindowSize\";\nimport PassSound from \"../../../assets/pass.mp3\";\nimport FailSound from \"../../../assets/fail.mp3\";\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\n\n// Simple Quiz Renderer Component - Safe from object rendering issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerSelect,\n  onNext,\n  onPrevious,\n  timeLeft,\n  examTitle\n}) => {\n  if (!question || typeof question !== 'object') {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl p-8 shadow-lg text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-red-600 mb-4\",\n          children: \"Question Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500\",\n          children: \"Unable to load question data.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Safely extract and convert all properties to strings\n  const questionText = String(question.name || '');\n  const answerType = String(question.answerType || '');\n  const correctOption = String(question.correctOption || '');\n  const correctAnswer = String(question.correctAnswer || '');\n  const imageUrl = question.image || question.imageUrl || '';\n\n  // Safely process options\n  let optionsArray = [];\n  if (question.options) {\n    try {\n      if (Array.isArray(question.options)) {\n        optionsArray = question.options.filter(opt => opt && typeof opt === 'string').map(opt => String(opt).trim()).filter(opt => opt.length > 0);\n      } else if (typeof question.options === 'object') {\n        optionsArray = Object.values(question.options).filter(opt => opt && typeof opt === 'string').map(opt => String(opt).trim()).filter(opt => opt.length > 0);\n      }\n    } catch (error) {\n      console.error('Error processing options:', error);\n      optionsArray = [];\n    }\n  }\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n  const isTimeWarning = timeLeft <= 60;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold\",\n                children: \"Q\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: String(examTitle)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500\",\n                children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-6 py-3 rounded-xl font-mono font-bold text-white shadow-lg ${isTimeWarning ? 'bg-red-600 animate-pulse' : 'bg-blue-600'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs mb-1\",\n              children: \"TIME\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg\",\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl p-8 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center bg-blue-100 rounded-full px-4 py-2\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-blue-800 font-semibold\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-medium text-gray-900 leading-relaxed\",\n            children: questionText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-8 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-block rounded-xl overflow-hidden shadow-lg border border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: imageUrl,\n              alt: \"Question\",\n              className: \"max-w-full max-h-96 object-contain\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: answerType === \"Options\" && optionsArray.length > 0 ?\n          // Multiple Choice Questions\n          optionsArray.map((option, index) => {\n            const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\n            const isSelected = selectedAnswer === index;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onAnswerSelect(index),\n              className: `w-full text-left p-4 rounded-xl border-2 transition-all duration-300 ${isSelected ? 'border-blue-500 bg-blue-50 text-blue-900' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${isSelected ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600'}`,\n                  children: optionLetter\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-1 font-medium\",\n                  children: String(option)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 23\n                }, this), isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-4 h-4 text-white\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 154,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 153,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 21\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 19\n            }, this);\n          }) :\n          /*#__PURE__*/\n          // Fill in the blank / Free text\n          _jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700\",\n              children: \"Your Answer:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: selectedAnswer || '',\n              onChange: e => onAnswerSelect(e.target.value),\n              placeholder: \"Type your answer here...\",\n              className: \"w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onPrevious,\n          disabled: questionIndex === 0,\n          className: `px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${questionIndex === 0 ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-gray-600 text-white hover:bg-gray-700 shadow-lg hover:shadow-xl'}`,\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-500 mb-2\",\n            children: \"Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-48 bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n              style: {\n                width: `${(questionIndex + 1) / totalQuestions * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onNext,\n          className: \"px-6 py-3 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl\",\n          children: questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next →'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 5\n  }, this);\n};\n\n// Simple Review Renderer Component - Safe from object rendering issues\n_c = SimpleQuizRenderer;\nconst SimpleReviewRenderer = ({\n  questions,\n  selectedOptions,\n  explanations,\n  fetchExplanation,\n  setView,\n  examData,\n  setSelectedQuestionIndex,\n  setSelectedOptions,\n  setResult,\n  setTimeUp,\n  setSecondsLeft,\n  setExplanations\n}) => {\n  if (!questions || !Array.isArray(questions)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl p-8 shadow-lg text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-red-600 mb-4\",\n          children: \"Review Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500\",\n          children: \"No questions available for review.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-blue-600 mb-2\",\n            children: \"Answer Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-slate-600\",\n            children: \"Review your answers and get explanations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4 mb-6\",\n        children: questions.map((question, index) => {\n          // Safety check\n          if (!question || typeof question !== 'object') {\n            return null;\n          }\n\n          // Safely extract data\n          const questionText = String(question.name || '');\n          const answerType = String(question.answerType || '');\n          const correctOption = question.correctOption;\n          const correctAnswer = question.correctAnswer;\n          const userAnswer = selectedOptions[index];\n\n          // Determine if answer is correct\n          let isCorrect = false;\n          let correctAnswerText = '';\n          let userAnswerText = '';\n          if (answerType === \"Options\") {\n            isCorrect = correctOption === userAnswer;\n\n            // Get correct answer text\n            if (question.options && correctOption !== undefined) {\n              const optionValue = question.options[correctOption];\n              correctAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || correctOption || \"Unknown\");\n            } else {\n              correctAnswerText = String(correctOption || \"Unknown\");\n            }\n\n            // Get user answer text\n            if (question.options && userAnswer !== undefined) {\n              const optionValue = question.options[userAnswer];\n              userAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\n            } else {\n              userAnswerText = String(userAnswer || \"Not answered\");\n            }\n          } else {\n            isCorrect = correctAnswer === userAnswer;\n            correctAnswerText = String(correctAnswer || \"Unknown\");\n            userAnswerText = String(userAnswer || \"Not answered\");\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-lg shadow-md border-2 p-4 ${isCorrect ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\",\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-slate-800 font-medium leading-relaxed\",\n                    children: questionText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-semibold text-slate-600\",\n                children: \"Your Answer: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`,\n                children: userAnswerText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 19\n              }, this), isCorrect ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-3 text-green-600 text-xl\",\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-3 text-red-600 text-xl\",\n                children: \"\\u2717\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-semibold text-slate-600\",\n                children: \"Correct Answer: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-green-700\",\n                children: correctAnswerText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-3 text-green-500 text-xl\",\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 19\n            }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\",\n                onClick: () => {\n                  fetchExplanation(questionText, correctAnswerText, userAnswerText, question.image || question.imageUrl || '');\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCA1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 354,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Get Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 19\n            }, this), explanations[questionText] && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-600 text-lg mr-2\",\n                  children: \"\\uD83D\\uDCA1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 364,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"font-bold text-gray-800 text-base\",\n                  children: \"Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 21\n              }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3 p-2 bg-gray-50 rounded border border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-700 text-sm font-medium\",\n                    children: \"\\uD83D\\uDCCA Reference Diagram:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: question.image || question.imageUrl,\n                    alt: \"Question diagram\",\n                    className: \"max-w-full max-h-48 object-contain rounded border border-gray-300\",\n                    style: {\n                      maxWidth: '350px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 374,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded\",\n                children: String(explanations[questionText] || '')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 385,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 19\n            }, this)]\n          }, String(question._id || index), true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-8 py-4 bg-gray-600 hover:bg-gray-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg\",\n          onClick: () => setView(\"result\"),\n          children: \"\\u2190 Back to Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-8 py-4 bg-green-600 hover:bg-green-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg\",\n          onClick: () => {\n            // Reset exam state and restart\n            setView(\"instructions\");\n            setSelectedQuestionIndex(0);\n            setSelectedOptions({});\n            setResult({});\n            setTimeUp(false);\n            setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n            setExplanations({});\n          },\n          children: \"\\uD83D\\uDD04 Retake Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 243,\n    columnNumber: 5\n  }, this);\n};\n_c2 = SimpleReviewRenderer;\nfunction WriteExam() {\n  _s();\n  var _result$correctAnswer, _result$correctAnswer2, _result$correctAnswer3, _result$correctAnswer4;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [result, setResult] = useState({});\n  const params = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [view, setView] = useState(\"instructions\");\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [startTime, setStartTime] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const [explanations, setExplanations] = useState({});\n  const getExamData = useCallback(async () => {\n    try {\n      setIsLoading(true);\n      dispatch(ShowLoading());\n      console.log(\"Fetching exam data for ID:\", params.id);\n      const response = await getExamById({\n        examId: params.id\n      });\n      console.log(\"Exam API Response:\", response);\n      dispatch(HideLoading());\n      setIsLoading(false);\n      if (response.success) {\n        const examData = response.data;\n\n        // Check different possible question locations\n        let questions = [];\n        if (examData !== null && examData !== void 0 && examData.questions && Array.isArray(examData.questions)) {\n          questions = examData.questions;\n        } else if (examData !== null && examData !== void 0 && examData.question && Array.isArray(examData.question)) {\n          questions = examData.question;\n        } else if (examData && Array.isArray(examData)) {\n          questions = examData;\n        }\n        console.log(\"Exam Data:\", examData);\n        console.log(\"Questions found:\", questions.length);\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\n        setQuestions(questions);\n        setExamData(examData);\n        setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n        if (questions.length === 0) {\n          console.warn(\"No questions found in exam data\");\n          console.log(\"Full response for debugging:\", response);\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\n        }\n      } else {\n        console.error(\"API Error:\", response.message);\n        console.log(\"Full error response:\", response);\n        message.error(response.message || \"Failed to load exam data\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      setIsLoading(false);\n      console.error(\"Exception in getExamData:\", error);\n      message.error(error.message || \"Failed to load exam. Please try again.\");\n    }\n  }, [params.id, dispatch]);\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      // Check if user is available\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      const indexMap = [];\n      questions.forEach((q, idx) => {\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          indexMap.push(idx);\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      const wrongPayload = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: q.correctAnswer || q.correctOption,\n              userAnswer: userAnswerKey\n            });\n          }\n        } else if (q.answerType === \"Options\") {\n          const correctKey = q.correctOption;\n          const correctValue = q.options && q.options[correctKey] || correctKey;\n          const userValue = q.options && q.options[userAnswerKey] || userAnswerKey || \"\";\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: correctValue,\n              userAnswer: userValue\n            });\n          }\n        }\n      });\n\n      // Calculate time spent\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60; // Convert minutes to seconds\n\n      // Calculate score and points\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round(correctCount / totalQuestions * 100);\n      const points = correctCount * 10; // 10 points per correct answer\n\n      // Determine pass/fail based on percentage\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers: correctAnswers || [],\n        wrongAnswers: wrongAnswers || [],\n        verdict: verdict || \"Fail\",\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n      setResult(tempResult);\n      const response = await addReport({\n        exam: params.id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        // Include XP data in the result\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n        setResult(resultWithXP);\n        setView(\"result\");\n        window.scrollTo(0, 0);\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const startTimer = () => {\n    const totalSeconds = (examData === null || examData === void 0 ? void 0 : examData.duration) || 0;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now()); // Record start time for XP calculation\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  };\n  useEffect(() => {\n    if (timeUp && view === \"questions\") {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, view, intervalId, calculateResult]);\n  useEffect(() => {\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\n    if (params.id) {\n      getExamData();\n    } else {\n      console.error(\"No exam ID provided in URL parameters\");\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\n      navigate('/user/quiz');\n    }\n  }, [params.id, getExamData, navigate]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n\n  // Add fullscreen class for all quiz views (instructions, questions, results)\n  useEffect(() => {\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\n      document.body.classList.add(\"quiz-fullscreen\");\n    } else {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    };\n  }, [view]);\n\n  // Repair function for fixing orphaned questions\n  const repairExamQuestions = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await fetch('/api/exams/repair-exam-questions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          examId: params.id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        message.success(data.message);\n        // Reload the exam data\n        getExamData();\n      } else {\n        message.error(data.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to repair exam questions\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Check if user is authenticated\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-10 h-10 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 745,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 744,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 743,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-8\",\n          children: \"Please log in to access the exam and start your learning journey.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 749,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n          onClick: () => navigate(\"/login\"),\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 742,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 741,\n      columnNumber: 7\n    }, this);\n  }\n  return examData ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [view === \"instructions\" && /*#__PURE__*/_jsxDEV(Instructions, {\n      examData: examData,\n      setView: setView,\n      startTimer: startTimer,\n      questions: questions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 765,\n      columnNumber: 9\n    }, this), view === \"questions\" && (isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white animate-spin\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              className: \"opacity-25\",\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\",\n              stroke: \"currentColor\",\n              strokeWidth: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              className: \"opacity-75\",\n              fill: \"currentColor\",\n              d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-blue-800 mb-4\",\n          children: \"Loading Quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 783,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-600 text-lg\",\n          children: \"Please wait while we prepare your questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 775,\n      columnNumber: 11\n    }, this) : questions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 794,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 793,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 792,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-amber-800 mb-4\",\n          children: \"No Questions Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 797,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-amber-700 mb-6 text-lg leading-relaxed\",\n          children: \"This exam appears to have no questions. This could be due to:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 798,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-left text-amber-700 mb-8 space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Questions not properly linked to this exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 802,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Database connection issues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Exam configuration problems\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 801,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: repairExamQuestions,\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD27 Repair Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              console.log(\"Retrying exam data fetch...\");\n              getExamData();\n            },\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD04 Retry Loading\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 813,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/user/quiz'),\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\u2190 Back to Quiz List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 822,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 791,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 790,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(SimpleQuizRenderer, {\n      question: questions[selectedQuestionIndex],\n      questionIndex: selectedQuestionIndex,\n      totalQuestions: questions.length,\n      selectedAnswer: selectedOptions[selectedQuestionIndex],\n      onAnswerSelect: answer => setSelectedOptions({\n        ...selectedOptions,\n        [selectedQuestionIndex]: answer\n      }),\n      onNext: () => {\n        if (selectedQuestionIndex === questions.length - 1) {\n          calculateResult();\n        } else {\n          setSelectedQuestionIndex(selectedQuestionIndex + 1);\n        }\n      },\n      onPrevious: () => {\n        if (selectedQuestionIndex > 0) {\n          setSelectedQuestionIndex(selectedQuestionIndex - 1);\n        }\n      },\n      timeLeft: secondsLeft,\n      examTitle: (examData === null || examData === void 0 ? void 0 : examData.name) || \"Quiz\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 832,\n      columnNumber: 11\n    }, this)), view === \"result\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\",\n      children: [result.verdict === \"Pass\" && /*#__PURE__*/_jsxDEV(Confetti, {\n        width: width,\n        height: height\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 858,\n        columnNumber: 41\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-8 py-10 text-center relative ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\" : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500 to-green-600\" : \"bg-gradient-to-br from-amber-500 to-orange-600\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: result.verdict === \"Pass\" ? Pass : Fail,\n                  alt: result.verdict,\n                  className: \"w-12 h-12 object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 874,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 869,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: `text-4xl font-black mb-4 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"}`,\n                children: result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 880,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\",\n                children: result.verdict === \"Pass\" ? \"You've mastered this exam with flying colors!\" : \"Every challenge makes you stronger. Try again!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-blue-600 mb-2 tracking-tight\",\n                    children: [Math.round((((_result$correctAnswer = result.correctAnswers) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.length) || 0) / questions.length * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 900,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-blue-700/80 uppercase tracking-wider\",\n                    children: \"Your Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 903,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-emerald-600 mb-2 tracking-tight\",\n                    children: [((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0, \"/\", questions.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\",\n                    children: \"Correct\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 914,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\" : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"} to-transparent`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-4xl font-black mb-2 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                    children: result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 928,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-sm font-bold uppercase tracking-wider ${result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"}`,\n                    children: result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 927,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 895,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative bg-slate-100 rounded-2xl p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-bold text-slate-700 mb-1\",\n                    children: \"Performance Overview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-slate-500\",\n                    children: \"Your achievement level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 945,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${result.verdict === \"Pass\" ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\" : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"}`,\n                      style: {\n                        width: `${(((_result$correctAnswer3 = result.correctAnswers) === null || _result$correctAnswer3 === void 0 ? void 0 : _result$correctAnswer3.length) || 0) / questions.length * 100}%`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 959,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 951,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 950,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"0%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 963,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-lg font-black tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                      children: [Math.round((((_result$correctAnswer4 = result.correctAnswers) === null || _result$correctAnswer4 === void 0 ? void 0 : _result$correctAnswer4.length) || 0) / questions.length * 100), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 964,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"100%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 969,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 962,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 949,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 944,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 943,\n              columnNumber: 17\n            }, this), result.xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(XPResultDisplay, {\n                xpData: result.xpData\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                onClick: () => setView(\"review\"),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 988,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative\",\n                  children: \"Review Answers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 983,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 894,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 861,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 860,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 857,\n      columnNumber: 9\n    }, this), view === \"review\" && /*#__PURE__*/_jsxDEV(SimpleReviewRenderer, {\n      questions: questions,\n      selectedOptions: selectedOptions,\n      explanations: explanations,\n      fetchExplanation: fetchExplanation,\n      setView: setView,\n      examData: examData,\n      setSelectedQuestionIndex: setSelectedQuestionIndex,\n      setSelectedOptions: setSelectedOptions,\n      setResult: setResult,\n      setTimeUp: setTimeUp,\n      setSecondsLeft: setSecondsLeft,\n      setExplanations: setExplanations\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1001,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 762,\n    columnNumber: 5\n  }, this) : null;\n}\n_s(WriteExam, \"O6w+2GWMw4fZ1y7nA7sammn5z5g=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useWindowSize];\n});\n_c3 = WriteExam;\nexport default WriteExam;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"SimpleQuizRenderer\");\n$RefreshReg$(_c2, \"SimpleReviewRenderer\");\n$RefreshReg$(_c3, \"WriteExam\");", "map": {"version": 3, "names": ["message", "React", "useCallback", "useEffect", "useState", "useDispatch", "useSelector", "useNavigate", "useParams", "getExamById", "addReport", "HideLoading", "ShowLoading", "Instructions", "Pass", "Fail", "Confetti", "useWindowSize", "PassSound", "FailSound", "chatWithChatGPTToGetAns", "chatWithChatGPTToExplainAns", "XPResultDisplay", "jsxDEV", "_jsxDEV", "SimpleQuiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerSelect", "onNext", "onPrevious", "timeLeft", "examTitle", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "questionText", "String", "name", "answerType", "correctOption", "<PERSON><PERSON><PERSON><PERSON>", "imageUrl", "image", "optionsArray", "options", "Array", "isArray", "filter", "opt", "map", "trim", "length", "Object", "values", "error", "console", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "isTimeWarning", "src", "alt", "option", "index", "optionLetter", "fromCharCode", "isSelected", "onClick", "fill", "viewBox", "fillRule", "d", "clipRule", "type", "value", "onChange", "e", "target", "placeholder", "disabled", "style", "width", "_c", "SimpleReviewRenderer", "questions", "selectedOptions", "explanations", "fetchExplanation", "<PERSON><PERSON><PERSON><PERSON>", "examData", "setSelectedQuestionIndex", "setSelectedOptions", "setResult", "setTimeUp", "setSecondsLeft", "setExplanations", "userAnswer", "isCorrect", "correctAnswerText", "userAnswerText", "undefined", "optionValue", "max<PERSON><PERSON><PERSON>", "_id", "duration", "_c2", "WriteExam", "_s", "_result$correctAnswer", "_result$correctAnswer2", "_result$correctAnswer3", "_result$correctAnswer4", "setExamData", "setQuestions", "selectedQuestionIndex", "result", "params", "dispatch", "navigate", "view", "secondsLeft", "timeUp", "intervalId", "setIntervalId", "isLoading", "setIsLoading", "startTime", "setStartTime", "user", "state", "height", "getExamData", "log", "id", "response", "examId", "success", "data", "keys", "warn", "warning", "checkFreeTextAnswers", "payload", "calculateResult", "freeTextPayload", "indexMap", "for<PERSON>ach", "q", "idx", "push", "expectedAnswer", "gptResults", "gptMap", "r", "reason", "correctAnswers", "wrongAnswers", "wrongPayload", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "correctValue", "userValue", "timeSpent", "Date", "now", "totalTimeAllowed", "correctCount", "scorePercentage", "round", "points", "passingPercentage", "passingMarks", "verdict", "tempResult", "score", "exam", "resultWithXP", "xpData", "window", "scrollTo", "Audio", "play", "prev", "explanation", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "repairExamQuestions", "fetch", "method", "headers", "localStorage", "getItem", "JSON", "stringify", "json", "cx", "cy", "stroke", "strokeWidth", "answer", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/WriteExam/index.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\r\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\r\n\r\n// Simple Quiz Renderer Component - Safe from object rendering issues\r\nconst SimpleQuizRenderer = ({ question, questionIndex, totalQuestions, selectedAnswer, onAnswerSelect, onNext, onPrevious, timeLeft, examTitle }) => {\r\n  if (!question || typeof question !== 'object') {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\">\r\n        <div className=\"bg-white rounded-xl p-8 shadow-lg text-center\">\r\n          <h3 className=\"text-xl font-bold text-red-600 mb-4\">Question Error</h3>\r\n          <p className=\"text-red-500\">Unable to load question data.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Safely extract and convert all properties to strings\r\n  const questionText = String(question.name || '');\r\n  const answerType = String(question.answerType || '');\r\n  const correctOption = String(question.correctOption || '');\r\n  const correctAnswer = String(question.correctAnswer || '');\r\n  const imageUrl = question.image || question.imageUrl || '';\r\n\r\n  // Safely process options\r\n  let optionsArray = [];\r\n  if (question.options) {\r\n    try {\r\n      if (Array.isArray(question.options)) {\r\n        optionsArray = question.options\r\n          .filter(opt => opt && typeof opt === 'string')\r\n          .map(opt => String(opt).trim())\r\n          .filter(opt => opt.length > 0);\r\n      } else if (typeof question.options === 'object') {\r\n        optionsArray = Object.values(question.options)\r\n          .filter(opt => opt && typeof opt === 'string')\r\n          .map(opt => String(opt).trim())\r\n          .filter(opt => opt.length > 0);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error processing options:', error);\r\n      optionsArray = [];\r\n    }\r\n  }\r\n\r\n  const formatTime = (seconds) => {\r\n    const minutes = Math.floor(seconds / 60);\r\n    const remainingSeconds = seconds % 60;\r\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const isTimeWarning = timeLeft <= 60;\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n      {/* Header */}\r\n      <div className=\"bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50\">\r\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\">\r\n                <span className=\"text-white font-bold\">Q</span>\r\n              </div>\r\n              <div>\r\n                <h1 className=\"text-lg font-semibold text-gray-900\">{String(examTitle)}</h1>\r\n                <p className=\"text-sm text-gray-500\">Question {questionIndex + 1} of {totalQuestions}</p>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Timer */}\r\n            <div className={`px-6 py-3 rounded-xl font-mono font-bold text-white shadow-lg ${\r\n              isTimeWarning ? 'bg-red-600 animate-pulse' : 'bg-blue-600'\r\n            }`}>\r\n              <div className=\"text-xs mb-1\">TIME</div>\r\n              <div className=\"text-lg\">{formatTime(timeLeft)}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Question Content */}\r\n      <div className=\"max-w-4xl mx-auto px-4 py-8\">\r\n        <div className=\"bg-white rounded-2xl shadow-xl p-8 mb-6\">\r\n          {/* Question Number */}\r\n          <div className=\"mb-6\">\r\n            <div className=\"inline-flex items-center bg-blue-100 rounded-full px-4 py-2\">\r\n              <span className=\"text-blue-800 font-semibold\">Question {questionIndex + 1} of {totalQuestions}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Question Text */}\r\n          <div className=\"mb-8\">\r\n            <h2 className=\"text-xl font-medium text-gray-900 leading-relaxed\">\r\n              {questionText}\r\n            </h2>\r\n          </div>\r\n\r\n          {/* Image if present */}\r\n          {imageUrl && (\r\n            <div className=\"mb-8 text-center\">\r\n              <div className=\"inline-block rounded-xl overflow-hidden shadow-lg border border-gray-200\">\r\n                <img\r\n                  src={imageUrl}\r\n                  alt=\"Question\"\r\n                  className=\"max-w-full max-h-96 object-contain\"\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Answer Options */}\r\n          <div className=\"space-y-4\">\r\n            {answerType === \"Options\" && optionsArray.length > 0 ? (\r\n              // Multiple Choice Questions\r\n              optionsArray.map((option, index) => {\r\n                const optionLetter = String.fromCharCode(65 + index); // A, B, C, D\r\n                const isSelected = selectedAnswer === index;\r\n\r\n                return (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => onAnswerSelect(index)}\r\n                    className={`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 ${\r\n                      isSelected\r\n                        ? 'border-blue-500 bg-blue-50 text-blue-900'\r\n                        : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-50'\r\n                    }`}\r\n                  >\r\n                    <div className=\"flex items-center space-x-4\">\r\n                      <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${\r\n                        isSelected\r\n                          ? 'bg-blue-600 text-white'\r\n                          : 'bg-gray-100 text-gray-600'\r\n                      }`}>\r\n                        {optionLetter}\r\n                      </div>\r\n                      <span className=\"flex-1 font-medium\">{String(option)}</span>\r\n                      {isSelected && (\r\n                        <div className=\"w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center\">\r\n                          <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                            <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\r\n                          </svg>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </button>\r\n                );\r\n              })\r\n            ) : (\r\n              // Fill in the blank / Free text\r\n              <div className=\"space-y-4\">\r\n                <label className=\"block text-sm font-medium text-gray-700\">\r\n                  Your Answer:\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={selectedAnswer || ''}\r\n                  onChange={(e) => onAnswerSelect(e.target.value)}\r\n                  placeholder=\"Type your answer here...\"\r\n                  className=\"w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-lg\"\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div className=\"flex justify-between items-center\">\r\n          <button\r\n            onClick={onPrevious}\r\n            disabled={questionIndex === 0}\r\n            className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${\r\n              questionIndex === 0\r\n                ? 'bg-gray-200 text-gray-400 cursor-not-allowed'\r\n                : 'bg-gray-600 text-white hover:bg-gray-700 shadow-lg hover:shadow-xl'\r\n            }`}\r\n          >\r\n            ← Previous\r\n          </button>\r\n\r\n          <div className=\"text-center\">\r\n            <div className=\"text-sm text-gray-500 mb-2\">Progress</div>\r\n            <div className=\"w-48 bg-gray-200 rounded-full h-2\">\r\n              <div\r\n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\r\n                style={{ width: `${((questionIndex + 1) / totalQuestions) * 100}%` }}\r\n              ></div>\r\n            </div>\r\n          </div>\r\n\r\n          <button\r\n            onClick={onNext}\r\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-xl font-semibold hover:bg-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl\"\r\n          >\r\n            {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next →'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Simple Review Renderer Component - Safe from object rendering issues\r\nconst SimpleReviewRenderer = ({\r\n  questions,\r\n  selectedOptions,\r\n  explanations,\r\n  fetchExplanation,\r\n  setView,\r\n  examData,\r\n  setSelectedQuestionIndex,\r\n  setSelectedOptions,\r\n  setResult,\r\n  setTimeUp,\r\n  setSecondsLeft,\r\n  setExplanations\r\n}) => {\r\n  if (!questions || !Array.isArray(questions)) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\">\r\n        <div className=\"bg-white rounded-xl p-8 shadow-lg text-center\">\r\n          <h3 className=\"text-xl font-bold text-red-600 mb-4\">Review Error</h3>\r\n          <p className=\"text-red-500\">No questions available for review.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\">\r\n      <div className=\"max-w-4xl mx-auto px-4\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-6\">\r\n          <div className=\"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\">\r\n            <h2 className=\"text-2xl font-bold text-blue-600 mb-2\">Answer Review</h2>\r\n            <p className=\"text-slate-600\">Review your answers and get explanations</p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Questions Review */}\r\n        <div className=\"space-y-4 mb-6\">\r\n          {questions.map((question, index) => {\r\n            // Safety check\r\n            if (!question || typeof question !== 'object') {\r\n              return null;\r\n            }\r\n\r\n            // Safely extract data\r\n            const questionText = String(question.name || '');\r\n            const answerType = String(question.answerType || '');\r\n            const correctOption = question.correctOption;\r\n            const correctAnswer = question.correctAnswer;\r\n            const userAnswer = selectedOptions[index];\r\n\r\n            // Determine if answer is correct\r\n            let isCorrect = false;\r\n            let correctAnswerText = '';\r\n            let userAnswerText = '';\r\n\r\n            if (answerType === \"Options\") {\r\n              isCorrect = correctOption === userAnswer;\r\n\r\n              // Get correct answer text\r\n              if (question.options && correctOption !== undefined) {\r\n                const optionValue = question.options[correctOption];\r\n                correctAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || correctOption || \"Unknown\");\r\n              } else {\r\n                correctAnswerText = String(correctOption || \"Unknown\");\r\n              }\r\n\r\n              // Get user answer text\r\n              if (question.options && userAnswer !== undefined) {\r\n                const optionValue = question.options[userAnswer];\r\n                userAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\r\n              } else {\r\n                userAnswerText = String(userAnswer || \"Not answered\");\r\n              }\r\n            } else {\r\n              isCorrect = correctAnswer === userAnswer;\r\n              correctAnswerText = String(correctAnswer || \"Unknown\");\r\n              userAnswerText = String(userAnswer || \"Not answered\");\r\n            }\r\n\r\n            return (\r\n              <div\r\n                key={String(question._id || index)}\r\n                className={`rounded-lg shadow-md border-2 p-4 ${\r\n                  isCorrect\r\n                    ? 'bg-green-50 border-green-300'\r\n                    : 'bg-red-50 border-red-300'\r\n                }`}\r\n              >\r\n                {/* Question */}\r\n                <div className=\"mb-3\">\r\n                  <div className=\"flex items-start space-x-3\">\r\n                    <div className=\"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\">\r\n                      {index + 1}\r\n                    </div>\r\n                    <div className=\"flex-1\">\r\n                      <p className=\"text-slate-800 font-medium leading-relaxed\">{questionText}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Your Answer */}\r\n                <div className=\"mb-2\">\r\n                  <span className=\"text-sm font-semibold text-slate-600\">Your Answer: </span>\r\n                  <span className={`font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\r\n                    {userAnswerText}\r\n                  </span>\r\n                  {isCorrect ? (\r\n                    <span className=\"ml-3 text-green-600 text-xl\">✓</span>\r\n                  ) : (\r\n                    <span className=\"ml-3 text-red-600 text-xl\">✗</span>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Correct Answer (only for wrong answers) */}\r\n                {!isCorrect && (\r\n                  <div className=\"mb-2\">\r\n                    <span className=\"text-sm font-semibold text-slate-600\">Correct Answer: </span>\r\n                    <span className=\"font-medium text-green-700\">{correctAnswerText}</span>\r\n                    <span className=\"ml-3 text-green-500 text-xl\">✓</span>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation Button (only for wrong answers) */}\r\n                {!isCorrect && (\r\n                  <div className=\"mt-2\">\r\n                    <button\r\n                      className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\"\r\n                      onClick={() => {\r\n                        fetchExplanation(\r\n                          questionText,\r\n                          correctAnswerText,\r\n                          userAnswerText,\r\n                          question.image || question.imageUrl || ''\r\n                        );\r\n                      }}\r\n                    >\r\n                      <span>💡</span>\r\n                      <span>Get Explanation</span>\r\n                    </button>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation Display */}\r\n                {explanations[questionText] && (\r\n                  <div className=\"mt-3 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200\">\r\n                    <div className=\"flex items-center mb-2\">\r\n                      <span className=\"text-blue-600 text-lg mr-2\">💡</span>\r\n                      <h6 className=\"font-bold text-gray-800 text-base\">Explanation</h6>\r\n                    </div>\r\n\r\n                    {/* Image if present */}\r\n                    {(question.image || question.imageUrl) && (\r\n                      <div className=\"mb-3 p-2 bg-gray-50 rounded border border-gray-200\">\r\n                        <div className=\"flex items-center mb-1\">\r\n                          <span className=\"text-gray-700 text-sm font-medium\">📊 Reference Diagram:</span>\r\n                        </div>\r\n                        <div className=\"flex justify-center\">\r\n                          <img\r\n                            src={question.image || question.imageUrl}\r\n                            alt=\"Question diagram\"\r\n                            className=\"max-w-full max-h-48 object-contain rounded border border-gray-300\"\r\n                            style={{ maxWidth: '350px' }}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    <div className=\"text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded\">\r\n                      {String(explanations[questionText] || '')}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\r\n          <button\r\n            className=\"px-8 py-4 bg-gray-600 hover:bg-gray-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg\"\r\n            onClick={() => setView(\"result\")}\r\n          >\r\n            ← Back to Results\r\n          </button>\r\n\r\n          <button\r\n            className=\"px-8 py-4 bg-green-600 hover:bg-green-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg\"\r\n            onClick={() => {\r\n              // Reset exam state and restart\r\n              setView(\"instructions\");\r\n              setSelectedQuestionIndex(0);\r\n              setSelectedOptions({});\r\n              setResult({});\r\n              setTimeUp(false);\r\n              setSecondsLeft(examData?.duration || 0);\r\n              setExplanations({});\r\n            }}\r\n          >\r\n            🔄 Retake Quiz\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction WriteExam() {\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [startTime, setStartTime] = useState(null);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      dispatch(ShowLoading());\r\n      console.log(\"Fetching exam data for ID:\", params.id);\r\n\r\n      const response = await getExamById({ examId: params.id });\r\n      console.log(\"Exam API Response:\", response);\r\n\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n\r\n      if (response.success) {\r\n        const examData = response.data;\r\n\r\n        // Check different possible question locations\r\n        let questions = [];\r\n        if (examData?.questions && Array.isArray(examData.questions)) {\r\n          questions = examData.questions;\r\n        } else if (examData?.question && Array.isArray(examData.question)) {\r\n          questions = examData.question;\r\n        } else if (examData && Array.isArray(examData)) {\r\n          questions = examData;\r\n        }\r\n\r\n        console.log(\"Exam Data:\", examData);\r\n        console.log(\"Questions found:\", questions.length);\r\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\r\n\r\n        setQuestions(questions);\r\n        setExamData(examData);\r\n        setSecondsLeft(examData?.duration || 0);\r\n\r\n        if (questions.length === 0) {\r\n          console.warn(\"No questions found in exam data\");\r\n          console.log(\"Full response for debugging:\", response);\r\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\r\n        }\r\n      } else {\r\n        console.error(\"API Error:\", response.message);\r\n        console.log(\"Full error response:\", response);\r\n        message.error(response.message || \"Failed to load exam data\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n      console.error(\"Exception in getExamData:\", error);\r\n      message.error(error.message || \"Failed to load exam. Please try again.\");\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = (q.options && q.options[correctKey]) || correctKey;\r\n          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      // Calculate time spent\r\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\r\n      const totalTimeAllowed = (examData?.duration || 0) * 60; // Convert minutes to seconds\r\n\r\n      // Calculate score and points\r\n      const totalQuestions = questions.length;\r\n      const correctCount = correctAnswers.length;\r\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\r\n      const points = correctCount * 10; // 10 points per correct answer\r\n\r\n      // Determine pass/fail based on percentage\r\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\r\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\r\n\r\n      const tempResult = {\r\n        correctAnswers: correctAnswers || [],\r\n        wrongAnswers: wrongAnswers || [],\r\n        verdict: verdict || \"Fail\",\r\n        score: scorePercentage,\r\n        points: points,\r\n        totalQuestions: totalQuestions,\r\n        timeSpent: timeSpent,\r\n        totalTimeAllowed: totalTimeAllowed\r\n      };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        // Include XP data in the result\r\n        const resultWithXP = {\r\n          ...tempResult,\r\n          xpData: response.xpData\r\n        };\r\n        setResult(resultWithXP);\r\n\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0;\r\n    setSecondsLeft(totalSeconds);\r\n    setStartTime(Date.now()); // Record start time for XP calculation\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\r\n    if (params.id) {\r\n      getExamData();\r\n    } else {\r\n      console.error(\"No exam ID provided in URL parameters\");\r\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\r\n      navigate('/user/quiz');\r\n    }\r\n  }, [params.id, getExamData, navigate]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Add fullscreen class for all quiz views (instructions, questions, results)\r\n  useEffect(() => {\r\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\r\n      document.body.classList.add(\"quiz-fullscreen\");\r\n    } else {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    };\r\n  }, [view]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n          questions={questions}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        isLoading ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\">\r\n                <svg className=\"w-12 h-12 text-white animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-blue-800 mb-4\">Loading Quiz...</h3>\r\n              <p className=\"text-blue-600 text-lg\">\r\n                Please wait while we prepare your questions.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-6 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. This could be due to:\r\n              </p>\r\n              <ul className=\"text-left text-amber-700 mb-8 space-y-2\">\r\n                <li>• Questions not properly linked to this exam</li>\r\n                <li>• Database connection issues</li>\r\n                <li>• Exam configuration problems</li>\r\n              </ul>\r\n              <div className=\"space-y-3\">\r\n                <button\r\n                  onClick={repairExamQuestions}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔧 Repair Questions\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    console.log(\"Retrying exam data fetch...\");\r\n                    getExamData();\r\n                  }}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔄 Retry Loading\r\n                </button>\r\n                <button\r\n                  onClick={() => navigate('/user/quiz')}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  ← Back to Quiz List\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <SimpleQuizRenderer\r\n            question={questions[selectedQuestionIndex]}\r\n            questionIndex={selectedQuestionIndex}\r\n            totalQuestions={questions.length}\r\n            selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n            onAnswerSelect={(answer) => setSelectedOptions({...selectedOptions, [selectedQuestionIndex]: answer})}\r\n            onNext={() => {\r\n              if (selectedQuestionIndex === questions.length - 1) {\r\n                calculateResult();\r\n              } else {\r\n                setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n              }\r\n            }}\r\n            onPrevious={() => {\r\n              if (selectedQuestionIndex > 0) {\r\n                setSelectedQuestionIndex(selectedQuestionIndex - 1);\r\n              }\r\n            }}\r\n            timeLeft={secondsLeft}\r\n            examTitle={examData?.name || \"Quiz\"}\r\n          />\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\">\r\n              {/* Modern Header */}\r\n              <div className={`px-8 py-10 text-center relative ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\"\r\n                  : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"\r\n              }`}>\r\n                <div className=\"relative\">\r\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500 to-green-600\"\r\n                      : \"bg-gradient-to-br from-amber-500 to-orange-600\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-12 h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${\r\n                    result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"}\r\n                  </h1>\r\n                  <p className=\"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've mastered this exam with flying colors!\"\r\n                      : \"Every challenge makes you stronger. Try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modern Statistics Cards */}\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                  {/* Score Card */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-blue-600 mb-2 tracking-tight\">\r\n                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-blue-700/80 uppercase tracking-wider\">Your Score</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-emerald-600 mb-2 tracking-tight\">\r\n                        {result.correctAnswers?.length || 0}/{questions.length}\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\">Correct</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\"\r\n                      : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"\r\n                  }`}>\r\n                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${\r\n                      result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"\r\n                    } to-transparent`}></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className={`text-4xl font-black mb-2 tracking-tight ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                      </div>\r\n                      <div className={`text-sm font-bold uppercase tracking-wider ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Progress Visualization */}\r\n                <div className=\"mb-8\">\r\n                  <div className=\"relative bg-slate-100 rounded-2xl p-6\">\r\n                    <div className=\"text-center mb-4\">\r\n                      <h3 className=\"text-lg font-bold text-slate-700 mb-1\">Performance Overview</h3>\r\n                      <p className=\"text-sm text-slate-500\">Your achievement level</p>\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\">\r\n                        <div\r\n                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${\r\n                            result.verdict === \"Pass\"\r\n                              ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\"\r\n                              : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"\r\n                          }`}\r\n                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                        >\r\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"></div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center mt-3\">\r\n                        <span className=\"text-xs font-medium text-slate-500\">0%</span>\r\n                        <span className={`text-lg font-black tracking-tight ${\r\n                          result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                        }`}>\r\n                          {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                        </span>\r\n                        <span className=\"text-xs font-medium text-slate-500\">100%</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* XP Display */}\r\n                {result.xpData && (\r\n                  <div className=\"mb-8\">\r\n                    <XPResultDisplay xpData={result.xpData} />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Modern Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Review Answers</span>\r\n                  </button>\r\n\r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <SimpleReviewRenderer\r\n          questions={questions}\r\n          selectedOptions={selectedOptions}\r\n          explanations={explanations}\r\n          fetchExplanation={fetchExplanation}\r\n          setView={setView}\r\n          examData={examData}\r\n          setSelectedQuestionIndex={setSelectedQuestionIndex}\r\n          setSelectedOptions={setSelectedOptions}\r\n          setResult={setResult}\r\n          setTimeUp={setTimeUp}\r\n          setSecondsLeft={setSecondsLeft}\r\n          setExplanations={setExplanations}\r\n        />\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,uBAAuB,EAAEC,2BAA2B,QAAQ,wBAAwB;AAC7F,OAAOC,eAAe,MAAM,4CAA4C;;AAExE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa;EAAEC,cAAc;EAAEC,cAAc;EAAEC,cAAc;EAAEC,MAAM;EAAEC,UAAU;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EACnJ,IAAI,CAACR,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAC7C,oBACEF,OAAA;MAAKW,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACrGZ,OAAA;QAAKW,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC5DZ,OAAA;UAAIW,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEhB,OAAA;UAAGW,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAA6B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,YAAY,GAAGC,MAAM,CAAChB,QAAQ,CAACiB,IAAI,IAAI,EAAE,CAAC;EAChD,MAAMC,UAAU,GAAGF,MAAM,CAAChB,QAAQ,CAACkB,UAAU,IAAI,EAAE,CAAC;EACpD,MAAMC,aAAa,GAAGH,MAAM,CAAChB,QAAQ,CAACmB,aAAa,IAAI,EAAE,CAAC;EAC1D,MAAMC,aAAa,GAAGJ,MAAM,CAAChB,QAAQ,CAACoB,aAAa,IAAI,EAAE,CAAC;EAC1D,MAAMC,QAAQ,GAAGrB,QAAQ,CAACsB,KAAK,IAAItB,QAAQ,CAACqB,QAAQ,IAAI,EAAE;;EAE1D;EACA,IAAIE,YAAY,GAAG,EAAE;EACrB,IAAIvB,QAAQ,CAACwB,OAAO,EAAE;IACpB,IAAI;MACF,IAAIC,KAAK,CAACC,OAAO,CAAC1B,QAAQ,CAACwB,OAAO,CAAC,EAAE;QACnCD,YAAY,GAAGvB,QAAQ,CAACwB,OAAO,CAC5BG,MAAM,CAACC,GAAG,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,CAC7CC,GAAG,CAACD,GAAG,IAAIZ,MAAM,CAACY,GAAG,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAC9BH,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC;MAClC,CAAC,MAAM,IAAI,OAAO/B,QAAQ,CAACwB,OAAO,KAAK,QAAQ,EAAE;QAC/CD,YAAY,GAAGS,MAAM,CAACC,MAAM,CAACjC,QAAQ,CAACwB,OAAO,CAAC,CAC3CG,MAAM,CAACC,GAAG,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,CAC7CC,GAAG,CAACD,GAAG,IAAIZ,MAAM,CAACY,GAAG,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAC9BH,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACG,MAAM,GAAG,CAAC,CAAC;MAClC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDX,YAAY,GAAG,EAAE;IACnB;EACF;EAEA,MAAMa,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;EAED,MAAMC,aAAa,GAAGrC,QAAQ,IAAI,EAAE;EAEpC,oBACET,OAAA;IAAKW,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExEZ,OAAA;MAAKW,SAAS,EAAC,yEAAyE;MAAAC,QAAA,eACtFZ,OAAA;QAAKW,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1CZ,OAAA;UAAKW,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDZ,OAAA;YAAKW,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CZ,OAAA;cAAKW,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClFZ,OAAA;gBAAMW,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACNhB,OAAA;cAAAY,QAAA,gBACEZ,OAAA;gBAAIW,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAEM,MAAM,CAACR,SAAS;cAAC;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC5EhB,OAAA;gBAAGW,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAC,WAAS,EAACT,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;cAAA;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhB,OAAA;YAAKW,SAAS,EAAG,iEACfmC,aAAa,GAAG,0BAA0B,GAAG,aAC9C,EAAE;YAAAlC,QAAA,gBACDZ,OAAA;cAAKW,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxChB,OAAA;cAAKW,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAE0B,UAAU,CAAC7B,QAAQ;YAAC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhB,OAAA;MAAKW,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CZ,OAAA;QAAKW,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEtDZ,OAAA;UAAKW,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBZ,OAAA;YAAKW,SAAS,EAAC,6DAA6D;YAAAC,QAAA,eAC1EZ,OAAA;cAAMW,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GAAC,WAAS,EAACT,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhB,OAAA;UAAKW,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBZ,OAAA;YAAIW,SAAS,EAAC,mDAAmD;YAAAC,QAAA,EAC9DK;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAGLO,QAAQ,iBACPvB,OAAA;UAAKW,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/BZ,OAAA;YAAKW,SAAS,EAAC,0EAA0E;YAAAC,QAAA,eACvFZ,OAAA;cACE+C,GAAG,EAAExB,QAAS;cACdyB,GAAG,EAAC,UAAU;cACdrC,SAAS,EAAC;YAAoC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDhB,OAAA;UAAKW,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvBQ,UAAU,KAAK,SAAS,IAAIK,YAAY,CAACQ,MAAM,GAAG,CAAC;UAClD;UACAR,YAAY,CAACM,GAAG,CAAC,CAACkB,MAAM,EAAEC,KAAK,KAAK;YAClC,MAAMC,YAAY,GAAGjC,MAAM,CAACkC,YAAY,CAAC,EAAE,GAAGF,KAAK,CAAC,CAAC,CAAC;YACtD,MAAMG,UAAU,GAAGhD,cAAc,KAAK6C,KAAK;YAE3C,oBACElD,OAAA;cAEEsD,OAAO,EAAEA,CAAA,KAAMhD,cAAc,CAAC4C,KAAK,CAAE;cACrCvC,SAAS,EAAG,wEACV0C,UAAU,GACN,0CAA0C,GAC1C,iEACL,EAAE;cAAAzC,QAAA,eAEHZ,OAAA;gBAAKW,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CZ,OAAA;kBAAKW,SAAS,EAAG,2EACf0C,UAAU,GACN,wBAAwB,GACxB,2BACL,EAAE;kBAAAzC,QAAA,EACAuC;gBAAY;kBAAAtC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNhB,OAAA;kBAAMW,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAEM,MAAM,CAAC+B,MAAM;gBAAC;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAC3DqC,UAAU,iBACTrD,OAAA;kBAAKW,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,eAChFZ,OAAA;oBAAKW,SAAS,EAAC,oBAAoB;oBAAC4C,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAA5C,QAAA,eACzEZ,OAAA;sBAAMyD,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,oHAAoH;sBAACC,QAAQ,EAAC;oBAAS;sBAAA9C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC,GAxBDkC,KAAK;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAyBJ,CAAC;UAEb,CAAC,CAAC;UAAA;UAEF;UACAhB,OAAA;YAAKW,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBZ,OAAA;cAAOW,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRhB,OAAA;cACE4D,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExD,cAAc,IAAI,EAAG;cAC5ByD,QAAQ,EAAGC,CAAC,IAAKzD,cAAc,CAACyD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDI,WAAW,EAAC,0BAA0B;cACtCtD,SAAS,EAAC;YAAiJ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5J,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhB,OAAA;QAAKW,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDZ,OAAA;UACEsD,OAAO,EAAE9C,UAAW;UACpB0D,QAAQ,EAAE/D,aAAa,KAAK,CAAE;UAC9BQ,SAAS,EAAG,kEACVR,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,oEACL,EAAE;UAAAS,QAAA,EACJ;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAEThB,OAAA;UAAKW,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BZ,OAAA;YAAKW,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1DhB,OAAA;YAAKW,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDZ,OAAA;cACEW,SAAS,EAAC,0DAA0D;cACpEwD,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAG,CAACjE,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAI;cAAG;YAAE;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhB,OAAA;UACEsD,OAAO,EAAE/C,MAAO;UAChBI,SAAS,EAAC,mIAAmI;UAAAC,QAAA,EAE5IT,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;QAAQ;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAqD,EAAA,GArMMpE,kBAAkB;AAsMxB,MAAMqE,oBAAoB,GAAGA,CAAC;EAC5BC,SAAS;EACTC,eAAe;EACfC,YAAY;EACZC,gBAAgB;EAChBC,OAAO;EACPC,QAAQ;EACRC,wBAAwB;EACxBC,kBAAkB;EAClBC,SAAS;EACTC,SAAS;EACTC,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,IAAI,CAACX,SAAS,IAAI,CAAC5C,KAAK,CAACC,OAAO,CAAC2C,SAAS,CAAC,EAAE;IAC3C,oBACEvE,OAAA;MAAKW,SAAS,EAAC,wFAAwF;MAAAC,QAAA,eACrGZ,OAAA;QAAKW,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAC5DZ,OAAA;UAAIW,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEhB,OAAA;UAAGW,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEhB,OAAA;IAAKW,SAAS,EAAC,6EAA6E;IAAAC,QAAA,eAC1FZ,OAAA;MAAKW,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCZ,OAAA;QAAKW,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BZ,OAAA;UAAKW,SAAS,EAAC,kFAAkF;UAAAC,QAAA,gBAC/FZ,OAAA;YAAIW,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEhB,OAAA;YAAGW,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhB,OAAA;QAAKW,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5B2D,SAAS,CAACxC,GAAG,CAAC,CAAC7B,QAAQ,EAAEgD,KAAK,KAAK;UAClC;UACA,IAAI,CAAChD,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;YAC7C,OAAO,IAAI;UACb;;UAEA;UACA,MAAMe,YAAY,GAAGC,MAAM,CAAChB,QAAQ,CAACiB,IAAI,IAAI,EAAE,CAAC;UAChD,MAAMC,UAAU,GAAGF,MAAM,CAAChB,QAAQ,CAACkB,UAAU,IAAI,EAAE,CAAC;UACpD,MAAMC,aAAa,GAAGnB,QAAQ,CAACmB,aAAa;UAC5C,MAAMC,aAAa,GAAGpB,QAAQ,CAACoB,aAAa;UAC5C,MAAM6D,UAAU,GAAGX,eAAe,CAACtB,KAAK,CAAC;;UAEzC;UACA,IAAIkC,SAAS,GAAG,KAAK;UACrB,IAAIC,iBAAiB,GAAG,EAAE;UAC1B,IAAIC,cAAc,GAAG,EAAE;UAEvB,IAAIlE,UAAU,KAAK,SAAS,EAAE;YAC5BgE,SAAS,GAAG/D,aAAa,KAAK8D,UAAU;;YAExC;YACA,IAAIjF,QAAQ,CAACwB,OAAO,IAAIL,aAAa,KAAKkE,SAAS,EAAE;cACnD,MAAMC,WAAW,GAAGtF,QAAQ,CAACwB,OAAO,CAACL,aAAa,CAAC;cACnDgE,iBAAiB,GAAG,OAAOG,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGtE,MAAM,CAACsE,WAAW,IAAInE,aAAa,IAAI,SAAS,CAAC;YACvH,CAAC,MAAM;cACLgE,iBAAiB,GAAGnE,MAAM,CAACG,aAAa,IAAI,SAAS,CAAC;YACxD;;YAEA;YACA,IAAInB,QAAQ,CAACwB,OAAO,IAAIyD,UAAU,KAAKI,SAAS,EAAE;cAChD,MAAMC,WAAW,GAAGtF,QAAQ,CAACwB,OAAO,CAACyD,UAAU,CAAC;cAChDG,cAAc,GAAG,OAAOE,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGtE,MAAM,CAACsE,WAAW,IAAIL,UAAU,IAAI,cAAc,CAAC;YACtH,CAAC,MAAM;cACLG,cAAc,GAAGpE,MAAM,CAACiE,UAAU,IAAI,cAAc,CAAC;YACvD;UACF,CAAC,MAAM;YACLC,SAAS,GAAG9D,aAAa,KAAK6D,UAAU;YACxCE,iBAAiB,GAAGnE,MAAM,CAACI,aAAa,IAAI,SAAS,CAAC;YACtDgE,cAAc,GAAGpE,MAAM,CAACiE,UAAU,IAAI,cAAc,CAAC;UACvD;UAEA,oBACEnF,OAAA;YAEEW,SAAS,EAAG,qCACVyE,SAAS,GACL,8BAA8B,GAC9B,0BACL,EAAE;YAAAxE,QAAA,gBAGHZ,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBZ,OAAA;gBAAKW,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCZ,OAAA;kBAAKW,SAAS,EAAC,iHAAiH;kBAAAC,QAAA,EAC7HsC,KAAK,GAAG;gBAAC;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrBZ,OAAA;oBAAGW,SAAS,EAAC,4CAA4C;oBAAAC,QAAA,EAAEK;kBAAY;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhB,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBZ,OAAA;gBAAMW,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3EhB,OAAA;gBAAMW,SAAS,EAAG,eAAcyE,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;gBAAAxE,QAAA,EAC7E0E;cAAc;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,EACNoE,SAAS,gBACRpF,OAAA;gBAAMW,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAEtDhB,OAAA;gBAAMW,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAACoE,SAAS,iBACTpF,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBZ,OAAA;gBAAMW,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9EhB,OAAA;gBAAMW,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAEyE;cAAiB;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvEhB,OAAA;gBAAMW,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,EAGA,CAACoE,SAAS,iBACTpF,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBZ,OAAA;gBACEW,SAAS,EAAC,iKAAiK;gBAC3K2C,OAAO,EAAEA,CAAA,KAAM;kBACboB,gBAAgB,CACdzD,YAAY,EACZoE,iBAAiB,EACjBC,cAAc,EACdpF,QAAQ,CAACsB,KAAK,IAAItB,QAAQ,CAACqB,QAAQ,IAAI,EACzC,CAAC;gBACH,CAAE;gBAAAX,QAAA,gBAEFZ,OAAA;kBAAAY,QAAA,EAAM;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACfhB,OAAA;kBAAAY,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGAyD,YAAY,CAACxD,YAAY,CAAC,iBACzBjB,OAAA;cAAKW,SAAS,EAAC,4FAA4F;cAAAC,QAAA,gBACzGZ,OAAA;gBAAKW,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrCZ,OAAA;kBAAMW,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtDhB,OAAA;kBAAIW,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,EAGL,CAACd,QAAQ,CAACsB,KAAK,IAAItB,QAAQ,CAACqB,QAAQ,kBACnCvB,OAAA;gBAAKW,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,gBACjEZ,OAAA;kBAAKW,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,eACrCZ,OAAA;oBAAMW,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,eAClCZ,OAAA;oBACE+C,GAAG,EAAE7C,QAAQ,CAACsB,KAAK,IAAItB,QAAQ,CAACqB,QAAS;oBACzCyB,GAAG,EAAC,kBAAkB;oBACtBrC,SAAS,EAAC,mEAAmE;oBAC7EwD,KAAK,EAAE;sBAAEsB,QAAQ,EAAE;oBAAQ;kBAAE;oBAAA5E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDhB,OAAA;gBAAKW,SAAS,EAAC,8DAA8D;gBAAAC,QAAA,EAC1EM,MAAM,CAACuD,YAAY,CAACxD,YAAY,CAAC,IAAI,EAAE;cAAC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,GA1FIE,MAAM,CAAChB,QAAQ,CAACwF,GAAG,IAAIxC,KAAK,CAAC;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2F/B,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNhB,OAAA;QAAKW,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAC1EZ,OAAA;UACEW,SAAS,EAAC,+GAA+G;UACzH2C,OAAO,EAAEA,CAAA,KAAMqB,OAAO,CAAC,QAAQ,CAAE;UAAA/D,QAAA,EAClC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAEThB,OAAA;UACEW,SAAS,EAAC,iHAAiH;UAC3H2C,OAAO,EAAEA,CAAA,KAAM;YACb;YACAqB,OAAO,CAAC,cAAc,CAAC;YACvBE,wBAAwB,CAAC,CAAC,CAAC;YAC3BC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACtBC,SAAS,CAAC,CAAC,CAAC,CAAC;YACbC,SAAS,CAAC,KAAK,CAAC;YAChBC,cAAc,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,QAAQ,KAAI,CAAC,CAAC;YACvCT,eAAe,CAAC,CAAC,CAAC,CAAC;UACrB,CAAE;UAAAtE,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC4E,GAAA,GA9MItB,oBAAoB;AAgN1B,SAASuB,SAASA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACnB,MAAM,CAACtB,QAAQ,EAAEuB,WAAW,CAAC,GAAGvH,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC2F,SAAS,EAAE6B,YAAY,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyH,qBAAqB,EAAExB,wBAAwB,CAAC,GAAGjG,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC4F,eAAe,EAAEM,kBAAkB,CAAC,GAAGlG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC0H,MAAM,EAAEvB,SAAS,CAAC,GAAGnG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM2H,MAAM,GAAGvH,SAAS,CAAC,CAAC;EAC1B,MAAMwH,QAAQ,GAAG3H,WAAW,CAAC,CAAC;EAC9B,MAAM4H,QAAQ,GAAG1H,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2H,IAAI,EAAE/B,OAAO,CAAC,GAAG/F,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAM,CAAC+H,WAAW,EAAE1B,cAAc,CAAC,GAAGrG,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgI,MAAM,EAAE5B,SAAS,CAAC,GAAGpG,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiI,UAAU,EAAEC,aAAa,CAAC,GAAGlI,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmI,SAAS,EAAEC,YAAY,CAAC,GAAGpI,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACqI,SAAS,EAAEC,YAAY,CAAC,GAAGtI,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM;IAAEuI;EAAK,CAAC,GAAGrI,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM;IAAE/C,KAAK;IAAEiD;EAAO,CAAC,GAAG5H,aAAa,CAAC,CAAC;EACzC,MAAM,CAACgF,YAAY,EAAES,eAAe,CAAC,GAAGtG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEpD,MAAM0I,WAAW,GAAG5I,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFsI,YAAY,CAAC,IAAI,CAAC;MAClBR,QAAQ,CAACpH,WAAW,CAAC,CAAC,CAAC;MACvBiD,OAAO,CAACkF,GAAG,CAAC,4BAA4B,EAAEhB,MAAM,CAACiB,EAAE,CAAC;MAEpD,MAAMC,QAAQ,GAAG,MAAMxI,WAAW,CAAC;QAAEyI,MAAM,EAAEnB,MAAM,CAACiB;MAAG,CAAC,CAAC;MACzDnF,OAAO,CAACkF,GAAG,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;MAE3CjB,QAAQ,CAACrH,WAAW,CAAC,CAAC,CAAC;MACvB6H,YAAY,CAAC,KAAK,CAAC;MAEnB,IAAIS,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAM/C,QAAQ,GAAG6C,QAAQ,CAACG,IAAI;;QAE9B;QACA,IAAIrD,SAAS,GAAG,EAAE;QAClB,IAAIK,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEL,SAAS,IAAI5C,KAAK,CAACC,OAAO,CAACgD,QAAQ,CAACL,SAAS,CAAC,EAAE;UAC5DA,SAAS,GAAGK,QAAQ,CAACL,SAAS;QAChC,CAAC,MAAM,IAAIK,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE1E,QAAQ,IAAIyB,KAAK,CAACC,OAAO,CAACgD,QAAQ,CAAC1E,QAAQ,CAAC,EAAE;UACjEqE,SAAS,GAAGK,QAAQ,CAAC1E,QAAQ;QAC/B,CAAC,MAAM,IAAI0E,QAAQ,IAAIjD,KAAK,CAACC,OAAO,CAACgD,QAAQ,CAAC,EAAE;UAC9CL,SAAS,GAAGK,QAAQ;QACtB;QAEAvC,OAAO,CAACkF,GAAG,CAAC,YAAY,EAAE3C,QAAQ,CAAC;QACnCvC,OAAO,CAACkF,GAAG,CAAC,kBAAkB,EAAEhD,SAAS,CAACtC,MAAM,CAAC;QACjDI,OAAO,CAACkF,GAAG,CAAC,sBAAsB,EAAErF,MAAM,CAAC2F,IAAI,CAACjD,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhEwB,YAAY,CAAC7B,SAAS,CAAC;QACvB4B,WAAW,CAACvB,QAAQ,CAAC;QACrBK,cAAc,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,QAAQ,KAAI,CAAC,CAAC;QAEvC,IAAIpB,SAAS,CAACtC,MAAM,KAAK,CAAC,EAAE;UAC1BI,OAAO,CAACyF,IAAI,CAAC,iCAAiC,CAAC;UAC/CzF,OAAO,CAACkF,GAAG,CAAC,8BAA8B,EAAEE,QAAQ,CAAC;UACrDjJ,OAAO,CAACuJ,OAAO,CAAC,6DAA6D,CAAC;QAChF;MACF,CAAC,MAAM;QACL1F,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEqF,QAAQ,CAACjJ,OAAO,CAAC;QAC7C6D,OAAO,CAACkF,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAAC;QAC7CjJ,OAAO,CAAC4D,KAAK,CAACqF,QAAQ,CAACjJ,OAAO,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACdoE,QAAQ,CAACrH,WAAW,CAAC,CAAC,CAAC;MACvB6H,YAAY,CAAC,KAAK,CAAC;MACnB3E,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD5D,OAAO,CAAC4D,KAAK,CAACA,KAAK,CAAC5D,OAAO,IAAI,wCAAwC,CAAC;IAC1E;EACF,CAAC,EAAE,CAAC+H,MAAM,CAACiB,EAAE,EAAEhB,QAAQ,CAAC,CAAC;EAEzB,MAAMwB,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAAChG,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAE2F;IAAK,CAAC,GAAG,MAAMhI,uBAAuB,CAACqI,OAAO,CAAC;IACvD,OAAOL,IAAI;EACb,CAAC;EAED,MAAMM,eAAe,GAAGxJ,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF;MACA,IAAI,CAACyI,IAAI,IAAI,CAACA,IAAI,CAACzB,GAAG,EAAE;QACtBlH,OAAO,CAAC4D,KAAK,CAAC,sCAAsC,CAAC;QACrDqE,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAD,QAAQ,CAACpH,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAM+I,eAAe,GAAG,EAAE;MAC1B,MAAMC,QAAQ,GAAG,EAAE;MAEnB7D,SAAS,CAAC8D,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAAClH,UAAU,KAAK,WAAW,IAAIkH,CAAC,CAAClH,UAAU,KAAK,mBAAmB,EAAE;UACxEgH,QAAQ,CAACI,IAAI,CAACD,GAAG,CAAC;UAClBJ,eAAe,CAACK,IAAI,CAAC;YACnBtI,QAAQ,EAAEoI,CAAC,CAACnH,IAAI;YAChBsH,cAAc,EAAEH,CAAC,CAAChH,aAAa,IAAIgH,CAAC,CAACjH,aAAa;YAClD8D,UAAU,EAAEX,eAAe,CAAC+D,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMG,UAAU,GAAG,MAAMV,oBAAoB,CAACG,eAAe,CAAC;MAC9D,MAAMQ,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACL,OAAO,CAAEO,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACtC,MAAM,IAAI,OAAOsC,CAAC,CAACtC,MAAM,CAAClB,SAAS,KAAK,SAAS,EAAE;UACvDuD,MAAM,CAACC,CAAC,CAAC1I,QAAQ,CAAC,GAAG0I,CAAC,CAACtC,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOsC,CAAC,CAACxD,SAAS,KAAK,SAAS,EAAE;UAC3CuD,MAAM,CAACC,CAAC,CAAC1I,QAAQ,CAAC,GAAG;YAAEkF,SAAS,EAAEwD,CAAC,CAACxD,SAAS;YAAEyD,MAAM,EAAED,CAAC,CAACC,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,YAAY,GAAG,EAAE;MAEvBzE,SAAS,CAAC8D,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMU,aAAa,GAAGzE,eAAe,CAAC+D,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAAClH,UAAU,KAAK,WAAW,IAAIkH,CAAC,CAAClH,UAAU,KAAK,mBAAmB,EAAE;UACxE,MAAM;YAAEgE,SAAS,GAAG,KAAK;YAAEyD,MAAM,GAAG;UAAG,CAAC,GAAGF,MAAM,CAACL,CAAC,CAACnH,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAM+H,QAAQ,GAAG;YAAE,GAAGZ,CAAC;YAAEnD,UAAU,EAAE8D,aAAa;YAAEJ;UAAO,CAAC;UAE5D,IAAIzD,SAAS,EAAE;YACb0D,cAAc,CAACN,IAAI,CAACU,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACP,IAAI,CAACU,QAAQ,CAAC;YAC3BF,YAAY,CAACR,IAAI,CAAC;cAChBtI,QAAQ,EAAEoI,CAAC,CAACnH,IAAI;cAChBsH,cAAc,EAAEH,CAAC,CAAChH,aAAa,IAAIgH,CAAC,CAACjH,aAAa;cAClD8D,UAAU,EAAE8D;YACd,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IAAIX,CAAC,CAAClH,UAAU,KAAK,SAAS,EAAE;UACrC,MAAM+H,UAAU,GAAGb,CAAC,CAACjH,aAAa;UAClC,MAAM+H,YAAY,GAAId,CAAC,CAAC5G,OAAO,IAAI4G,CAAC,CAAC5G,OAAO,CAACyH,UAAU,CAAC,IAAKA,UAAU;UACvE,MAAME,SAAS,GAAIf,CAAC,CAAC5G,OAAO,IAAI4G,CAAC,CAAC5G,OAAO,CAACuH,aAAa,CAAC,IAAKA,aAAa,IAAI,EAAE;UAEhF,MAAM7D,SAAS,GAAG+D,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGZ,CAAC;YAAEnD,UAAU,EAAE8D;UAAc,CAAC;UAEpD,IAAI7D,SAAS,EAAE;YACb0D,cAAc,CAACN,IAAI,CAACU,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACP,IAAI,CAACU,QAAQ,CAAC;YAC3BF,YAAY,CAACR,IAAI,CAAC;cAChBtI,QAAQ,EAAEoI,CAAC,CAACnH,IAAI;cAChBsH,cAAc,EAAEW,YAAY;cAC5BjE,UAAU,EAAEkE;YACd,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,SAAS,GAAGrC,SAAS,GAAGxE,IAAI,CAACC,KAAK,CAAC,CAAC6G,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGvC,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC;MAC7E,MAAMwC,gBAAgB,GAAG,CAAC,CAAA7E,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,QAAQ,KAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;MAEzD;MACA,MAAMvF,cAAc,GAAGmE,SAAS,CAACtC,MAAM;MACvC,MAAMyH,YAAY,GAAGZ,cAAc,CAAC7G,MAAM;MAC1C,MAAM0H,eAAe,GAAGlH,IAAI,CAACmH,KAAK,CAAEF,YAAY,GAAGtJ,cAAc,GAAI,GAAG,CAAC;MACzE,MAAMyJ,MAAM,GAAGH,YAAY,GAAG,EAAE,CAAC,CAAC;;MAElC;MACA,MAAMI,iBAAiB,GAAGlF,QAAQ,CAACmF,YAAY,IAAI,EAAE,CAAC,CAAC;MACvD,MAAMC,OAAO,GAAGL,eAAe,IAAIG,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEtE,MAAMG,UAAU,GAAG;QACjBnB,cAAc,EAAEA,cAAc,IAAI,EAAE;QACpCC,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCiB,OAAO,EAAEA,OAAO,IAAI,MAAM;QAC1BE,KAAK,EAAEP,eAAe;QACtBE,MAAM,EAAEA,MAAM;QACdzJ,cAAc,EAAEA,cAAc;QAC9BkJ,SAAS,EAAEA,SAAS;QACpBG,gBAAgB,EAAEA;MACpB,CAAC;MAED1E,SAAS,CAACkF,UAAU,CAAC;MAErB,MAAMxC,QAAQ,GAAG,MAAMvI,SAAS,CAAC;QAC/BiL,IAAI,EAAE5D,MAAM,CAACiB,EAAE;QACflB,MAAM,EAAE2D,UAAU;QAClB9C,IAAI,EAAEA,IAAI,CAACzB;MACb,CAAC,CAAC;MAEF,IAAI+B,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMyC,YAAY,GAAG;UACnB,GAAGH,UAAU;UACbI,MAAM,EAAE5C,QAAQ,CAAC4C;QACnB,CAAC;QACDtF,SAAS,CAACqF,YAAY,CAAC;QAEvBzF,OAAO,CAAC,QAAQ,CAAC;QACjB2F,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,IAAIC,KAAK,CAACR,OAAO,KAAK,MAAM,GAAGtK,SAAS,GAAGC,SAAS,CAAC,CAAC8K,IAAI,CAAC,CAAC;MAC9D,CAAC,MAAM;QACLjM,OAAO,CAAC4D,KAAK,CAACqF,QAAQ,CAACjJ,OAAO,CAAC;MACjC;MACAgI,QAAQ,CAACrH,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOiD,KAAK,EAAE;MACdoE,QAAQ,CAACrH,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAAC4D,KAAK,CAACA,KAAK,CAAC5D,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC+F,SAAS,EAAEC,eAAe,EAAEI,QAAQ,EAAE2B,MAAM,CAACiB,EAAE,EAAEL,IAAI,EAAEV,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE/E,MAAM9B,gBAAgB,GAAG,MAAAA,CAAOxE,QAAQ,EAAEuI,cAAc,EAAEtD,UAAU,EAAE5D,QAAQ,KAAK;IACjF,IAAI;MACFiF,QAAQ,CAACpH,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMqI,QAAQ,GAAG,MAAM5H,2BAA2B,CAAC;QAAEK,QAAQ;QAAEuI,cAAc;QAAEtD,UAAU;QAAE5D;MAAS,CAAC,CAAC;MACtGiF,QAAQ,CAACrH,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIsI,QAAQ,CAACE,OAAO,EAAE;QACpBzC,eAAe,CAAEwF,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAACxK,QAAQ,GAAGuH,QAAQ,CAACkD;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACLnM,OAAO,CAAC4D,KAAK,CAACqF,QAAQ,CAACrF,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdoE,QAAQ,CAACrH,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAAC4D,KAAK,CAACA,KAAK,CAAC5D,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMoM,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAG,CAAAjG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,QAAQ,KAAI,CAAC;IAC5CV,cAAc,CAAC4F,YAAY,CAAC;IAC5B3D,YAAY,CAACqC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1B,MAAMsB,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtC9F,cAAc,CAAE+F,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACLhG,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACR8B,aAAa,CAACgE,aAAa,CAAC;EAC9B,CAAC;EAEDnM,SAAS,CAAC,MAAM;IACd,IAAIiI,MAAM,IAAIF,IAAI,KAAK,WAAW,EAAE;MAClCuE,aAAa,CAACpE,UAAU,CAAC;MACzBqB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACtB,MAAM,EAAEF,IAAI,EAAEG,UAAU,EAAEqB,eAAe,CAAC,CAAC;EAE/CvJ,SAAS,CAAC,MAAM;IACd0D,OAAO,CAACkF,GAAG,CAAC,kCAAkC,EAAEhB,MAAM,CAACiB,EAAE,CAAC;IAC1D,IAAIjB,MAAM,CAACiB,EAAE,EAAE;MACbF,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLjF,OAAO,CAACD,KAAK,CAAC,uCAAuC,CAAC;MACtD5D,OAAO,CAAC4D,KAAK,CAAC,sDAAsD,CAAC;MACrEqE,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,MAAM,CAACiB,EAAE,EAAEF,WAAW,EAAEb,QAAQ,CAAC,CAAC;EAEtC9H,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIkI,UAAU,EAAE;QACdoE,aAAa,CAACpE,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACAlI,SAAS,CAAC,MAAM;IACd,IAAI+H,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACxEwE,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChD,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD;;IAEA;IACA,OAAO,MAAM;MACXJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,CAAC5E,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM6E,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF/E,QAAQ,CAACpH,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMqI,QAAQ,GAAG,MAAM+D,KAAK,CAAC,kCAAkC,EAAE;QAC/DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDT,IAAI,EAAEU,IAAI,CAACC,SAAS,CAAC;UAAEpE,MAAM,EAAEnB,MAAM,CAACiB;QAAG,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMI,IAAI,GAAG,MAAMH,QAAQ,CAACsE,IAAI,CAAC,CAAC;MAClC,IAAInE,IAAI,CAACD,OAAO,EAAE;QAChBnJ,OAAO,CAACmJ,OAAO,CAACC,IAAI,CAACpJ,OAAO,CAAC;QAC7B;QACA8I,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACL9I,OAAO,CAAC4D,KAAK,CAACwF,IAAI,CAACpJ,OAAO,CAAC;MAC7B;IACF,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,iCAAiC,CAAC;IAClD,CAAC,SAAS;MACRoE,QAAQ,CAACrH,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,IAAI,CAACgI,IAAI,EAAE;IACT,oBACEnH,OAAA;MAAKW,SAAS,EAAC,qGAAqG;MAAAC,QAAA,eAClHZ,OAAA;QAAKW,SAAS,EAAC,2GAA2G;QAAAC,QAAA,gBACxHZ,OAAA;UAAKW,SAAS,EAAC,kFAAkF;UAAAC,QAAA,eAC/FZ,OAAA;YAAKW,SAAS,EAAC,sBAAsB;YAAC4C,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA5C,QAAA,eAC3EZ,OAAA;cAAMyD,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,8JAA8J;cAACC,QAAQ,EAAC;YAAS;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhB,OAAA;UAAIW,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFhB,OAAA;UAAGW,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvGhB,OAAA;UACEW,SAAS,EAAC,mNAAmN;UAC7N2C,OAAO,EAAEA,CAAA,KAAMmD,QAAQ,CAAC,QAAQ,CAAE;UAAA7F,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAO4D,QAAQ,gBACb5E,OAAA;IAAKW,SAAS,EAAC,oEAAoE;IAAAC,QAAA,GAEhF8F,IAAI,KAAK,cAAc,iBACtB1G,OAAA,CAACX,YAAY;MACXuF,QAAQ,EAAEA,QAAS;MACnBD,OAAO,EAAEA,OAAQ;MACjBiG,UAAU,EAAEA,UAAW;MACvBrG,SAAS,EAAEA;IAAU;MAAA1D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACF,EAEA0F,IAAI,KAAK,WAAW,KACnBK,SAAS,gBACP/G,OAAA;MAAKW,SAAS,EAAC,qGAAqG;MAAAC,QAAA,eAClHZ,OAAA;QAAKW,SAAS,EAAC,2GAA2G;QAAAC,QAAA,gBACxHZ,OAAA;UAAKW,SAAS,EAAC,2IAA2I;UAAAC,QAAA,eACxJZ,OAAA;YAAKW,SAAS,EAAC,mCAAmC;YAAC4C,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAAA5C,QAAA,gBAChFZ,OAAA;cAAQW,SAAS,EAAC,YAAY;cAACqL,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAACrD,CAAC,EAAC,IAAI;cAACsD,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC;YAAG;cAAAtL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACrGhB,OAAA;cAAMW,SAAS,EAAC,YAAY;cAAC4C,IAAI,EAAC,cAAc;cAACG,CAAC,EAAC;YAAiH;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhB,OAAA;UAAIW,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1EhB,OAAA;UAAGW,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJuD,SAAS,CAACtC,MAAM,KAAK,CAAC,gBACxBjC,OAAA;MAAKW,SAAS,EAAC,sGAAsG;MAAAC,QAAA,eACnHZ,OAAA;QAAKW,SAAS,EAAC,4GAA4G;QAAAC,QAAA,gBACzHZ,OAAA;UAAKW,SAAS,EAAC,8HAA8H;UAAAC,QAAA,eAC3IZ,OAAA;YAAKW,SAAS,EAAC,sBAAsB;YAAC4C,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA5C,QAAA,eAC3EZ,OAAA;cAAMyD,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,mNAAmN;cAACC,QAAQ,EAAC;YAAS;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhB,OAAA;UAAIW,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9EhB,OAAA;UAAGW,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJhB,OAAA;UAAIW,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBACrDZ,OAAA;YAAAY,QAAA,EAAI;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDhB,OAAA;YAAAY,QAAA,EAAI;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrChB,OAAA;YAAAY,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACLhB,OAAA;UAAKW,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBZ,OAAA;YACEsD,OAAO,EAAEiI,mBAAoB;YAC7B5K,SAAS,EAAC,iNAAiN;YAAAC,QAAA,EAC5N;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThB,OAAA;YACEsD,OAAO,EAAEA,CAAA,KAAM;cACbjB,OAAO,CAACkF,GAAG,CAAC,6BAA6B,CAAC;cAC1CD,WAAW,CAAC,CAAC;YACf,CAAE;YACF3G,SAAS,EAAC,2MAA2M;YAAAC,QAAA,EACtN;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThB,OAAA;YACEsD,OAAO,EAAEA,CAAA,KAAMmD,QAAQ,CAAC,YAAY,CAAE;YACtC9F,SAAS,EAAC,2MAA2M;YAAAC,QAAA,EACtN;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENhB,OAAA,CAACC,kBAAkB;MACjBC,QAAQ,EAAEqE,SAAS,CAAC8B,qBAAqB,CAAE;MAC3ClG,aAAa,EAAEkG,qBAAsB;MACrCjG,cAAc,EAAEmE,SAAS,CAACtC,MAAO;MACjC5B,cAAc,EAAEmE,eAAe,CAAC6B,qBAAqB,CAAE;MACvD/F,cAAc,EAAG8L,MAAM,IAAKtH,kBAAkB,CAAC;QAAC,GAAGN,eAAe;QAAE,CAAC6B,qBAAqB,GAAG+F;MAAM,CAAC,CAAE;MACtG7L,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAI8F,qBAAqB,KAAK9B,SAAS,CAACtC,MAAM,GAAG,CAAC,EAAE;UAClDiG,eAAe,CAAC,CAAC;QACnB,CAAC,MAAM;UACLrD,wBAAwB,CAACwB,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACF7F,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAI6F,qBAAqB,GAAG,CAAC,EAAE;UAC7BxB,wBAAwB,CAACwB,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACF5F,QAAQ,EAAEkG,WAAY;MACtBjG,SAAS,EAAE,CAAAkE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEzD,IAAI,KAAI;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CACF,CACF,EAEA0F,IAAI,KAAK,QAAQ,iBAChB1G,OAAA;MAAKW,SAAS,EAAC,6EAA6E;MAAAC,QAAA,GACzF0F,MAAM,CAAC0D,OAAO,KAAK,MAAM,iBAAIhK,OAAA,CAACR,QAAQ;QAAC4E,KAAK,EAAEA,KAAM;QAACiD,MAAM,EAAEA;MAAO;QAAAxG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAExEhB,OAAA;QAAKW,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCZ,OAAA;UAAKW,SAAS,EAAC,+FAA+F;UAAAC,QAAA,gBAE5GZ,OAAA;YAAKW,SAAS,EAAG,mCACf2F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GACrB,sEAAsE,GACtE,oEACL,EAAE;YAAApJ,QAAA,eACDZ,OAAA;cAAKW,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBZ,OAAA;gBAAKW,SAAS,EAAG,iFACf2F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GACrB,iDAAiD,GACjD,gDACL,EAAE;gBAAApJ,QAAA,eACDZ,OAAA;kBACE+C,GAAG,EAAEuD,MAAM,CAAC0D,OAAO,KAAK,MAAM,GAAG1K,IAAI,GAAGC,IAAK;kBAC7CyD,GAAG,EAAEsD,MAAM,CAAC0D,OAAQ;kBACpBrJ,SAAS,EAAC;gBAA0B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNhB,OAAA;gBAAIW,SAAS,EAAG,2CACd2F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;gBAAApJ,QAAA,EACA0F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GAAG,iBAAiB,GAAG;cAAe;gBAAAnJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACLhB,OAAA;gBAAGW,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAC/E0F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GACtB,+CAA+C,GAC/C;cAAgD;gBAAAnJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhB,OAAA;YAAKW,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBZ,OAAA;cAAKW,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBAEzDZ,OAAA;gBAAKW,SAAS,EAAC,yKAAyK;gBAAAC,QAAA,gBACtLZ,OAAA;kBAAKW,SAAS,EAAC;gBAAqI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3JhB,OAAA;kBAAKW,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCZ,OAAA;oBAAKW,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,GACnE6B,IAAI,CAACmH,KAAK,CAAE,CAAC,EAAA7D,qBAAA,GAAAO,MAAM,CAACwC,cAAc,cAAA/C,qBAAA,uBAArBA,qBAAA,CAAuB9D,MAAM,KAAI,CAAC,IAAIsC,SAAS,CAACtC,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNhB,OAAA;oBAAKW,SAAS,EAAC,6DAA6D;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNhB,OAAA;gBAAKW,SAAS,EAAC,8KAA8K;gBAAAC,QAAA,gBAC3LZ,OAAA;kBAAKW,SAAS,EAAC;gBAAwI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9JhB,OAAA;kBAAKW,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCZ,OAAA;oBAAKW,SAAS,EAAC,0DAA0D;oBAAAC,QAAA,GACtE,EAAAoF,sBAAA,GAAAM,MAAM,CAACwC,cAAc,cAAA9C,sBAAA,uBAArBA,sBAAA,CAAuB/D,MAAM,KAAI,CAAC,EAAC,GAAC,EAACsC,SAAS,CAACtC,MAAM;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNhB,OAAA;oBAAKW,SAAS,EAAC,gEAAgE;oBAAAC,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNhB,OAAA;gBAAKW,SAAS,EAAG,qGACf2F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GACrB,4EAA4E,GAC5E,yEACL,EAAE;gBAAApJ,QAAA,gBACDZ,OAAA;kBAAKW,SAAS,EAAG,wGACf2F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GAAG,oBAAoB,GAAG,kBACpD;gBAAiB;kBAAAnJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBhB,OAAA;kBAAKW,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCZ,OAAA;oBAAKW,SAAS,EAAG,2CACf2F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;oBAAApJ,QAAA,EACA0F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG;kBAAO;oBAAAnJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACNhB,OAAA;oBAAKW,SAAS,EAAG,8CACf2F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GAAG,qBAAqB,GAAG,mBACrD,EAAE;oBAAApJ,QAAA,EACA0F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GAAG,UAAU,GAAI,QAAOpF,QAAQ,CAACmF,YAAa;kBAAC;oBAAAlJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhB,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBZ,OAAA;gBAAKW,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDZ,OAAA;kBAAKW,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BZ,OAAA;oBAAIW,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/EhB,OAAA;oBAAGW,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNhB,OAAA;kBAAKW,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACvBZ,OAAA;oBAAKW,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChFZ,OAAA;sBACEW,SAAS,EAAG,uFACV2F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GACrB,6DAA6D,GAC7D,2DACL,EAAE;sBACH7F,KAAK,EAAE;wBAAEC,KAAK,EAAG,GAAG,CAAC,EAAA6B,sBAAA,GAAAK,MAAM,CAACwC,cAAc,cAAA7C,sBAAA,uBAArBA,sBAAA,CAAuBhE,MAAM,KAAI,CAAC,IAAIsC,SAAS,CAACtC,MAAM,GAAI,GAAI;sBAAG,CAAE;sBAAArB,QAAA,eAExFZ,OAAA;wBAAKW,SAAS,EAAC;sBAAgE;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNhB,OAAA;oBAAKW,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,gBACrDZ,OAAA;sBAAMW,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9DhB,OAAA;sBAAMW,SAAS,EAAG,qCAChB2F,MAAM,CAAC0D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;sBAAApJ,QAAA,GACA6B,IAAI,CAACmH,KAAK,CAAE,CAAC,EAAA1D,sBAAA,GAAAI,MAAM,CAACwC,cAAc,cAAA5C,sBAAA,uBAArBA,sBAAA,CAAuBjE,MAAM,KAAI,CAAC,IAAIsC,SAAS,CAACtC,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;oBAAA;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPhB,OAAA;sBAAMW,SAAS,EAAC,oCAAoC;sBAAAC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLsF,MAAM,CAAC+D,MAAM,iBACZrK,OAAA;cAAKW,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBZ,OAAA,CAACF,eAAe;gBAACuK,MAAM,EAAE/D,MAAM,CAAC+D;cAAO;gBAAAxJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACN,eAGDhB,OAAA;cAAKW,SAAS,EAAC,gDAAgD;cAAAC,QAAA,eAC7DZ,OAAA;gBACEW,SAAS,EAAC,sPAAsP;gBAChQ2C,OAAO,EAAEA,CAAA,KAAMqB,OAAO,CAAC,QAAQ,CAAE;gBAAA/D,QAAA,gBAEjCZ,OAAA;kBAAKW,SAAS,EAAC;gBAAkI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxJhB,OAAA;kBAAMW,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA0F,IAAI,KAAK,QAAQ,iBAChB1G,OAAA,CAACsE,oBAAoB;MACnBC,SAAS,EAAEA,SAAU;MACrBC,eAAe,EAAEA,eAAgB;MACjCC,YAAY,EAAEA,YAAa;MAC3BC,gBAAgB,EAAEA,gBAAiB;MACnCC,OAAO,EAAEA,OAAQ;MACjBC,QAAQ,EAAEA,QAAS;MACnBC,wBAAwB,EAAEA,wBAAyB;MACnDC,kBAAkB,EAAEA,kBAAmB;MACvCC,SAAS,EAAEA,SAAU;MACrBC,SAAS,EAAEA,SAAU;MACrBC,cAAc,EAAEA,cAAe;MAC/BC,eAAe,EAAEA;IAAgB;MAAArE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC,GACJ,IAAI;AACV;AAAC8E,EAAA,CAjlBQD,SAAS;EAAA,QAMD7G,SAAS,EACPH,WAAW,EACXE,WAAW,EAOXD,WAAW,EAEFW,aAAa;AAAA;AAAA4M,GAAA,GAjBhCxG,SAAS;AAmlBlB,eAAeA,SAAS;AAAC,IAAAxB,EAAA,EAAAuB,GAAA,EAAAyG,GAAA;AAAAC,YAAA,CAAAjI,EAAA;AAAAiI,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}