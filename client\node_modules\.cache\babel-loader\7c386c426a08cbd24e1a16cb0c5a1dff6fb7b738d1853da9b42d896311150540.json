{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbUser, TbUsers, TbTrophy, TbRefresh, TbPlayerPlay, TbPlayerPause, TbClock } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingList = ({\n  users = [],\n  currentUserId = null,\n  layout = 'horizontal',\n  // 'horizontal', 'vertical', 'grid'\n  size = 'medium',\n  showStats = true,\n  className = '',\n  currentUserRef = null,\n  showFindMe = false,\n  onRefresh = null,\n  lastUpdated = null,\n  autoRefresh = false,\n  onAutoRefreshToggle = null\n}) => {\n  _s();\n  const [localShowFindMe, setLocalShowFindMe] = useState(false);\n  const localCurrentUserRef = useRef(null);\n\n  // Use passed refs or local ones\n  const userRef = currentUserRef || localCurrentUserRef;\n  const findMeActive = showFindMe || localShowFindMe;\n\n  // Sort users by rank (no filtering)\n  const sortedUsers = users.sort((a, b) => (a.rank || 0) - (b.rank || 0));\n\n  // Calculate class ranks\n  const usersWithClassRank = sortedUsers.map(user => {\n    // Group users by class and calculate class rank\n    const sameClassUsers = sortedUsers.filter(u => u.class === user.class);\n    const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n    return {\n      ...user,\n      classRank\n    };\n  });\n\n  // Find current user\n  const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n\n  // Scroll to current user\n  const scrollToCurrentUser = () => {\n    if (userRef.current) {\n      userRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setLocalShowFindMe(true);\n      // Hide the highlight after 3 seconds\n      setTimeout(() => setLocalShowFindMe(false), 3000);\n    }\n  };\n\n  // Get layout classes\n  const getLayoutClasses = () => {\n    switch (layout) {\n      case 'vertical':\n        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n      case 'grid':\n        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n      case 'horizontal':\n      default:\n        return 'space-y-3';\n    }\n  };\n\n  // Container animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  // Stats summary with enhanced calculations\n  const totalUsers = users.length;\n  const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium' || u.normalizedSubscriptionStatus === 'premium').length;\n\n  // Use ranking score or XP as the primary metric\n  const topScore = users.length > 0 ? Math.max(...users.map(u => u.rankingScore || u.totalXP || u.totalPoints || 0)) : 0;\n\n  // Calculate additional stats\n  const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n  const averageXP = users.length > 0 ? Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [showStats && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-6 h-6 text-yellow-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Leaderboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 29\n          }, this), lastUpdated && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-1 text-sm text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Updated \", new Date(lastUpdated).toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [onAutoRefreshToggle && /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: onAutoRefreshToggle,\n            className: `px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${autoRefresh ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`,\n            children: [autoRefresh ? /*#__PURE__*/_jsxDEV(TbPlayerPause, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 52\n            }, this) : /*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 92\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: autoRefresh ? 'Auto' : 'Manual'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 33\n          }, this), currentUserId && /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: scrollToCurrentUser,\n            className: \"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUser, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Find Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 sm:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n              className: \"w-5 h-5 text-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: [activeUsers, \" active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-5 h-5 text-yellow-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Premium Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: premiumUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: [totalUsers > 0 ? Math.round(premiumUsers / totalUsers * 100) : 0, \"% premium\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUser, {\n              className: \"w-5 h-5 text-green-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Top Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: topScore.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: \"ranking points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-5 h-5 text-purple-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Avg XP\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: averageXP.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: \"experience points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: containerVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      className: getLayoutClasses(),\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: usersWithClassRank.map((user, index) => {\n          const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n          const rank = user.rank || index + 1;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            ref: isCurrentUser ? userRef : null,\n            layout: true,\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              scale: 0.9\n            },\n            transition: {\n              duration: 0.2\n            },\n            className: `${isCurrentUser && findMeActive ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl' : isCurrentUser ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(UserRankingCard, {\n              user: user,\n              rank: rank,\n              classRank: user.classRank,\n              isCurrentUser: isCurrentUser,\n              layout: layout,\n              size: size,\n              showStats: showStats\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 33\n            }, this)\n          }, user.userId || user._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 13\n    }, this), usersWithClassRank.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No users found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"No users available to display\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 17\n    }, this), currentUserId && usersWithClassRank.length > 10 && /*#__PURE__*/_jsxDEV(motion.button, {\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      onClick: scrollToCurrentUser,\n      className: \"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\",\n      title: \"Find me in ranking\",\n      children: /*#__PURE__*/_jsxDEV(TbUser, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 9\n  }, this);\n};\n_s(UserRankingList, \"RTQ97dkuZ8K2q/XxV28t5FHTxeI=\");\n_c = UserRankingList;\nexport default UserRankingList;\nvar _c;\n$RefreshReg$(_c, \"UserRankingList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "motion", "AnimatePresence", "TbUser", "TbUsers", "TbTrophy", "TbRefresh", "TbPlayerPlay", "TbPlayerPause", "TbClock", "UserRankingCard", "jsxDEV", "_jsxDEV", "UserRankingList", "users", "currentUserId", "layout", "size", "showStats", "className", "currentUserRef", "showFindMe", "onRefresh", "lastUpdated", "autoRefresh", "onAutoRefreshToggle", "_s", "localShowFindMe", "setLocalShowFindMe", "localCurrentUserRef", "userRef", "findMeActive", "sortedUsers", "sort", "a", "b", "rank", "usersWithClassRank", "map", "user", "sameClassUsers", "filter", "u", "class", "classRank", "findIndex", "_id", "userId", "currentUser", "find", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "setTimeout", "getLayoutClasses", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "totalUsers", "length", "premiumUsers", "subscriptionStatus", "normalizedSubscriptionStatus", "topScore", "Math", "max", "rankingScore", "totalXP", "totalPoints", "activeUsers", "totalQuizzesTaken", "averageXP", "round", "reduce", "sum", "children", "div", "initial", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Date", "toLocaleTimeString", "button", "whileHover", "scale", "whileTap", "onClick", "toLocaleString", "variants", "index", "isCurrentUser", "ref", "exit", "duration", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingList.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbUser, TbUsers, TbTrophy, TbRefresh, TbPlayerPlay, TbPlayerPause, TbClock } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\n\nconst UserRankingList = ({\n    users = [],\n    currentUserId = null,\n    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'\n    size = 'medium',\n    showStats = true,\n    className = '',\n    currentUserRef = null,\n    showFindMe = false,\n    onRefresh = null,\n    lastUpdated = null,\n    autoRefresh = false,\n    onAutoRefreshToggle = null\n}) => {\n    const [localShowFindMe, setLocalShowFindMe] = useState(false);\n    const localCurrentUserRef = useRef(null);\n\n    // Use passed refs or local ones\n    const userRef = currentUserRef || localCurrentUserRef;\n    const findMeActive = showFindMe || localShowFindMe;\n\n    // Sort users by rank (no filtering)\n    const sortedUsers = users.sort((a, b) => (a.rank || 0) - (b.rank || 0));\n\n    // Calculate class ranks\n    const usersWithClassRank = sortedUsers.map(user => {\n        // Group users by class and calculate class rank\n        const sameClassUsers = sortedUsers.filter(u => u.class === user.class);\n        const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n        return { ...user, classRank };\n    });\n\n    // Find current user\n    const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n\n    // Scroll to current user\n    const scrollToCurrentUser = () => {\n        if (userRef.current) {\n            userRef.current.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n            setLocalShowFindMe(true);\n            // Hide the highlight after 3 seconds\n            setTimeout(() => setLocalShowFindMe(false), 3000);\n        }\n    };\n\n    // Get layout classes\n    const getLayoutClasses = () => {\n        switch (layout) {\n            case 'vertical':\n                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n            case 'grid':\n                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n            case 'horizontal':\n            default:\n                return 'space-y-3';\n        }\n    };\n\n    // Container animation variants\n    const containerVariants = {\n        hidden: { opacity: 0 },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n\n    // Stats summary with enhanced calculations\n    const totalUsers = users.length;\n    const premiumUsers = users.filter(u =>\n        u.subscriptionStatus === 'active' ||\n        u.subscriptionStatus === 'premium' ||\n        u.normalizedSubscriptionStatus === 'premium'\n    ).length;\n\n    // Use ranking score or XP as the primary metric\n    const topScore = users.length > 0 ? Math.max(...users.map(u =>\n        u.rankingScore || u.totalXP || u.totalPoints || 0\n    )) : 0;\n\n    // Calculate additional stats\n    const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n    const averageXP = users.length > 0 ?\n        Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n\n    return (\n        <div className={`space-y-6 ${className}`}>\n            {/* Header with Stats */}\n            {showStats && (\n                <motion.div\n                    initial={{ opacity: 0, y: -20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\"\n                >\n                    <div className=\"flex items-center justify-between mb-4\">\n                        <div className=\"flex items-center space-x-4\">\n                            <h2 className=\"text-2xl font-bold text-gray-900 flex items-center space-x-2\">\n                                <TbTrophy className=\"w-6 h-6 text-yellow-500\" />\n                                <span>Leaderboard</span>\n                            </h2>\n\n                            {lastUpdated && (\n                                <div className=\"flex items-center space-x-1 text-sm text-gray-500\">\n                                    <TbClock className=\"w-4 h-4\" />\n                                    <span>Updated {new Date(lastUpdated).toLocaleTimeString()}</span>\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"flex items-center space-x-2\">\n                            {/* Auto-refresh toggle */}\n                            {onAutoRefreshToggle && (\n                                <motion.button\n                                    whileHover={{ scale: 1.05 }}\n                                    whileTap={{ scale: 0.95 }}\n                                    onClick={onAutoRefreshToggle}\n                                    className={`px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${\n                                        autoRefresh\n                                            ? 'bg-green-500 hover:bg-green-600 text-white'\n                                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'\n                                    }`}\n                                >\n                                    {autoRefresh ? <TbPlayerPause className=\"w-4 h-4\" /> : <TbPlayerPlay className=\"w-4 h-4\" />}\n                                    <span className=\"hidden sm:inline\">\n                                        {autoRefresh ? 'Auto' : 'Manual'}\n                                    </span>\n                                </motion.button>\n                            )}\n\n\n\n                            {/* Find Me button */}\n                            {currentUserId && (\n                                <motion.button\n                                    whileHover={{ scale: 1.05 }}\n                                    whileTap={{ scale: 0.95 }}\n                                    onClick={scrollToCurrentUser}\n                                    className=\"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\"\n                                >\n                                    <TbUser className=\"w-4 h-4\" />\n                                    <span className=\"hidden sm:inline\">Find Me</span>\n                                </motion.button>\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4\">\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbUsers className=\"w-5 h-5 text-blue-500\" />\n                                <span className=\"text-sm text-gray-600\">Total Users</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{totalUsers}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">{activeUsers} active</div>\n                        </div>\n\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbTrophy className=\"w-5 h-5 text-yellow-500\" />\n                                <span className=\"text-sm text-gray-600\">Premium Users</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{premiumUsers}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">\n                                {totalUsers > 0 ? Math.round((premiumUsers / totalUsers) * 100) : 0}% premium\n                            </div>\n                        </div>\n\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbUser className=\"w-5 h-5 text-green-500\" />\n                                <span className=\"text-sm text-gray-600\">Top Score</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{topScore.toLocaleString()}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">ranking points</div>\n                        </div>\n\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbTrophy className=\"w-5 h-5 text-purple-500\" />\n                                <span className=\"text-sm text-gray-600\">Avg XP</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{averageXP.toLocaleString()}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">experience points</div>\n                        </div>\n                    </div>\n                </motion.div>\n            )}\n\n\n\n            {/* User List */}\n            <motion.div\n                variants={containerVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className={getLayoutClasses()}\n            >\n                <AnimatePresence>\n                    {usersWithClassRank.map((user, index) => {\n                        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n                        const rank = user.rank || index + 1;\n\n                        return (\n                            <motion.div\n                                key={user.userId || user._id}\n                                ref={isCurrentUser ? userRef : null}\n                                layout\n                                initial={{ opacity: 0, scale: 0.9 }}\n                                animate={{ opacity: 1, scale: 1 }}\n                                exit={{ opacity: 0, scale: 0.9 }}\n                                transition={{ duration: 0.2 }}\n                                className={`${\n                                    isCurrentUser && findMeActive\n                                        ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl'\n                                        : isCurrentUser\n                                        ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg'\n                                        : ''\n                                }`}\n                            >\n                                <UserRankingCard\n                                    user={user}\n                                    rank={rank}\n                                    classRank={user.classRank}\n                                    isCurrentUser={isCurrentUser}\n                                    layout={layout}\n                                    size={size}\n                                    showStats={showStats}\n                                />\n                            </motion.div>\n                        );\n                    })}\n                </AnimatePresence>\n            </motion.div>\n\n            {/* Empty State */}\n            {usersWithClassRank.length === 0 && (\n                <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    className=\"text-center py-12\"\n                >\n                    <TbUsers className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No users found</h3>\n                    <p className=\"text-gray-500\">\n                        No users available to display\n                    </p>\n                </motion.div>\n            )}\n\n            {/* Floating Action Button for Current User */}\n            {currentUserId && usersWithClassRank.length > 10 && (\n                <motion.button\n                    initial={{ opacity: 0, scale: 0 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    onClick={scrollToCurrentUser}\n                    className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\"\n                    title=\"Find me in ranking\"\n                >\n                    <TbUser className=\"w-6 h-6\" />\n                </motion.button>\n            )}\n        </div>\n    );\n};\n\nexport default UserRankingList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAEC,OAAO,QAAQ,gBAAgB;AAC3G,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,eAAe,GAAGA,CAAC;EACrBC,KAAK,GAAG,EAAE;EACVC,aAAa,GAAG,IAAI;EACpBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG,EAAE;EACdC,cAAc,GAAG,IAAI;EACrBC,UAAU,GAAG,KAAK;EAClBC,SAAS,GAAG,IAAI;EAChBC,WAAW,GAAG,IAAI;EAClBC,WAAW,GAAG,KAAK;EACnBC,mBAAmB,GAAG;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM8B,mBAAmB,GAAG7B,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM8B,OAAO,GAAGV,cAAc,IAAIS,mBAAmB;EACrD,MAAME,YAAY,GAAGV,UAAU,IAAIM,eAAe;;EAElD;EACA,MAAMK,WAAW,GAAGlB,KAAK,CAACmB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAACE,IAAI,IAAI,CAAC,KAAKD,CAAC,CAACC,IAAI,IAAI,CAAC,CAAC,CAAC;;EAEvE;EACA,MAAMC,kBAAkB,GAAGL,WAAW,CAACM,GAAG,CAACC,IAAI,IAAI;IAC/C;IACA,MAAMC,cAAc,GAAGR,WAAW,CAACS,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKJ,IAAI,CAACI,KAAK,CAAC;IACtE,MAAMC,SAAS,GAAGJ,cAAc,CAACK,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,GAAG,KAAKP,IAAI,CAACO,GAAG,IAAIJ,CAAC,CAACK,MAAM,KAAKR,IAAI,CAACQ,MAAM,CAAC,GAAG,CAAC;IACnG,OAAO;MAAE,GAAGR,IAAI;MAAEK;IAAU,CAAC;EACjC,CAAC,CAAC;;EAEF;EACA,MAAMI,WAAW,GAAGX,kBAAkB,CAACY,IAAI,CAACV,IAAI,IAAIA,IAAI,CAACO,GAAG,KAAK/B,aAAa,IAAIwB,IAAI,CAACQ,MAAM,KAAKhC,aAAa,CAAC;;EAEhH;EACA,MAAMmC,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIpB,OAAO,CAACqB,OAAO,EAAE;MACjBrB,OAAO,CAACqB,OAAO,CAACC,cAAc,CAAC;QAC3BC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACF1B,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACA2B,UAAU,CAAC,MAAM3B,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACrD;EACJ,CAAC;;EAED;EACA,MAAM4B,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,QAAQxC,MAAM;MACV,KAAK,UAAU;QACX,OAAO,qEAAqE;MAChF,KAAK,MAAM;QACP,OAAO,sDAAsD;MACjE,KAAK,YAAY;MACjB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;;EAED;EACA,MAAMyC,iBAAiB,GAAG;IACtBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACLD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACRC,eAAe,EAAE;MACrB;IACJ;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGjD,KAAK,CAACkD,MAAM;EAC/B,MAAMC,YAAY,GAAGnD,KAAK,CAAC2B,MAAM,CAACC,CAAC,IAC/BA,CAAC,CAACwB,kBAAkB,KAAK,QAAQ,IACjCxB,CAAC,CAACwB,kBAAkB,KAAK,SAAS,IAClCxB,CAAC,CAACyB,4BAA4B,KAAK,SACvC,CAAC,CAACH,MAAM;;EAER;EACA,MAAMI,QAAQ,GAAGtD,KAAK,CAACkD,MAAM,GAAG,CAAC,GAAGK,IAAI,CAACC,GAAG,CAAC,GAAGxD,KAAK,CAACwB,GAAG,CAACI,CAAC,IACvDA,CAAC,CAAC6B,YAAY,IAAI7B,CAAC,CAAC8B,OAAO,IAAI9B,CAAC,CAAC+B,WAAW,IAAI,CACpD,CAAC,CAAC,GAAG,CAAC;;EAEN;EACA,MAAMC,WAAW,GAAG5D,KAAK,CAAC2B,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACiC,iBAAiB,IAAI,CAAC,IAAI,CAAC,CAAC,CAACX,MAAM;EAC5E,MAAMY,SAAS,GAAG9D,KAAK,CAACkD,MAAM,GAAG,CAAC,GAC9BK,IAAI,CAACQ,KAAK,CAAC/D,KAAK,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAErC,CAAC,KAAKqC,GAAG,IAAIrC,CAAC,CAAC8B,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG1D,KAAK,CAACkD,MAAM,CAAC,GAAG,CAAC;EAEtF,oBACIpD,OAAA;IAAKO,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAA6D,QAAA,GAEpC9D,SAAS,iBACNN,OAAA,CAACX,MAAM,CAACgF,GAAG;MACPC,OAAO,EAAE;QAAEvB,OAAO,EAAE,CAAC;QAAEwB,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEzB,OAAO,EAAE,CAAC;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAC9BhE,SAAS,EAAC,kFAAkF;MAAA6D,QAAA,gBAE5FpE,OAAA;QAAKO,SAAS,EAAC,wCAAwC;QAAA6D,QAAA,gBACnDpE,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAA6D,QAAA,gBACxCpE,OAAA;YAAIO,SAAS,EAAC,8DAA8D;YAAA6D,QAAA,gBACxEpE,OAAA,CAACP,QAAQ;cAACc,SAAS,EAAC;YAAyB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD5E,OAAA;cAAAoE,QAAA,EAAM;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EAEJjE,WAAW,iBACRX,OAAA;YAAKO,SAAS,EAAC,mDAAmD;YAAA6D,QAAA,gBAC9DpE,OAAA,CAACH,OAAO;cAACU,SAAS,EAAC;YAAS;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B5E,OAAA;cAAAoE,QAAA,GAAM,UAAQ,EAAC,IAAIS,IAAI,CAAClE,WAAW,CAAC,CAACmE,kBAAkB,CAAC,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEN5E,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAA6D,QAAA,GAEvCvD,mBAAmB,iBAChBb,OAAA,CAACX,MAAM,CAAC0F,MAAM;YACVC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BE,OAAO,EAAEtE,mBAAoB;YAC7BN,SAAS,EAAG,+FACRK,WAAW,GACL,4CAA4C,GAC5C,6CACT,EAAE;YAAAwD,QAAA,GAEFxD,WAAW,gBAAGZ,OAAA,CAACJ,aAAa;cAACW,SAAS,EAAC;YAAS;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5E,OAAA,CAACL,YAAY;cAACY,SAAS,EAAC;YAAS;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3F5E,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAA6D,QAAA,EAC7BxD,WAAW,GAAG,MAAM,GAAG;YAAQ;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAClB,EAKAzE,aAAa,iBACVH,OAAA,CAACX,MAAM,CAAC0F,MAAM;YACVC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BE,OAAO,EAAE7C,mBAAoB;YAC7B/B,SAAS,EAAC,0IAA0I;YAAA6D,QAAA,gBAEpJpE,OAAA,CAACT,MAAM;cAACgB,SAAS,EAAC;YAAS;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B5E,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAA6D,QAAA,EAAC;YAAO;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAClB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN5E,OAAA;QAAKO,SAAS,EAAC,uCAAuC;QAAA6D,QAAA,gBAClDpE,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA6D,QAAA,gBAC3DpE,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA6D,QAAA,gBACxCpE,OAAA,CAACR,OAAO;cAACe,SAAS,EAAC;YAAuB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C5E,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA6D,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACN5E,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA6D,QAAA,EAAEjB;UAAU;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzE5E,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA6D,QAAA,GAAEN,WAAW,EAAC,SAAO;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eAEN5E,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA6D,QAAA,gBAC3DpE,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA6D,QAAA,gBACxCpE,OAAA,CAACP,QAAQ;cAACc,SAAS,EAAC;YAAyB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD5E,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA6D,QAAA,EAAC;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACN5E,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA6D,QAAA,EAAEf;UAAY;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3E5E,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA6D,QAAA,GACtCjB,UAAU,GAAG,CAAC,GAAGM,IAAI,CAACQ,KAAK,CAAEZ,YAAY,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,WACxE;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN5E,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA6D,QAAA,gBAC3DpE,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA6D,QAAA,gBACxCpE,OAAA,CAACT,MAAM;cAACgB,SAAS,EAAC;YAAwB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C5E,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA6D,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACN5E,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA6D,QAAA,EAAEZ,QAAQ,CAAC4B,cAAc,CAAC;UAAC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxF5E,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA6D,QAAA,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAEN5E,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA6D,QAAA,gBAC3DpE,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA6D,QAAA,gBACxCpE,OAAA,CAACP,QAAQ;cAACc,SAAS,EAAC;YAAyB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD5E,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA6D,QAAA,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACN5E,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA6D,QAAA,EAAEJ,SAAS,CAACoB,cAAc,CAAC;UAAC;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzF5E,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA6D,QAAA,EAAC;UAAiB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACf,eAKD5E,OAAA,CAACX,MAAM,CAACgF,GAAG;MACPgB,QAAQ,EAAExC,iBAAkB;MAC5ByB,OAAO,EAAC,QAAQ;MAChBE,OAAO,EAAC,SAAS;MACjBjE,SAAS,EAAEqC,gBAAgB,CAAC,CAAE;MAAAwB,QAAA,eAE9BpE,OAAA,CAACV,eAAe;QAAA8E,QAAA,EACX3C,kBAAkB,CAACC,GAAG,CAAC,CAACC,IAAI,EAAE2D,KAAK,KAAK;UACrC,MAAMC,aAAa,GAAG5D,IAAI,CAACQ,MAAM,KAAKhC,aAAa,IAAIwB,IAAI,CAACO,GAAG,KAAK/B,aAAa;UACjF,MAAMqB,IAAI,GAAGG,IAAI,CAACH,IAAI,IAAI8D,KAAK,GAAG,CAAC;UAEnC,oBACItF,OAAA,CAACX,MAAM,CAACgF,GAAG;YAEPmB,GAAG,EAAED,aAAa,GAAGrE,OAAO,GAAG,IAAK;YACpCd,MAAM;YACNkE,OAAO,EAAE;cAAEvB,OAAO,EAAE,CAAC;cAAEkC,KAAK,EAAE;YAAI,CAAE;YACpCT,OAAO,EAAE;cAAEzB,OAAO,EAAE,CAAC;cAAEkC,KAAK,EAAE;YAAE,CAAE;YAClCQ,IAAI,EAAE;cAAE1C,OAAO,EAAE,CAAC;cAAEkC,KAAK,EAAE;YAAI,CAAE;YACjChC,UAAU,EAAE;cAAEyC,QAAQ,EAAE;YAAI,CAAE;YAC9BnF,SAAS,EAAG,GACRgF,aAAa,IAAIpE,YAAY,GACvB,6GAA6G,GAC7GoE,aAAa,GACb,+CAA+C,GAC/C,EACT,EAAE;YAAAnB,QAAA,eAEHpE,OAAA,CAACF,eAAe;cACZ6B,IAAI,EAAEA,IAAK;cACXH,IAAI,EAAEA,IAAK;cACXQ,SAAS,EAAEL,IAAI,CAACK,SAAU;cAC1BuD,aAAa,EAAEA,aAAc;cAC7BnF,MAAM,EAAEA,MAAO;cACfC,IAAI,EAAEA,IAAK;cACXC,SAAS,EAAEA;YAAU;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC,GAvBGjD,IAAI,CAACQ,MAAM,IAAIR,IAAI,CAACO,GAAG;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBpB,CAAC;QAErB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGZnD,kBAAkB,CAAC2B,MAAM,KAAK,CAAC,iBAC5BpD,OAAA,CAACX,MAAM,CAACgF,GAAG;MACPC,OAAO,EAAE;QAAEvB,OAAO,EAAE;MAAE,CAAE;MACxByB,OAAO,EAAE;QAAEzB,OAAO,EAAE;MAAE,CAAE;MACxBxC,SAAS,EAAC,mBAAmB;MAAA6D,QAAA,gBAE7BpE,OAAA,CAACR,OAAO;QAACe,SAAS,EAAC;MAAsC;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5D5E,OAAA;QAAIO,SAAS,EAAC,wCAAwC;QAAA6D,QAAA,EAAC;MAAc;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1E5E,OAAA;QAAGO,SAAS,EAAC,eAAe;QAAA6D,QAAA,EAAC;MAE7B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACf,EAGAzE,aAAa,IAAIsB,kBAAkB,CAAC2B,MAAM,GAAG,EAAE,iBAC5CpD,OAAA,CAACX,MAAM,CAAC0F,MAAM;MACVT,OAAO,EAAE;QAAEvB,OAAO,EAAE,CAAC;QAAEkC,KAAK,EAAE;MAAE,CAAE;MAClCT,OAAO,EAAE;QAAEzB,OAAO,EAAE,CAAC;QAAEkC,KAAK,EAAE;MAAE,CAAE;MAClCD,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MACzBE,OAAO,EAAE7C,mBAAoB;MAC7B/B,SAAS,EAAC,6IAA6I;MACvJoF,KAAK,EAAC,oBAAoB;MAAAvB,QAAA,eAE1BpE,OAAA,CAACT,MAAM;QAACgB,SAAS,EAAC;MAAS;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAClB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC9D,EAAA,CA9QIb,eAAe;AAAA2F,EAAA,GAAf3F,eAAe;AAgRrB,eAAeA,eAAe;AAAC,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}