{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef, useCallback, useMemo } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { TbTrophy, TbMedal, TbRefresh, TbAlertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from \"react-icons/tb\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\nimport { message } from \"antd\";\nimport axiosInstance from \"../../../apicalls\";\nimport { isSessionValid, validateTokenFormat, cleanupInvalidSession } from \"../../../utils/authUtils\";\nimport { autoRefreshToken } from \"../../../apicalls/auth\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  var _rankingData$, _rankingData$2;\n  console.log('🏆 Ranking component rendered');\n\n  // Memoized selector to prevent unnecessary re-renders\n  const user = useSelector(state => {\n    var _state$users;\n    return (_state$users = state.users) === null || _state$users === void 0 ? void 0 : _state$users.user;\n  }, (left, right) => {\n    return (left === null || left === void 0 ? void 0 : left._id) === (right === null || right === void 0 ? void 0 : right._id) && (left === null || left === void 0 ? void 0 : left.name) === (right === null || right === void 0 ? void 0 : right.name);\n  });\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [refreshing, setRefreshing] = useState(false);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [autoRefresh, setAutoRefresh] = useState(false);\n  const currentUserRef = useRef(null);\n  const refreshIntervalRef = useRef(null);\n  console.log('🏆 Ranking state - rankingData length:', rankingData.length, 'loading:', loading, 'error:', error);\n  const fetchRankingData = useCallback(async (showRefreshMessage = false) => {\n    try {\n      // Check if user is available\n      if (!user || !user._id) {\n        console.log('🚫 User not available, skipping ranking fetch');\n        return;\n      }\n      console.log('🔄 Starting ranking data fetch for user:', user._id);\n      setLoading(true);\n\n      // Check if session is valid and refresh token if needed\n      if (!isSessionValid()) {\n        console.log('🔒 Session invalid, redirecting to login');\n        message.error('Your session has expired. Please login again.');\n        window.location.href = '/login';\n        return;\n      }\n\n      // Validate and refresh token if needed\n      const currentToken = localStorage.getItem('token');\n      if (!currentToken) {\n        console.log('🔒 No token found, redirecting to login');\n        message.error('Please login to view rankings');\n        window.location.href = '/login';\n        return;\n      }\n\n      // Validate token format\n      const tokenValidation = validateTokenFormat(currentToken);\n      if (!tokenValidation.isValid) {\n        console.log('🔒 Token validation failed:', tokenValidation.error);\n        cleanupInvalidSession();\n        message.error('Invalid session. Please login again.');\n        window.location.href = '/login';\n        return;\n      }\n      console.log('✅ Token validation passed, expires in', Math.floor(tokenValidation.timeLeft / 3600), 'hours');\n\n      // Try to auto-refresh token if it's expiring soon\n      try {\n        await autoRefreshToken();\n        console.log('🔄 Token auto-refresh attempted');\n      } catch (refreshError) {\n        console.log('⚠️ Token refresh failed:', refreshError);\n        // Continue anyway, the request might still work\n      }\n\n      if (showRefreshMessage) {\n        setRefreshing(true);\n        message.loading(\"Refreshing rankings...\", 1);\n      }\n\n      // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking\n      let response;\n\n      // Add cache-busting timestamp to ensure fresh data\n      const timestamp = new Date().getTime();\n\n      // Ensure we have the latest token for all requests\n      const latestToken = localStorage.getItem('token');\n      try {\n        // Try new XP-based leaderboard first\n        console.log('🎯 Attempting XP leaderboard API call...');\n        const xpResponse = await axiosInstance.get(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {\n          headers: {\n            'Cache-Control': 'no-cache',\n            'Authorization': `Bearer ${latestToken}`\n          }\n        });\n        console.log('📊 XP leaderboard response:', xpResponse.data);\n        if (xpResponse.data.success) {\n          var _response$data;\n          response = xpResponse.data;\n          console.log('✅ Using XP-based leaderboard with', ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.length) || 0, 'users');\n        } else {\n          throw new Error('XP leaderboard failed, trying enhanced leaderboard');\n        }\n      } catch (xpError) {\n        console.log('❌ XP leaderboard failed, trying enhanced leaderboard:', xpError);\n        try {\n          const enhancedResponse = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {\n            headers: {\n              'Cache-Control': 'no-cache',\n              'Authorization': `Bearer ${latestToken}`\n            }\n          });\n          console.log('📊 Enhanced leaderboard response:', enhancedResponse.data);\n          if (enhancedResponse.data.success) {\n            var _response$data2;\n            response = enhancedResponse.data;\n            console.log('✅ Using enhanced leaderboard with', ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.length) || 0, 'users');\n          } else {\n            throw new Error('Enhanced leaderboard failed');\n          }\n        } catch (enhancedError) {\n          console.log('❌ Falling back to regular ranking:', enhancedError);\n          response = await getAllReportsForRanking();\n          console.log('📊 Regular ranking response:', response);\n        }\n      }\n      if (response.success) {\n        var _response$data3;\n        console.log('🔄 Transforming ranking data...', ((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.length) || 0, 'users');\n\n        // Transform data to match UserRankingCard expectations with enhanced XP system support\n        const transformedData = response.data.map((userData, index) => ({\n          // User identification - handle both old and new API formats\n          userId: userData.userId || userData._id,\n          _id: userData.userId || userData._id,\n          name: userData.userName || userData.name || 'Unknown User',\n          profilePicture: userData.userPhoto || userData.profileImage,\n          profileImage: userData.profileImage || userData.userPhoto,\n          school: userData.userSchool || userData.school || 'Unknown School',\n          class: userData.userClass || userData.class || 'Unknown',\n          level: userData.userLevel || userData.level || 'Primary',\n          email: userData.email,\n          // Legacy points system\n          totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,\n          totalPointsEarned: userData.totalPointsEarned || userData.totalPoints || 0,\n          quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\n          totalQuizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\n          passedExamsCount: userData.passedExamsCount || 0,\n          retryCount: userData.retryCount || 0,\n          scoreRatio: userData.scoreRatio || 0,\n          // XP System data (new)\n          totalXP: userData.totalXP || 0,\n          currentLevel: userData.currentLevel || 1,\n          xpToNextLevel: userData.xpToNextLevel || 0,\n          seasonXP: userData.seasonXP || 0,\n          lifetimeXP: userData.lifetimeXP || 0,\n          // Statistics\n          averageScore: userData.averageScore || 0,\n          bestStreak: userData.bestStreak || 0,\n          currentStreak: userData.currentStreak || 0,\n          achievements: userData.achievements || [],\n          achievementCount: userData.achievementCount || (userData.achievements ? userData.achievements.length : 0),\n          // Ranking data (prioritize ranking score from enhanced system)\n          rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,\n          rank: userData.rank || index + 1,\n          score: userData.rankingScore || userData.score || userData.totalXP || userData.totalPoints || 0,\n          // Subscription status (handle normalized status)\n          subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\n          normalizedSubscriptionStatus: userData.normalizedSubscriptionStatus || userData.subscriptionStatus || 'free',\n          subscriptionPlan: userData.subscriptionPlan,\n          subscriptionEndDate: userData.subscriptionEndDate,\n          // XP breakdown (if available from new system)\n          breakdown: userData.breakdown || null,\n          // Additional metadata\n          createdAt: userData.createdAt,\n          updatedAt: userData.updatedAt\n        }));\n        console.log('✅ Setting ranking data with', transformedData.length, 'users');\n        setRankingData(transformedData);\n        setError(null);\n        setLastUpdated(new Date());\n\n        // Find current user's rank\n        const userRank = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id) || item.userId === (user === null || user === void 0 ? void 0 : user._id));\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n        if (userRank >= 0) {\n          console.log('🎯 Current user rank:', userRank + 1);\n        } else {\n          console.log('⚠️ Current user not found in ranking data');\n        }\n        if (showRefreshMessage) {\n          message.success(\"Rankings updated successfully!\");\n        }\n      } else {\n        console.log('❌ Response not successful:', response);\n        setError(response.message || \"Failed to fetch ranking data\");\n        message.error(\"Failed to load rankings\");\n      }\n    } catch (err) {\n      console.error('Ranking fetch error:', err);\n\n      // Handle different types of errors\n      let errorMessage = \"An error occurred while fetching rankings\";\n      if (err.response) {\n        var _err$response$data;\n        // Server responded with error status\n        const status = err.response.status;\n        const responseMessage = ((_err$response$data = err.response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || '';\n        if (status === 401) {\n          // Authentication error\n          console.log('🔒 Authentication error, clearing session');\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          errorMessage = \"Your session has expired. Please login again.\";\n          setTimeout(() => {\n            window.location.href = '/login';\n          }, 2000);\n        } else {\n          errorMessage = responseMessage || `Server error: ${status}`;\n        }\n      } else if (err.request) {\n        // Request was made but no response received\n        errorMessage = \"Network error: Unable to connect to server\";\n      } else if (err.message) {\n        // Something else happened\n        errorMessage = err.message;\n      }\n      setError(errorMessage);\n\n      // Only show error popup for non-auth errors\n      if (status !== 401) {\n        message.error(errorMessage);\n      }\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [user === null || user === void 0 ? void 0 : user._id]); // Use stable user ID as dependency\n\n  useEffect(() => {\n    console.log('🔄 Ranking component mounted, user:', user);\n    console.log('🔄 User available:', !!user, 'User ID:', user === null || user === void 0 ? void 0 : user._id);\n    if (user && user._id) {\n      console.log('✅ User available, fetching ranking data');\n      fetchRankingData();\n    } else {\n      console.log('⚠️ User not available yet, waiting...');\n    }\n\n    // Cleanup function to reset states when component unmounts\n    return () => {\n      setLoading(false);\n      setRefreshing(false);\n      setError(null);\n    };\n  }, [fetchRankingData]);\n\n  // Separate effect to handle user changes\n  useEffect(() => {\n    if (user && user._id) {\n      console.log('👤 User changed/available, fetching ranking data for:', user._id);\n      fetchRankingData();\n    }\n  }, [user === null || user === void 0 ? void 0 : user._id, fetchRankingData]);\n\n  // Force API call after component mounts (temporary fix)\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      console.log('🚀 Force calling direct API after 3 seconds...');\n      // Only call if we don't have data yet\n      if (rankingData.length === 0 && !loading) {\n        handleDirectAPICall();\n      }\n    }, 3000);\n    return () => clearTimeout(timer);\n  }, [rankingData.length, loading]);\n\n  // Auto-refresh functionality\n  useEffect(() => {\n    if (autoRefresh) {\n      refreshIntervalRef.current = setInterval(() => {\n        fetchRankingData(false); // Silent refresh\n      }, 30000); // Refresh every 30 seconds\n    } else {\n      if (refreshIntervalRef.current) {\n        clearInterval(refreshIntervalRef.current);\n        refreshIntervalRef.current = null;\n      }\n    }\n\n    // Cleanup on unmount\n    return () => {\n      if (refreshIntervalRef.current) {\n        clearInterval(refreshIntervalRef.current);\n      }\n    };\n  }, [autoRefresh, fetchRankingData]);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRef.current) {\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setShowFindMe(true);\n      setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds\n    }\n  };\n\n  const handleRefresh = () => {\n    console.log('🔄 Manual refresh triggered');\n\n    // Clear any existing errors\n    setError(null);\n\n    // Use direct API call instead of complex fetchRankingData\n    handleDirectAPICall();\n  };\n  const handleForceRefresh = () => {\n    console.log('🔄 Force refresh triggered - clearing cache');\n    // Clear any cached data\n    setRankingData([]);\n    setError(null);\n    setLoading(true);\n\n    // Force a fresh API call\n    fetchRankingData(true);\n  };\n\n  // Simple direct API call for testing\n  const handleDirectAPICall = async () => {\n    console.log('🚀 Direct API call for testing...');\n    setLoading(true);\n    setError(null);\n    try {\n      const token = localStorage.getItem('token');\n      console.log('🚀 Using token:', token ? 'Token exists' : 'No token');\n      const response = await axiosInstance.get('/api/quiz/xp-leaderboard?limit=10', {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      console.log('🚀 Direct API response:', response.data);\n      if (response.data.success && response.data.data) {\n        console.log('🚀 Setting data directly:', response.data.data.length, 'users');\n\n        // Set the data directly without any transformation\n        setRankingData(response.data.data);\n        setError(null);\n        setLoading(false);\n        message.success(`Loaded ${response.data.data.length} users successfully!`);\n      } else {\n        throw new Error('API returned no data');\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3;\n      console.error('🚀 Direct API call failed:', error);\n\n      // Handle different types of errors gracefully\n      let errorMessage = 'Failed to load ranking data';\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n        errorMessage = 'Session expired. Please refresh the page.';\n      } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 500) {\n        errorMessage = 'Server error. Please try again.';\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n      setError(errorMessage);\n      setLoading(false);\n\n      // Only show error message if it's not a session issue\n      if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) !== 401) {\n        message.error(errorMessage);\n      }\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-xl p-8 shadow-lg animate-fadeInUp\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading rankings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full animate-fadeInUp\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-2\",\n          children: \"Error Loading Rankings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRefresh,\n          className: \"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 mx-auto hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-200 to-pink-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-1/2 w-96 h-96 bg-gradient-to-br from-yellow-200 to-orange-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-4000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-grid-pattern opacity-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 border-b border-white/20 animate-slideInLeft\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.history.back(),\n              className: \"bg-white/80 backdrop-blur-lg hover:bg-white/90 text-gray-700 px-4 py-2 rounded-xl font-semibold transition-all duration-200 flex items-center space-x-2 border border-gray-200 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95\",\n              children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-black text-gray-900 gradient-text-blue\",\n                children: \"\\uD83C\\uDFC6 Student Rankings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 471,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: \"Compete with students across all levels\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [currentUserRank && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/80 backdrop-blur-lg rounded-xl px-4 py-2 border border-gray-200 shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 font-medium\",\n                  children: \"Your Rank\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg font-black text-blue-600\",\n                  children: [\"#\", currentUserRank.rank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 33\n            }, this), currentUserRank && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleFindMe,\n              className: \"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-3 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg hover:scale-105 active:scale-95\",\n              children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Find Me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleRefresh,\n              disabled: refreshing,\n              className: \"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 disabled:cursor-not-allowed\",\n              children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n                className: `w-5 h-5 ${refreshing ? 'animate-spin' : ''}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: refreshing ? 'Refreshing...' : 'Refresh'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 459,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8 px-4 animate-fadeInUp\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative animate-wiggle\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"text-6xl text-yellow-400 drop-shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2\",\n              children: \"\\uD83C\\uDFC6 LEADERBOARD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2 text-white/80\",\n              children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-medium\",\n                children: \"Battle for Glory\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto animate-scaleIn\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                className: \"text-orange-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 543,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbAward, {\n                className: \"text-purple-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: [rankingData.length, \" Competitors\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbMedal, {\n                className: \"text-yellow-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: \"Live Rankings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\",\n      children: rankingData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-16 bg-white/80 backdrop-blur-lg rounded-2xl border border-white/50 shadow-2xl animate-fadeInUp\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-wiggle\",\n          children: /*#__PURE__*/_jsxDEV(TbMedal, {\n            className: \"w-16 h-16 text-yellow-500 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: \"No Rankings Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6 text-lg\",\n          children: \"Complete some quizzes to join the leaderboard!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRefresh,\n          disabled: refreshing,\n          className: `bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg hover:scale-105 active:scale-95 ${refreshing ? 'opacity-50 cursor-not-allowed' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n            className: `w-5 h-5 inline mr-2 ${refreshing ? 'animate-spin' : ''}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 29\n          }, this), refreshing ? 'Refreshing...' : 'Refresh Rankings']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 567,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/80 backdrop-blur-xl rounded-2xl border border-white/50 p-8 shadow-2xl animate-fadeInUp\",\n        children: [rankingData.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 text-center\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleDirectAPICall,\n            disabled: loading,\n            className: \"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 disabled:from-gray-400 disabled:to-gray-500 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-200 shadow-lg hover:scale-105 active:scale-95 disabled:cursor-not-allowed\",\n            children: loading ? '🔄 Loading...' : '🚀 LOAD USERS NOW'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4 p-4 bg-blue-100 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"font-bold text-blue-800\",\n            children: \"Debug Info:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-700\",\n            children: [\"Ranking Data Length: \", rankingData.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-700\",\n            children: [\"Loading: \", loading.toString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-700\",\n            children: [\"Error: \", error || 'None']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-blue-700\",\n            children: [\"User ID: \", (user === null || user === void 0 ? void 0 : user._id) || 'None']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 29\n          }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-700\",\n              children: [\"Sample User: \", ((_rankingData$ = rankingData[0]) === null || _rankingData$ === void 0 ? void 0 : _rankingData$.name) || 'No name']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-blue-700\",\n              children: [\"Sample XP: \", ((_rankingData$2 = rankingData[0]) === null || _rankingData$2 === void 0 ? void 0 : _rankingData$2.totalXP) || 'No XP']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 598,\n          columnNumber: 25\n        }, this), rankingData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold mb-4\",\n            children: \"Simple User List (Testing):\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 33\n          }, this), rankingData.slice(0, 5).map((user, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-2 border-b border-gray-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [\"#\", index + 1, \" \", user.name || 'Unknown']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-4 text-blue-600\",\n              children: [user.totalXP || 0, \" XP\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-4 text-gray-500\",\n              children: [\"Class \", user.class || 'Unknown']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 41\n            }, this)]\n          }, user._id || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 37\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 29\n        }, this), currentUserRank && currentUserRank.rank <= 10 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-xl p-4 text-white animate-bounce\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold text-lg\",\n              children: \"\\uD83C\\uDF89 Congratulations! You're in the Top 10! \\uD83C\\uDF89\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(UserRankingList, {\n          users: rankingData,\n          currentUserId: (user === null || user === void 0 ? void 0 : user._id) || null,\n          layout: \"horizontal\",\n          size: \"medium\",\n          showStats: true,\n          className: \"space-y-4\",\n          currentUserRef: currentUserRef,\n          showFindMe: showFindMe,\n          lastUpdated: lastUpdated,\n          autoRefresh: autoRefresh,\n          onAutoRefreshToggle: () => setAutoRefresh(!autoRefresh)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 565,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 444,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"wI7auWS00otmXZKPOHszCwhZKTo=\", false, function () {\n  return [useSelector];\n});\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useCallback", "useMemo", "useSelector", "TbTrophy", "TbMedal", "TbRefresh", "TbAlertCircle", "TbArrowLeft", "TbTarget", "TbStar", "TbFlame", "TbAward", "getAllReportsForRanking", "UserRankingList", "message", "axiosInstance", "isSessionValid", "validateTokenFormat", "cleanupInvalidSession", "autoRefreshToken", "jsxDEV", "_jsxDEV", "Ranking", "_s", "_rankingData$", "_rankingData$2", "console", "log", "user", "state", "_state$users", "users", "left", "right", "_id", "name", "rankingData", "setRankingData", "loading", "setLoading", "error", "setError", "refreshing", "setRefreshing", "currentUserRank", "setCurrentUserRank", "showFindMe", "setShowFindMe", "lastUpdated", "setLastUpdated", "autoRefresh", "setAutoRefresh", "currentUserRef", "refreshIntervalRef", "length", "fetchRankingData", "showRefreshMessage", "window", "location", "href", "currentToken", "localStorage", "getItem", "tokenValidation", "<PERSON><PERSON><PERSON><PERSON>", "Math", "floor", "timeLeft", "refreshError", "response", "timestamp", "Date", "getTime", "latestToken", "xpResponse", "get", "headers", "data", "success", "_response$data", "Error", "xpError", "enhancedResponse", "_response$data2", "enhancedError", "_response$data3", "transformedData", "map", "userData", "index", "userId", "userName", "profilePicture", "userPhoto", "profileImage", "school", "userSchool", "class", "userClass", "level", "userLevel", "email", "totalPoints", "totalPointsEarned", "quizzesTaken", "totalQuizzesTaken", "passedExamsCount", "retryCount", "scoreRatio", "totalXP", "currentLevel", "xpToNextLevel", "seasonXP", "lifetimeXP", "averageScore", "bestStreak", "currentStreak", "achievements", "achievementCount", "rankingScore", "enhancedRankingScore", "rank", "score", "subscriptionStatus", "normalizedSubscriptionStatus", "subscriptionPlan", "subscriptionEndDate", "breakdown", "createdAt", "updatedAt", "userRank", "findIndex", "item", "err", "errorMessage", "_err$response$data", "status", "responseMessage", "removeItem", "setTimeout", "request", "timer", "handleDirectAPICall", "clearTimeout", "current", "setInterval", "clearInterval", "handleFindMe", "scrollIntoView", "behavior", "block", "handleRefresh", "handleForceRefresh", "token", "_error$response", "_error$response2", "_error$response3", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "history", "back", "disabled", "toString", "slice", "currentUserId", "layout", "size", "showStats", "onAutoRefreshToggle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState, useRef, useCallback, useMemo } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { Tb<PERSON>rophy, TbMedal, TbRefresh, Tb<PERSON>lertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from \"react-icons/tb\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\r\nimport { message } from \"antd\";\r\nimport axiosInstance from \"../../../apicalls\";\r\nimport { isSessionValid, validateTokenFormat, cleanupInvalidSession } from \"../../../utils/authUtils\";\r\nimport { autoRefreshToken } from \"../../../apicalls/auth\";\r\n\r\nconst Ranking = () => {\r\n    console.log('🏆 Ranking component rendered');\r\n\r\n    // Memoized selector to prevent unnecessary re-renders\r\n    const user = useSelector((state) => state.users?.user, (left, right) => {\r\n        return left?._id === right?._id && left?.name === right?.name;\r\n    });\r\n    const [rankingData, setRankingData] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [refreshing, setRefreshing] = useState(false);\r\n    const [currentUserRank, setCurrentUserRank] = useState(null);\r\n    const [showFindMe, setShowFindMe] = useState(false);\r\n    const [lastUpdated, setLastUpdated] = useState(null);\r\n    const [autoRefresh, setAutoRefresh] = useState(false);\r\n    const currentUserRef = useRef(null);\r\n    const refreshIntervalRef = useRef(null);\r\n\r\n    console.log('🏆 Ranking state - rankingData length:', rankingData.length, 'loading:', loading, 'error:', error);\r\n\r\n    const fetchRankingData = useCallback(async (showRefreshMessage = false) => {\r\n        try {\r\n            // Check if user is available\r\n            if (!user || !user._id) {\r\n                console.log('🚫 User not available, skipping ranking fetch');\r\n                return;\r\n            }\r\n\r\n            console.log('🔄 Starting ranking data fetch for user:', user._id);\r\n            setLoading(true);\r\n\r\n            // Check if session is valid and refresh token if needed\r\n            if (!isSessionValid()) {\r\n                console.log('🔒 Session invalid, redirecting to login');\r\n                message.error('Your session has expired. Please login again.');\r\n                window.location.href = '/login';\r\n                return;\r\n            }\r\n\r\n            // Validate and refresh token if needed\r\n            const currentToken = localStorage.getItem('token');\r\n            if (!currentToken) {\r\n                console.log('🔒 No token found, redirecting to login');\r\n                message.error('Please login to view rankings');\r\n                window.location.href = '/login';\r\n                return;\r\n            }\r\n\r\n            // Validate token format\r\n            const tokenValidation = validateTokenFormat(currentToken);\r\n            if (!tokenValidation.isValid) {\r\n                console.log('🔒 Token validation failed:', tokenValidation.error);\r\n                cleanupInvalidSession();\r\n                message.error('Invalid session. Please login again.');\r\n                window.location.href = '/login';\r\n                return;\r\n            }\r\n\r\n            console.log('✅ Token validation passed, expires in', Math.floor(tokenValidation.timeLeft / 3600), 'hours');\r\n\r\n            // Try to auto-refresh token if it's expiring soon\r\n            try {\r\n                await autoRefreshToken();\r\n                console.log('🔄 Token auto-refresh attempted');\r\n            } catch (refreshError) {\r\n                console.log('⚠️ Token refresh failed:', refreshError);\r\n                // Continue anyway, the request might still work\r\n            }\r\n\r\n            if (showRefreshMessage) {\r\n                setRefreshing(true);\r\n                message.loading(\"Refreshing rankings...\", 1);\r\n            }\r\n\r\n            // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking\r\n            let response;\r\n\r\n            // Add cache-busting timestamp to ensure fresh data\r\n            const timestamp = new Date().getTime();\r\n\r\n            // Ensure we have the latest token for all requests\r\n            const latestToken = localStorage.getItem('token');\r\n\r\n            try {\r\n                // Try new XP-based leaderboard first\r\n                console.log('🎯 Attempting XP leaderboard API call...');\r\n\r\n                const xpResponse = await axiosInstance.get(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {\r\n                    headers: {\r\n                        'Cache-Control': 'no-cache',\r\n                        'Authorization': `Bearer ${latestToken}`\r\n                    }\r\n                });\r\n\r\n                console.log('📊 XP leaderboard response:', xpResponse.data);\r\n\r\n                if (xpResponse.data.success) {\r\n                    response = xpResponse.data;\r\n                    console.log('✅ Using XP-based leaderboard with', response.data?.length || 0, 'users');\r\n                } else {\r\n                    throw new Error('XP leaderboard failed, trying enhanced leaderboard');\r\n                }\r\n            } catch (xpError) {\r\n                console.log('❌ XP leaderboard failed, trying enhanced leaderboard:', xpError);\r\n                try {\r\n                    const enhancedResponse = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {\r\n                        headers: {\r\n                            'Cache-Control': 'no-cache',\r\n                            'Authorization': `Bearer ${latestToken}`\r\n                        }\r\n                    });\r\n\r\n                    console.log('📊 Enhanced leaderboard response:', enhancedResponse.data);\r\n\r\n                    if (enhancedResponse.data.success) {\r\n                        response = enhancedResponse.data;\r\n                        console.log('✅ Using enhanced leaderboard with', response.data?.length || 0, 'users');\r\n                    } else {\r\n                        throw new Error('Enhanced leaderboard failed');\r\n                    }\r\n                } catch (enhancedError) {\r\n                    console.log('❌ Falling back to regular ranking:', enhancedError);\r\n                    response = await getAllReportsForRanking();\r\n                    console.log('📊 Regular ranking response:', response);\r\n                }\r\n            }\r\n\r\n            if (response.success) {\r\n                console.log('🔄 Transforming ranking data...', response.data?.length || 0, 'users');\r\n\r\n                // Transform data to match UserRankingCard expectations with enhanced XP system support\r\n                const transformedData = response.data.map((userData, index) => ({\r\n                    // User identification - handle both old and new API formats\r\n                    userId: userData.userId || userData._id,\r\n                    _id: userData.userId || userData._id,\r\n                    name: userData.userName || userData.name || 'Unknown User',\r\n                    profilePicture: userData.userPhoto || userData.profileImage,\r\n                    profileImage: userData.profileImage || userData.userPhoto,\r\n                    school: userData.userSchool || userData.school || 'Unknown School',\r\n                    class: userData.userClass || userData.class || 'Unknown',\r\n                    level: userData.userLevel || userData.level || 'Primary',\r\n                    email: userData.email,\r\n\r\n                    // Legacy points system\r\n                    totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,\r\n                    totalPointsEarned: userData.totalPointsEarned || userData.totalPoints || 0,\r\n                    quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\r\n                    totalQuizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\r\n                    passedExamsCount: userData.passedExamsCount || 0,\r\n                    retryCount: userData.retryCount || 0,\r\n                    scoreRatio: userData.scoreRatio || 0,\r\n\r\n                    // XP System data (new)\r\n                    totalXP: userData.totalXP || 0,\r\n                    currentLevel: userData.currentLevel || 1,\r\n                    xpToNextLevel: userData.xpToNextLevel || 0,\r\n                    seasonXP: userData.seasonXP || 0,\r\n                    lifetimeXP: userData.lifetimeXP || 0,\r\n\r\n                    // Statistics\r\n                    averageScore: userData.averageScore || 0,\r\n                    bestStreak: userData.bestStreak || 0,\r\n                    currentStreak: userData.currentStreak || 0,\r\n                    achievements: userData.achievements || [],\r\n                    achievementCount: userData.achievementCount || (userData.achievements ? userData.achievements.length : 0),\r\n\r\n                    // Ranking data (prioritize ranking score from enhanced system)\r\n                    rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,\r\n                    rank: userData.rank || index + 1,\r\n                    score: userData.rankingScore || userData.score || userData.totalXP || userData.totalPoints || 0,\r\n\r\n                    // Subscription status (handle normalized status)\r\n                    subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\r\n                    normalizedSubscriptionStatus: userData.normalizedSubscriptionStatus || userData.subscriptionStatus || 'free',\r\n                    subscriptionPlan: userData.subscriptionPlan,\r\n                    subscriptionEndDate: userData.subscriptionEndDate,\r\n\r\n                    // XP breakdown (if available from new system)\r\n                    breakdown: userData.breakdown || null,\r\n\r\n                    // Additional metadata\r\n                    createdAt: userData.createdAt,\r\n                    updatedAt: userData.updatedAt\r\n                }));\r\n\r\n                console.log('✅ Setting ranking data with', transformedData.length, 'users');\r\n\r\n                setRankingData(transformedData);\r\n                setError(null);\r\n                setLastUpdated(new Date());\r\n\r\n                // Find current user's rank\r\n                const userRank = transformedData.findIndex(item =>\r\n                    item._id === user?._id || item.userId === user?._id\r\n                );\r\n                setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\r\n\r\n                if (userRank >= 0) {\r\n                    console.log('🎯 Current user rank:', userRank + 1);\r\n                } else {\r\n                    console.log('⚠️ Current user not found in ranking data');\r\n                }\r\n\r\n                if (showRefreshMessage) {\r\n                    message.success(\"Rankings updated successfully!\");\r\n                }\r\n            } else {\r\n                console.log('❌ Response not successful:', response);\r\n                setError(response.message || \"Failed to fetch ranking data\");\r\n                message.error(\"Failed to load rankings\");\r\n            }\r\n        } catch (err) {\r\n            console.error('Ranking fetch error:', err);\r\n\r\n            // Handle different types of errors\r\n            let errorMessage = \"An error occurred while fetching rankings\";\r\n\r\n            if (err.response) {\r\n                // Server responded with error status\r\n                const status = err.response.status;\r\n                const responseMessage = err.response.data?.message || '';\r\n\r\n                if (status === 401) {\r\n                    // Authentication error\r\n                    console.log('🔒 Authentication error, clearing session');\r\n                    localStorage.removeItem('token');\r\n                    localStorage.removeItem('user');\r\n                    errorMessage = \"Your session has expired. Please login again.\";\r\n                    setTimeout(() => {\r\n                        window.location.href = '/login';\r\n                    }, 2000);\r\n                } else {\r\n                    errorMessage = responseMessage || `Server error: ${status}`;\r\n                }\r\n            } else if (err.request) {\r\n                // Request was made but no response received\r\n                errorMessage = \"Network error: Unable to connect to server\";\r\n            } else if (err.message) {\r\n                // Something else happened\r\n                errorMessage = err.message;\r\n            }\r\n\r\n            setError(errorMessage);\r\n\r\n            // Only show error popup for non-auth errors\r\n            if (status !== 401) {\r\n                message.error(errorMessage);\r\n            }\r\n        } finally {\r\n            setLoading(false);\r\n            setRefreshing(false);\r\n        }\r\n    }, [user?._id]); // Use stable user ID as dependency\r\n\r\n    useEffect(() => {\r\n        console.log('🔄 Ranking component mounted, user:', user);\r\n        console.log('🔄 User available:', !!user, 'User ID:', user?._id);\r\n\r\n        if (user && user._id) {\r\n            console.log('✅ User available, fetching ranking data');\r\n            fetchRankingData();\r\n        } else {\r\n            console.log('⚠️ User not available yet, waiting...');\r\n        }\r\n\r\n        // Cleanup function to reset states when component unmounts\r\n        return () => {\r\n            setLoading(false);\r\n            setRefreshing(false);\r\n            setError(null);\r\n        };\r\n    }, [fetchRankingData]);\r\n\r\n    // Separate effect to handle user changes\r\n    useEffect(() => {\r\n        if (user && user._id) {\r\n            console.log('👤 User changed/available, fetching ranking data for:', user._id);\r\n            fetchRankingData();\r\n        }\r\n    }, [user?._id, fetchRankingData]);\r\n\r\n    // Force API call after component mounts (temporary fix)\r\n    useEffect(() => {\r\n        const timer = setTimeout(() => {\r\n            console.log('🚀 Force calling direct API after 3 seconds...');\r\n            // Only call if we don't have data yet\r\n            if (rankingData.length === 0 && !loading) {\r\n                handleDirectAPICall();\r\n            }\r\n        }, 3000);\r\n\r\n        return () => clearTimeout(timer);\r\n    }, [rankingData.length, loading]);\r\n\r\n    // Auto-refresh functionality\r\n    useEffect(() => {\r\n        if (autoRefresh) {\r\n            refreshIntervalRef.current = setInterval(() => {\r\n                fetchRankingData(false); // Silent refresh\r\n            }, 30000); // Refresh every 30 seconds\r\n        } else {\r\n            if (refreshIntervalRef.current) {\r\n                clearInterval(refreshIntervalRef.current);\r\n                refreshIntervalRef.current = null;\r\n            }\r\n        }\r\n\r\n        // Cleanup on unmount\r\n        return () => {\r\n            if (refreshIntervalRef.current) {\r\n                clearInterval(refreshIntervalRef.current);\r\n            }\r\n        };\r\n    }, [autoRefresh, fetchRankingData]);\r\n\r\n    // Find Me functionality\r\n    const handleFindMe = () => {\r\n        if (currentUserRef.current) {\r\n            currentUserRef.current.scrollIntoView({\r\n                behavior: 'smooth',\r\n                block: 'center'\r\n            });\r\n            setShowFindMe(true);\r\n            setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds\r\n        }\r\n    };\r\n\r\n    const handleRefresh = () => {\r\n        console.log('🔄 Manual refresh triggered');\r\n\r\n        // Clear any existing errors\r\n        setError(null);\r\n\r\n        // Use direct API call instead of complex fetchRankingData\r\n        handleDirectAPICall();\r\n    };\r\n\r\n    const handleForceRefresh = () => {\r\n        console.log('🔄 Force refresh triggered - clearing cache');\r\n        // Clear any cached data\r\n        setRankingData([]);\r\n        setError(null);\r\n        setLoading(true);\r\n\r\n        // Force a fresh API call\r\n        fetchRankingData(true);\r\n    };\r\n\r\n    // Simple direct API call for testing\r\n    const handleDirectAPICall = async () => {\r\n        console.log('🚀 Direct API call for testing...');\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        try {\r\n            const token = localStorage.getItem('token');\r\n            console.log('🚀 Using token:', token ? 'Token exists' : 'No token');\r\n\r\n            const response = await axiosInstance.get('/api/quiz/xp-leaderboard?limit=10', {\r\n                headers: {\r\n                    'Authorization': `Bearer ${token}`\r\n                }\r\n            });\r\n\r\n            console.log('🚀 Direct API response:', response.data);\r\n\r\n            if (response.data.success && response.data.data) {\r\n                console.log('🚀 Setting data directly:', response.data.data.length, 'users');\r\n\r\n                // Set the data directly without any transformation\r\n                setRankingData(response.data.data);\r\n                setError(null);\r\n                setLoading(false);\r\n\r\n                message.success(`Loaded ${response.data.data.length} users successfully!`);\r\n            } else {\r\n                throw new Error('API returned no data');\r\n            }\r\n        } catch (error) {\r\n            console.error('🚀 Direct API call failed:', error);\r\n\r\n            // Handle different types of errors gracefully\r\n            let errorMessage = 'Failed to load ranking data';\r\n\r\n            if (error.response?.status === 401) {\r\n                errorMessage = 'Session expired. Please refresh the page.';\r\n            } else if (error.response?.status === 500) {\r\n                errorMessage = 'Server error. Please try again.';\r\n            } else if (error.message) {\r\n                errorMessage = error.message;\r\n            }\r\n\r\n            setError(errorMessage);\r\n            setLoading(false);\r\n\r\n            // Only show error message if it's not a session issue\r\n            if (error.response?.status !== 401) {\r\n                message.error(errorMessage);\r\n            }\r\n        }\r\n    };\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center\">\r\n                <div className=\"text-center bg-white rounded-xl p-8 shadow-lg animate-fadeInUp\">\r\n                    <div className=\"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4 animate-spin\" />\r\n                    <p className=\"text-gray-600 font-medium\">Loading rankings...</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\">\r\n                <div className=\"text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full animate-fadeInUp\">\r\n                    <TbAlertCircle className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\r\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Error Loading Rankings</h2>\r\n                    <p className=\"text-gray-600 mb-6\">{error}</p>\r\n                    <button\r\n                        onClick={handleRefresh}\r\n                        className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 mx-auto hover:scale-105 active:scale-95\"\r\n                    >\r\n                        <TbRefresh className=\"w-5 h-5\" />\r\n                        <span>Try Again</span>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden\">\r\n            {/* Modern Background Pattern */}\r\n            <div className=\"absolute inset-0 overflow-hidden\">\r\n                <div className=\"absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse\"></div>\r\n                <div className=\"absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-200 to-pink-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-2000\"></div>\r\n                <div className=\"absolute bottom-0 left-1/2 w-96 h-96 bg-gradient-to-br from-yellow-200 to-orange-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-4000\"></div>\r\n\r\n                {/* Grid Pattern */}\r\n                <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\r\n            </div>\r\n\r\n            <div className=\"relative z-10\">\r\n                {/* Modern Header with Back Button */}\r\n                {/* Enhanced Header */}\r\n                <div className=\"p-6 border-b border-white/20 animate-slideInLeft\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex items-center space-x-4\">\r\n                            <button\r\n                                onClick={() => window.history.back()}\r\n                                className=\"bg-white/80 backdrop-blur-lg hover:bg-white/90 text-gray-700 px-4 py-2 rounded-xl font-semibold transition-all duration-200 flex items-center space-x-2 border border-gray-200 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95\"\r\n                            >\r\n                                <TbArrowLeft className=\"w-5 h-5\" />\r\n                                <span>Back</span>\r\n                            </button>\r\n\r\n                            {/* Page Title */}\r\n                            <div>\r\n                                <h1 className=\"text-2xl font-black text-gray-900 gradient-text-blue\">\r\n                                    🏆 Student Rankings\r\n                                </h1>\r\n                                <p className=\"text-sm text-gray-600 font-medium\">\r\n                                    Compete with students across all levels\r\n                                </p>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex items-center space-x-3\">\r\n                            {/* Current User Rank Display */}\r\n                            {currentUserRank && (\r\n                                <div className=\"bg-white/80 backdrop-blur-lg rounded-xl px-4 py-2 border border-gray-200 shadow-lg\">\r\n                                    <div className=\"text-center\">\r\n                                        <div className=\"text-xs text-gray-500 font-medium\">Your Rank</div>\r\n                                        <div className=\"text-lg font-black text-blue-600\">#{currentUserRank.rank}</div>\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n\r\n                            {/* Find Me Button */}\r\n                            {currentUserRank && (\r\n                                <button\r\n                                    onClick={handleFindMe}\r\n                                    className=\"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-3 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg hover:scale-105 active:scale-95\"\r\n                                >\r\n                                    <TbTarget className=\"w-5 h-5\" />\r\n                                    <span>Find Me</span>\r\n                                </button>\r\n                            )}\r\n\r\n                            {/* Refresh Button */}\r\n                            <button\r\n                                onClick={handleRefresh}\r\n                                disabled={refreshing}\r\n                                className=\"bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 disabled:cursor-not-allowed\"\r\n                            >\r\n                                <TbRefresh className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />\r\n                                <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>\r\n                            </button>\r\n\r\n\r\n\r\n\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Amazing Header */}\r\n                <div className=\"text-center mb-8 px-4 animate-fadeInUp\">\r\n                    <div className=\"flex items-center justify-center gap-4 mb-6\">\r\n                        <div className=\"relative animate-wiggle\">\r\n                            <TbTrophy className=\"text-6xl text-yellow-400 drop-shadow-lg\" />\r\n                            <div className=\"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-pulse\" />\r\n                        </div>\r\n                        <div>\r\n                            <h1 className=\"text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2\">\r\n                                🏆 LEADERBOARD\r\n                            </h1>\r\n                            <div className=\"flex items-center justify-center gap-2 text-white/80\">\r\n                                <TbStar className=\"text-yellow-400\" />\r\n                                <span className=\"text-lg font-medium\">Battle for Glory</span>\r\n                                <TbStar className=\"text-yellow-400\" />\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Stats Bar */}\r\n                    <div className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto animate-scaleIn\">\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\">\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbFlame className=\"text-orange-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    {currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'}\r\n                                </span>\r\n                            </div>\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbAward className=\"text-purple-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    {rankingData.length} Competitors\r\n                                </span>\r\n                            </div>\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbMedal className=\"text-yellow-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    Live Rankings\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Main Content */}\r\n            <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\">\r\n                {rankingData.length === 0 ? (\r\n                    <div className=\"text-center py-16 bg-white/80 backdrop-blur-lg rounded-2xl border border-white/50 shadow-2xl animate-fadeInUp\">\r\n                        <div className=\"animate-wiggle\">\r\n                            <TbMedal className=\"w-16 h-16 text-yellow-500 mx-auto mb-4\" />\r\n                        </div>\r\n                        <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">No Rankings Available</h3>\r\n                        <p className=\"text-gray-600 mb-6 text-lg\">Complete some quizzes to join the leaderboard!</p>\r\n                        <button\r\n                            onClick={handleRefresh}\r\n                            disabled={refreshing}\r\n                            className={`bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg hover:scale-105 active:scale-95 ${refreshing ? 'opacity-50 cursor-not-allowed' : ''}`}\r\n                        >\r\n                            <TbRefresh className={`w-5 h-5 inline mr-2 ${refreshing ? 'animate-spin' : ''}`} />\r\n                            {refreshing ? 'Refreshing...' : 'Refresh Rankings'}\r\n                        </button>\r\n                    </div>\r\n                ) : (\r\n                    <div className=\"bg-white/80 backdrop-blur-xl rounded-2xl border border-white/50 p-8 shadow-2xl animate-fadeInUp\">\r\n                        {/* LOAD USERS Button - Only show if no data */}\r\n                        {rankingData.length === 0 && (\r\n                            <div className=\"mb-6 text-center\">\r\n                                <button\r\n                                    onClick={handleDirectAPICall}\r\n                                    disabled={loading}\r\n                                    className=\"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 disabled:from-gray-400 disabled:to-gray-500 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-200 shadow-lg hover:scale-105 active:scale-95 disabled:cursor-not-allowed\"\r\n                                >\r\n                                    {loading ? '🔄 Loading...' : '🚀 LOAD USERS NOW'}\r\n                                </button>\r\n                            </div>\r\n                        )}\r\n\r\n                        {/* Debug Info */}\r\n                        <div className=\"mb-4 p-4 bg-blue-100 rounded-lg\">\r\n                            <h3 className=\"font-bold text-blue-800\">Debug Info:</h3>\r\n                            <p className=\"text-blue-700\">Ranking Data Length: {rankingData.length}</p>\r\n                            <p className=\"text-blue-700\">Loading: {loading.toString()}</p>\r\n                            <p className=\"text-blue-700\">Error: {error || 'None'}</p>\r\n                            <p className=\"text-blue-700\">User ID: {user?._id || 'None'}</p>\r\n                            {rankingData.length > 0 && (\r\n                                <div className=\"mt-2\">\r\n                                    <p className=\"text-blue-700\">Sample User: {rankingData[0]?.name || 'No name'}</p>\r\n                                    <p className=\"text-blue-700\">Sample XP: {rankingData[0]?.totalXP || 'No XP'}</p>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n\r\n                        {/* Simple List for Testing */}\r\n                        {rankingData.length > 0 && (\r\n                            <div className=\"mb-6\">\r\n                                <h3 className=\"text-lg font-bold mb-4\">Simple User List (Testing):</h3>\r\n                                {rankingData.slice(0, 5).map((user, index) => (\r\n                                    <div key={user._id || index} className=\"p-2 border-b border-gray-200\">\r\n                                        <span className=\"font-medium\">#{index + 1} {user.name || 'Unknown'}</span>\r\n                                        <span className=\"ml-4 text-blue-600\">{user.totalXP || 0} XP</span>\r\n                                        <span className=\"ml-4 text-gray-500\">Class {user.class || 'Unknown'}</span>\r\n                                    </div>\r\n                                ))}\r\n                            </div>\r\n                        )}\r\n\r\n                        {/* Achievement Banner for Top Users */}\r\n                        {currentUserRank && currentUserRank.rank <= 10 && (\r\n                            <div className=\"mb-6 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-xl p-4 text-white animate-bounce\">\r\n                                <div className=\"flex items-center justify-center space-x-2\">\r\n                                    <TbTrophy className=\"w-6 h-6\" />\r\n                                    <span className=\"font-bold text-lg\">\r\n                                        🎉 Congratulations! You're in the Top 10! 🎉\r\n                                    </span>\r\n                                    <TbTrophy className=\"w-6 h-6\" />\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n                        <UserRankingList\r\n                            users={rankingData}\r\n                            currentUserId={user?._id || null}\r\n                            layout=\"horizontal\"\r\n                            size=\"medium\"\r\n                            showStats={true}\r\n                            className=\"space-y-4\"\r\n                            currentUserRef={currentUserRef}\r\n                            showFindMe={showFindMe}\r\n                            lastUpdated={lastUpdated}\r\n                            autoRefresh={autoRefresh}\r\n                            onAutoRefreshToggle={() => setAutoRefresh(!autoRefresh)}\r\n                        />\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AAChF,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC7H,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,aAAa,MAAM,mBAAmB;AAC7C,SAASC,cAAc,EAAEC,mBAAmB,EAAEC,qBAAqB,QAAQ,0BAA0B;AACrG,SAASC,gBAAgB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,aAAA,EAAAC,cAAA;EAClBC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;;EAE5C;EACA,MAAMC,IAAI,GAAG1B,WAAW,CAAE2B,KAAK;IAAA,IAAAC,YAAA;IAAA,QAAAA,YAAA,GAAKD,KAAK,CAACE,KAAK,cAAAD,YAAA,uBAAXA,YAAA,CAAaF,IAAI;EAAA,GAAE,CAACI,IAAI,EAAEC,KAAK,KAAK;IACpE,OAAO,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,GAAG,OAAKD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,GAAG,KAAI,CAAAF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,IAAI,OAAKF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,IAAI;EACjE,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC4C,UAAU,EAAEC,aAAa,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMsD,cAAc,GAAGrD,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMsD,kBAAkB,GAAGtD,MAAM,CAAC,IAAI,CAAC;EAEvC2B,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAES,WAAW,CAACkB,MAAM,EAAE,UAAU,EAAEhB,OAAO,EAAE,QAAQ,EAAEE,KAAK,CAAC;EAE/G,MAAMe,gBAAgB,GAAGvD,WAAW,CAAC,OAAOwD,kBAAkB,GAAG,KAAK,KAAK;IACvE,IAAI;MACA;MACA,IAAI,CAAC5B,IAAI,IAAI,CAACA,IAAI,CAACM,GAAG,EAAE;QACpBR,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D;MACJ;MAEAD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEC,IAAI,CAACM,GAAG,CAAC;MACjEK,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI,CAACvB,cAAc,CAAC,CAAC,EAAE;QACnBU,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvDb,OAAO,CAAC0B,KAAK,CAAC,+CAA+C,CAAC;QAC9DiB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B;MACJ;;MAEA;MACA,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAClD,IAAI,CAACF,YAAY,EAAE;QACflC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QACtDb,OAAO,CAAC0B,KAAK,CAAC,+BAA+B,CAAC;QAC9CiB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B;MACJ;;MAEA;MACA,MAAMI,eAAe,GAAG9C,mBAAmB,CAAC2C,YAAY,CAAC;MACzD,IAAI,CAACG,eAAe,CAACC,OAAO,EAAE;QAC1BtC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoC,eAAe,CAACvB,KAAK,CAAC;QACjEtB,qBAAqB,CAAC,CAAC;QACvBJ,OAAO,CAAC0B,KAAK,CAAC,sCAAsC,CAAC;QACrDiB,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B;MACJ;MAEAjC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEsC,IAAI,CAACC,KAAK,CAACH,eAAe,CAACI,QAAQ,GAAG,IAAI,CAAC,EAAE,OAAO,CAAC;;MAE1G;MACA,IAAI;QACA,MAAMhD,gBAAgB,CAAC,CAAC;QACxBO,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAClD,CAAC,CAAC,OAAOyC,YAAY,EAAE;QACnB1C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEyC,YAAY,CAAC;QACrD;MACJ;;MAEA,IAAIZ,kBAAkB,EAAE;QACpBb,aAAa,CAAC,IAAI,CAAC;QACnB7B,OAAO,CAACwB,OAAO,CAAC,wBAAwB,EAAE,CAAC,CAAC;MAChD;;MAEA;MACA,IAAI+B,QAAQ;;MAEZ;MACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;;MAEtC;MACA,MAAMC,WAAW,GAAGZ,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAEjD,IAAI;QACA;QACApC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QAEvD,MAAM+C,UAAU,GAAG,MAAM3D,aAAa,CAAC4D,GAAG,CAAE,yCAAwCL,SAAU,EAAC,EAAE;UAC7FM,OAAO,EAAE;YACL,eAAe,EAAE,UAAU;YAC3B,eAAe,EAAG,UAASH,WAAY;UAC3C;QACJ,CAAC,CAAC;QAEF/C,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+C,UAAU,CAACG,IAAI,CAAC;QAE3D,IAAIH,UAAU,CAACG,IAAI,CAACC,OAAO,EAAE;UAAA,IAAAC,cAAA;UACzBV,QAAQ,GAAGK,UAAU,CAACG,IAAI;UAC1BnD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,EAAAoD,cAAA,GAAAV,QAAQ,CAACQ,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAezB,MAAM,KAAI,CAAC,EAAE,OAAO,CAAC;QACzF,CAAC,MAAM;UACH,MAAM,IAAI0B,KAAK,CAAC,oDAAoD,CAAC;QACzE;MACJ,CAAC,CAAC,OAAOC,OAAO,EAAE;QACdvD,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEsD,OAAO,CAAC;QAC7E,IAAI;UACA,MAAMC,gBAAgB,GAAG,MAAMnE,aAAa,CAAC4D,GAAG,CAAE,+CAA8CL,SAAU,EAAC,EAAE;YACzGM,OAAO,EAAE;cACL,eAAe,EAAE,UAAU;cAC3B,eAAe,EAAG,UAASH,WAAY;YAC3C;UACJ,CAAC,CAAC;UAEF/C,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEuD,gBAAgB,CAACL,IAAI,CAAC;UAEvE,IAAIK,gBAAgB,CAACL,IAAI,CAACC,OAAO,EAAE;YAAA,IAAAK,eAAA;YAC/Bd,QAAQ,GAAGa,gBAAgB,CAACL,IAAI;YAChCnD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,EAAAwD,eAAA,GAAAd,QAAQ,CAACQ,IAAI,cAAAM,eAAA,uBAAbA,eAAA,CAAe7B,MAAM,KAAI,CAAC,EAAE,OAAO,CAAC;UACzF,CAAC,MAAM;YACH,MAAM,IAAI0B,KAAK,CAAC,6BAA6B,CAAC;UAClD;QACJ,CAAC,CAAC,OAAOI,aAAa,EAAE;UACpB1D,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEyD,aAAa,CAAC;UAChEf,QAAQ,GAAG,MAAMzD,uBAAuB,CAAC,CAAC;UAC1Cc,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE0C,QAAQ,CAAC;QACzD;MACJ;MAEA,IAAIA,QAAQ,CAACS,OAAO,EAAE;QAAA,IAAAO,eAAA;QAClB3D,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,EAAA0D,eAAA,GAAAhB,QAAQ,CAACQ,IAAI,cAAAQ,eAAA,uBAAbA,eAAA,CAAe/B,MAAM,KAAI,CAAC,EAAE,OAAO,CAAC;;QAEnF;QACA,MAAMgC,eAAe,GAAGjB,QAAQ,CAACQ,IAAI,CAACU,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,MAAM;UAC5D;UACAC,MAAM,EAAEF,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACtD,GAAG;UACvCA,GAAG,EAAEsD,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACtD,GAAG;UACpCC,IAAI,EAAEqD,QAAQ,CAACG,QAAQ,IAAIH,QAAQ,CAACrD,IAAI,IAAI,cAAc;UAC1DyD,cAAc,EAAEJ,QAAQ,CAACK,SAAS,IAAIL,QAAQ,CAACM,YAAY;UAC3DA,YAAY,EAAEN,QAAQ,CAACM,YAAY,IAAIN,QAAQ,CAACK,SAAS;UACzDE,MAAM,EAAEP,QAAQ,CAACQ,UAAU,IAAIR,QAAQ,CAACO,MAAM,IAAI,gBAAgB;UAClEE,KAAK,EAAET,QAAQ,CAACU,SAAS,IAAIV,QAAQ,CAACS,KAAK,IAAI,SAAS;UACxDE,KAAK,EAAEX,QAAQ,CAACY,SAAS,IAAIZ,QAAQ,CAACW,KAAK,IAAI,SAAS;UACxDE,KAAK,EAAEb,QAAQ,CAACa,KAAK;UAErB;UACAC,WAAW,EAAEd,QAAQ,CAACe,iBAAiB,IAAIf,QAAQ,CAACc,WAAW,IAAI,CAAC;UACpEC,iBAAiB,EAAEf,QAAQ,CAACe,iBAAiB,IAAIf,QAAQ,CAACc,WAAW,IAAI,CAAC;UAC1EE,YAAY,EAAEhB,QAAQ,CAACiB,iBAAiB,IAAIjB,QAAQ,CAACgB,YAAY,IAAI,CAAC;UACtEC,iBAAiB,EAAEjB,QAAQ,CAACiB,iBAAiB,IAAIjB,QAAQ,CAACgB,YAAY,IAAI,CAAC;UAC3EE,gBAAgB,EAAElB,QAAQ,CAACkB,gBAAgB,IAAI,CAAC;UAChDC,UAAU,EAAEnB,QAAQ,CAACmB,UAAU,IAAI,CAAC;UACpCC,UAAU,EAAEpB,QAAQ,CAACoB,UAAU,IAAI,CAAC;UAEpC;UACAC,OAAO,EAAErB,QAAQ,CAACqB,OAAO,IAAI,CAAC;UAC9BC,YAAY,EAAEtB,QAAQ,CAACsB,YAAY,IAAI,CAAC;UACxCC,aAAa,EAAEvB,QAAQ,CAACuB,aAAa,IAAI,CAAC;UAC1CC,QAAQ,EAAExB,QAAQ,CAACwB,QAAQ,IAAI,CAAC;UAChCC,UAAU,EAAEzB,QAAQ,CAACyB,UAAU,IAAI,CAAC;UAEpC;UACAC,YAAY,EAAE1B,QAAQ,CAAC0B,YAAY,IAAI,CAAC;UACxCC,UAAU,EAAE3B,QAAQ,CAAC2B,UAAU,IAAI,CAAC;UACpCC,aAAa,EAAE5B,QAAQ,CAAC4B,aAAa,IAAI,CAAC;UAC1CC,YAAY,EAAE7B,QAAQ,CAAC6B,YAAY,IAAI,EAAE;UACzCC,gBAAgB,EAAE9B,QAAQ,CAAC8B,gBAAgB,KAAK9B,QAAQ,CAAC6B,YAAY,GAAG7B,QAAQ,CAAC6B,YAAY,CAAC/D,MAAM,GAAG,CAAC,CAAC;UAEzG;UACAiE,YAAY,EAAE/B,QAAQ,CAAC+B,YAAY,IAAI/B,QAAQ,CAACgC,oBAAoB,IAAIhC,QAAQ,CAACqB,OAAO,IAAIrB,QAAQ,CAACc,WAAW,IAAI,CAAC;UACrHmB,IAAI,EAAEjC,QAAQ,CAACiC,IAAI,IAAIhC,KAAK,GAAG,CAAC;UAChCiC,KAAK,EAAElC,QAAQ,CAAC+B,YAAY,IAAI/B,QAAQ,CAACkC,KAAK,IAAIlC,QAAQ,CAACqB,OAAO,IAAIrB,QAAQ,CAACc,WAAW,IAAI,CAAC;UAE/F;UACAqB,kBAAkB,EAAEnC,QAAQ,CAACmC,kBAAkB,IAAInC,QAAQ,CAACoC,4BAA4B,IAAI,MAAM;UAClGA,4BAA4B,EAAEpC,QAAQ,CAACoC,4BAA4B,IAAIpC,QAAQ,CAACmC,kBAAkB,IAAI,MAAM;UAC5GE,gBAAgB,EAAErC,QAAQ,CAACqC,gBAAgB;UAC3CC,mBAAmB,EAAEtC,QAAQ,CAACsC,mBAAmB;UAEjD;UACAC,SAAS,EAAEvC,QAAQ,CAACuC,SAAS,IAAI,IAAI;UAErC;UACAC,SAAS,EAAExC,QAAQ,CAACwC,SAAS;UAC7BC,SAAS,EAAEzC,QAAQ,CAACyC;QACxB,CAAC,CAAC,CAAC;QAEHvG,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE2D,eAAe,CAAChC,MAAM,EAAE,OAAO,CAAC;QAE3EjB,cAAc,CAACiD,eAAe,CAAC;QAC/B7C,QAAQ,CAAC,IAAI,CAAC;QACdQ,cAAc,CAAC,IAAIsB,IAAI,CAAC,CAAC,CAAC;;QAE1B;QACA,MAAM2D,QAAQ,GAAG5C,eAAe,CAAC6C,SAAS,CAACC,IAAI,IAC3CA,IAAI,CAAClG,GAAG,MAAKN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,KAAIkG,IAAI,CAAC1C,MAAM,MAAK9D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CACvD,CAAC;QACDW,kBAAkB,CAACqF,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;QAEvD,IAAIA,QAAQ,IAAI,CAAC,EAAE;UACfxG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuG,QAAQ,GAAG,CAAC,CAAC;QACtD,CAAC,MAAM;UACHxG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAC5D;QAEA,IAAI6B,kBAAkB,EAAE;UACpB1C,OAAO,CAACgE,OAAO,CAAC,gCAAgC,CAAC;QACrD;MACJ,CAAC,MAAM;QACHpD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE0C,QAAQ,CAAC;QACnD5B,QAAQ,CAAC4B,QAAQ,CAACvD,OAAO,IAAI,8BAA8B,CAAC;QAC5DA,OAAO,CAAC0B,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACJ,CAAC,CAAC,OAAO6F,GAAG,EAAE;MACV3G,OAAO,CAACc,KAAK,CAAC,sBAAsB,EAAE6F,GAAG,CAAC;;MAE1C;MACA,IAAIC,YAAY,GAAG,2CAA2C;MAE9D,IAAID,GAAG,CAAChE,QAAQ,EAAE;QAAA,IAAAkE,kBAAA;QACd;QACA,MAAMC,MAAM,GAAGH,GAAG,CAAChE,QAAQ,CAACmE,MAAM;QAClC,MAAMC,eAAe,GAAG,EAAAF,kBAAA,GAAAF,GAAG,CAAChE,QAAQ,CAACQ,IAAI,cAAA0D,kBAAA,uBAAjBA,kBAAA,CAAmBzH,OAAO,KAAI,EAAE;QAExD,IAAI0H,MAAM,KAAK,GAAG,EAAE;UAChB;UACA9G,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxDkC,YAAY,CAAC6E,UAAU,CAAC,OAAO,CAAC;UAChC7E,YAAY,CAAC6E,UAAU,CAAC,MAAM,CAAC;UAC/BJ,YAAY,GAAG,+CAA+C;UAC9DK,UAAU,CAAC,MAAM;YACblF,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;UACnC,CAAC,EAAE,IAAI,CAAC;QACZ,CAAC,MAAM;UACH2E,YAAY,GAAGG,eAAe,IAAK,iBAAgBD,MAAO,EAAC;QAC/D;MACJ,CAAC,MAAM,IAAIH,GAAG,CAACO,OAAO,EAAE;QACpB;QACAN,YAAY,GAAG,4CAA4C;MAC/D,CAAC,MAAM,IAAID,GAAG,CAACvH,OAAO,EAAE;QACpB;QACAwH,YAAY,GAAGD,GAAG,CAACvH,OAAO;MAC9B;MAEA2B,QAAQ,CAAC6F,YAAY,CAAC;;MAEtB;MACA,IAAIE,MAAM,KAAK,GAAG,EAAE;QAChB1H,OAAO,CAAC0B,KAAK,CAAC8F,YAAY,CAAC;MAC/B;IACJ,CAAC,SAAS;MACN/F,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC,EAAE,CAACf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEjBrC,SAAS,CAAC,MAAM;IACZ6B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,IAAI,CAAC;IACxDF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,CAAC,CAACC,IAAI,EAAE,UAAU,EAAEA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,CAAC;IAEhE,IAAIN,IAAI,IAAIA,IAAI,CAACM,GAAG,EAAE;MAClBR,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD4B,gBAAgB,CAAC,CAAC;IACtB,CAAC,MAAM;MACH7B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;IACxD;;IAEA;IACA,OAAO,MAAM;MACTY,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,KAAK,CAAC;MACpBF,QAAQ,CAAC,IAAI,CAAC;IAClB,CAAC;EACL,CAAC,EAAE,CAACc,gBAAgB,CAAC,CAAC;;EAEtB;EACA1D,SAAS,CAAC,MAAM;IACZ,IAAI+B,IAAI,IAAIA,IAAI,CAACM,GAAG,EAAE;MAClBR,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEC,IAAI,CAACM,GAAG,CAAC;MAC9EqB,gBAAgB,CAAC,CAAC;IACtB;EACJ,CAAC,EAAE,CAAC3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,EAAEqB,gBAAgB,CAAC,CAAC;;EAEjC;EACA1D,SAAS,CAAC,MAAM;IACZ,MAAMgJ,KAAK,GAAGF,UAAU,CAAC,MAAM;MAC3BjH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;MAC7D;MACA,IAAIS,WAAW,CAACkB,MAAM,KAAK,CAAC,IAAI,CAAChB,OAAO,EAAE;QACtCwG,mBAAmB,CAAC,CAAC;MACzB;IACJ,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,YAAY,CAACF,KAAK,CAAC;EACpC,CAAC,EAAE,CAACzG,WAAW,CAACkB,MAAM,EAAEhB,OAAO,CAAC,CAAC;;EAEjC;EACAzC,SAAS,CAAC,MAAM;IACZ,IAAIqD,WAAW,EAAE;MACbG,kBAAkB,CAAC2F,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC3C1F,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC7B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACf,CAAC,MAAM;MACH,IAAIF,kBAAkB,CAAC2F,OAAO,EAAE;QAC5BE,aAAa,CAAC7F,kBAAkB,CAAC2F,OAAO,CAAC;QACzC3F,kBAAkB,CAAC2F,OAAO,GAAG,IAAI;MACrC;IACJ;;IAEA;IACA,OAAO,MAAM;MACT,IAAI3F,kBAAkB,CAAC2F,OAAO,EAAE;QAC5BE,aAAa,CAAC7F,kBAAkB,CAAC2F,OAAO,CAAC;MAC7C;IACJ,CAAC;EACL,CAAC,EAAE,CAAC9F,WAAW,EAAEK,gBAAgB,CAAC,CAAC;;EAEnC;EACA,MAAM4F,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAI/F,cAAc,CAAC4F,OAAO,EAAE;MACxB5F,cAAc,CAAC4F,OAAO,CAACI,cAAc,CAAC;QAClCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACFvG,aAAa,CAAC,IAAI,CAAC;MACnB4F,UAAU,CAAC,MAAM5F,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAClD;EACJ,CAAC;;EAED,MAAMwG,aAAa,GAAGA,CAAA,KAAM;IACxB7H,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;IAE1C;IACAc,QAAQ,CAAC,IAAI,CAAC;;IAEd;IACAqG,mBAAmB,CAAC,CAAC;EACzB,CAAC;EAED,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAC7B9H,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;IAC1D;IACAU,cAAc,CAAC,EAAE,CAAC;IAClBI,QAAQ,CAAC,IAAI,CAAC;IACdF,UAAU,CAAC,IAAI,CAAC;;IAEhB;IACAgB,gBAAgB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMuF,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpCpH,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDY,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACA,MAAMgH,KAAK,GAAG5F,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3CpC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE8H,KAAK,GAAG,cAAc,GAAG,UAAU,CAAC;MAEnE,MAAMpF,QAAQ,GAAG,MAAMtD,aAAa,CAAC4D,GAAG,CAAC,mCAAmC,EAAE;QAC1EC,OAAO,EAAE;UACL,eAAe,EAAG,UAAS6E,KAAM;QACrC;MACJ,CAAC,CAAC;MAEF/H,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE0C,QAAQ,CAACQ,IAAI,CAAC;MAErD,IAAIR,QAAQ,CAACQ,IAAI,CAACC,OAAO,IAAIT,QAAQ,CAACQ,IAAI,CAACA,IAAI,EAAE;QAC7CnD,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0C,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAACvB,MAAM,EAAE,OAAO,CAAC;;QAE5E;QACAjB,cAAc,CAACgC,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAAC;QAClCpC,QAAQ,CAAC,IAAI,CAAC;QACdF,UAAU,CAAC,KAAK,CAAC;QAEjBzB,OAAO,CAACgE,OAAO,CAAE,UAAST,QAAQ,CAACQ,IAAI,CAACA,IAAI,CAACvB,MAAO,sBAAqB,CAAC;MAC9E,CAAC,MAAM;QACH,MAAM,IAAI0B,KAAK,CAAC,sBAAsB,CAAC;MAC3C;IACJ,CAAC,CAAC,OAAOxC,KAAK,EAAE;MAAA,IAAAkH,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACZlI,OAAO,CAACc,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAElD;MACA,IAAI8F,YAAY,GAAG,6BAA6B;MAEhD,IAAI,EAAAoB,eAAA,GAAAlH,KAAK,CAAC6B,QAAQ,cAAAqF,eAAA,uBAAdA,eAAA,CAAgBlB,MAAM,MAAK,GAAG,EAAE;QAChCF,YAAY,GAAG,2CAA2C;MAC9D,CAAC,MAAM,IAAI,EAAAqB,gBAAA,GAAAnH,KAAK,CAAC6B,QAAQ,cAAAsF,gBAAA,uBAAdA,gBAAA,CAAgBnB,MAAM,MAAK,GAAG,EAAE;QACvCF,YAAY,GAAG,iCAAiC;MACpD,CAAC,MAAM,IAAI9F,KAAK,CAAC1B,OAAO,EAAE;QACtBwH,YAAY,GAAG9F,KAAK,CAAC1B,OAAO;MAChC;MAEA2B,QAAQ,CAAC6F,YAAY,CAAC;MACtB/F,UAAU,CAAC,KAAK,CAAC;;MAEjB;MACA,IAAI,EAAAqH,gBAAA,GAAApH,KAAK,CAAC6B,QAAQ,cAAAuF,gBAAA,uBAAdA,gBAAA,CAAgBpB,MAAM,MAAK,GAAG,EAAE;QAChC1H,OAAO,CAAC0B,KAAK,CAAC8F,YAAY,CAAC;MAC/B;IACJ;EACJ,CAAC;EAED,IAAIhG,OAAO,EAAE;IACT,oBACIjB,OAAA;MAAKwI,SAAS,EAAC,2FAA2F;MAAAC,QAAA,eACtGzI,OAAA;QAAKwI,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC3EzI,OAAA;UAAKwI,SAAS,EAAC;QAAgG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClH7I,OAAA;UAAGwI,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAI1H,KAAK,EAAE;IACP,oBACInB,OAAA;MAAKwI,SAAS,EAAC,+FAA+F;MAAAC,QAAA,eAC1GzI,OAAA;QAAKwI,SAAS,EAAC,gFAAgF;QAAAC,QAAA,gBAC3FzI,OAAA,CAACf,aAAa;UAACuJ,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjE7I,OAAA;UAAIwI,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF7I,OAAA;UAAGwI,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEtH;QAAK;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7C7I,OAAA;UACI8I,OAAO,EAAEZ,aAAc;UACvBM,SAAS,EAAC,2KAA2K;UAAAC,QAAA,gBAErLzI,OAAA,CAAChB,SAAS;YAACwJ,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjC7I,OAAA;YAAAyI,QAAA,EAAM;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACI7I,OAAA;IAAKwI,SAAS,EAAC,iGAAiG;IAAAC,QAAA,gBAE5GzI,OAAA;MAAKwI,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC7CzI,OAAA;QAAKwI,SAAS,EAAC;MAAwJ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9K7I,OAAA;QAAKwI,SAAS,EAAC;MAA8K;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpM7I,OAAA;QAAKwI,SAAS,EAAC;MAAoL;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG1M7I,OAAA;QAAKwI,SAAS,EAAC;MAA4C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAEN7I,OAAA;MAAKwI,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAG1BzI,OAAA;QAAKwI,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC7DzI,OAAA;UAAKwI,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAC9CzI,OAAA;YAAKwI,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBACxCzI,OAAA;cACI8I,OAAO,EAAEA,CAAA,KAAM1G,MAAM,CAAC2G,OAAO,CAACC,IAAI,CAAC,CAAE;cACrCR,SAAS,EAAC,0OAA0O;cAAAC,QAAA,gBAEpPzI,OAAA,CAACd,WAAW;gBAACsJ,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnC7I,OAAA;gBAAAyI,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAGT7I,OAAA;cAAAyI,QAAA,gBACIzI,OAAA;gBAAIwI,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAErE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7I,OAAA;gBAAGwI,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN7I,OAAA;YAAKwI,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GAEvClH,eAAe,iBACZvB,OAAA;cAAKwI,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eAC/FzI,OAAA;gBAAKwI,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxBzI,OAAA;kBAAKwI,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClE7I,OAAA;kBAAKwI,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,GAAC,EAAClH,eAAe,CAAC6E,IAAI;gBAAA;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR,EAGAtH,eAAe,iBACZvB,OAAA;cACI8I,OAAO,EAAEhB,YAAa;cACtBU,SAAS,EAAC,sOAAsO;cAAAC,QAAA,gBAEhPzI,OAAA,CAACb,QAAQ;gBAACqJ,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChC7I,OAAA;gBAAAyI,QAAA,EAAM;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACX,eAGD7I,OAAA;cACI8I,OAAO,EAAEZ,aAAc;cACvBe,QAAQ,EAAE5H,UAAW;cACrBmH,SAAS,EAAC,oNAAoN;cAAAC,QAAA,gBAE9NzI,OAAA,CAAChB,SAAS;gBAACwJ,SAAS,EAAG,WAAUnH,UAAU,GAAG,cAAc,GAAG,EAAG;cAAE;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACvE7I,OAAA;gBAAAyI,QAAA,EAAOpH,UAAU,GAAG,eAAe,GAAG;cAAS;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN7I,OAAA;QAAKwI,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACnDzI,OAAA;UAAKwI,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBACxDzI,OAAA;YAAKwI,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACpCzI,OAAA,CAAClB,QAAQ;cAAC0J,SAAS,EAAC;YAAyC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChE7I,OAAA;cAAKwI,SAAS,EAAC;YAA2E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eACN7I,OAAA;YAAAyI,QAAA,gBACIzI,OAAA;cAAIwI,SAAS,EAAC,oHAAoH;cAAAC,QAAA,EAAC;YAEnI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7I,OAAA;cAAKwI,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACjEzI,OAAA,CAACZ,MAAM;gBAACoJ,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtC7I,OAAA;gBAAMwI,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7D7I,OAAA,CAACZ,MAAM;gBAACoJ,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN7I,OAAA;UAAKwI,SAAS,EAAC,4GAA4G;UAAAC,QAAA,eACvHzI,OAAA;YAAKwI,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAC9DzI,OAAA;cAAKwI,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDzI,OAAA,CAACX,OAAO;gBAACmJ,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C7I,OAAA;gBAAMwI,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACrClH,eAAe,GAAI,eAAcA,eAAgB,EAAC,GAAG;cAAuB;gBAAAmH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7I,OAAA;cAAKwI,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDzI,OAAA,CAACV,OAAO;gBAACkJ,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C7I,OAAA;gBAAMwI,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GACrC1H,WAAW,CAACkB,MAAM,EAAC,cACxB;cAAA;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN7I,OAAA;cAAKwI,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnDzI,OAAA,CAACjB,OAAO;gBAACyJ,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C7I,OAAA;gBAAMwI,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAE3C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN7I,OAAA;MAAKwI,SAAS,EAAC,2DAA2D;MAAAC,QAAA,EACrE1H,WAAW,CAACkB,MAAM,KAAK,CAAC,gBACrBjC,OAAA;QAAKwI,SAAS,EAAC,+GAA+G;QAAAC,QAAA,gBAC1HzI,OAAA;UAAKwI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC3BzI,OAAA,CAACjB,OAAO;YAACyJ,SAAS,EAAC;UAAwC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACN7I,OAAA;UAAIwI,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChF7I,OAAA;UAAGwI,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5F7I,OAAA;UACI8I,OAAO,EAAEZ,aAAc;UACvBe,QAAQ,EAAE5H,UAAW;UACrBmH,SAAS,EAAG,wMAAuMnH,UAAU,GAAG,+BAA+B,GAAG,EAAG,EAAE;UAAAoH,QAAA,gBAEvQzI,OAAA,CAAChB,SAAS;YAACwJ,SAAS,EAAG,uBAAsBnH,UAAU,GAAG,cAAc,GAAG,EAAG;UAAE;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAClFxH,UAAU,GAAG,eAAe,GAAG,kBAAkB;QAAA;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,gBAEN7I,OAAA;QAAKwI,SAAS,EAAC,iGAAiG;QAAAC,QAAA,GAE3G1H,WAAW,CAACkB,MAAM,KAAK,CAAC,iBACrBjC,OAAA;UAAKwI,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC7BzI,OAAA;YACI8I,OAAO,EAAErB,mBAAoB;YAC7BwB,QAAQ,EAAEhI,OAAQ;YAClBuH,SAAS,EAAC,oRAAoR;YAAAC,QAAA,EAE7RxH,OAAO,GAAG,eAAe,GAAG;UAAmB;YAAAyH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR,eAGD7I,OAAA;UAAKwI,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC5CzI,OAAA;YAAIwI,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxD7I,OAAA;YAAGwI,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,uBAAqB,EAAC1H,WAAW,CAACkB,MAAM;UAAA;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1E7I,OAAA;YAAGwI,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,WAAS,EAACxH,OAAO,CAACiI,QAAQ,CAAC,CAAC;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9D7I,OAAA;YAAGwI,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,SAAO,EAACtH,KAAK,IAAI,MAAM;UAAA;YAAAuH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzD7I,OAAA;YAAGwI,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAC,WAAS,EAAC,CAAAlI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,KAAI,MAAM;UAAA;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC9D9H,WAAW,CAACkB,MAAM,GAAG,CAAC,iBACnBjC,OAAA;YAAKwI,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACjBzI,OAAA;cAAGwI,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,eAAa,EAAC,EAAAtI,aAAA,GAAAY,WAAW,CAAC,CAAC,CAAC,cAAAZ,aAAA,uBAAdA,aAAA,CAAgBW,IAAI,KAAI,SAAS;YAAA;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjF7I,OAAA;cAAGwI,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAC,aAAW,EAAC,EAAArI,cAAA,GAAAW,WAAW,CAAC,CAAC,CAAC,cAAAX,cAAA,uBAAdA,cAAA,CAAgBoF,OAAO,KAAI,OAAO;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGL9H,WAAW,CAACkB,MAAM,GAAG,CAAC,iBACnBjC,OAAA;UAAKwI,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACjBzI,OAAA;YAAIwI,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACtE9H,WAAW,CAACoI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACjF,GAAG,CAAC,CAAC3D,IAAI,EAAE6D,KAAK,kBACrCpE,OAAA;YAA6BwI,SAAS,EAAC,8BAA8B;YAAAC,QAAA,gBACjEzI,OAAA;cAAMwI,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAC,GAAC,EAACrE,KAAK,GAAG,CAAC,EAAC,GAAC,EAAC7D,IAAI,CAACO,IAAI,IAAI,SAAS;YAAA;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1E7I,OAAA;cAAMwI,SAAS,EAAC,oBAAoB;cAAAC,QAAA,GAAElI,IAAI,CAACiF,OAAO,IAAI,CAAC,EAAC,KAAG;YAAA;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClE7I,OAAA;cAAMwI,SAAS,EAAC,oBAAoB;cAAAC,QAAA,GAAC,QAAM,EAAClI,IAAI,CAACqE,KAAK,IAAI,SAAS;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAHrEtI,IAAI,CAACM,GAAG,IAAIuD,KAAK;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAItB,CACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAGAtH,eAAe,IAAIA,eAAe,CAAC6E,IAAI,IAAI,EAAE,iBAC1CpG,OAAA;UAAKwI,SAAS,EAAC,0GAA0G;UAAAC,QAAA,eACrHzI,OAAA;YAAKwI,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACvDzI,OAAA,CAAClB,QAAQ;cAAC0J,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChC7I,OAAA;cAAMwI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACP7I,OAAA,CAAClB,QAAQ;cAAC0J,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,eACD7I,OAAA,CAACR,eAAe;UACZkB,KAAK,EAAEK,WAAY;UACnBqI,aAAa,EAAE,CAAA7I,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,GAAG,KAAI,IAAK;UACjCwI,MAAM,EAAC,YAAY;UACnBC,IAAI,EAAC,QAAQ;UACbC,SAAS,EAAE,IAAK;UAChBf,SAAS,EAAC,WAAW;UACrBzG,cAAc,EAAEA,cAAe;UAC/BN,UAAU,EAAEA,UAAW;UACvBE,WAAW,EAAEA,WAAY;UACzBE,WAAW,EAAEA,WAAY;UACzB2H,mBAAmB,EAAEA,CAAA,KAAM1H,cAAc,CAAC,CAACD,WAAW;QAAE;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC3I,EAAA,CAroBID,OAAO;EAAA,QAIIpB,WAAW;AAAA;AAAA4K,EAAA,GAJtBxJ,OAAO;AAuoBb,eAAeA,OAAO;AAAC,IAAAwJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}