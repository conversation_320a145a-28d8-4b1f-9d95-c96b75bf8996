import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import {
  Tb<PERSON><PERSON><PERSON>,
  Tb<PERSON><PERSON><PERSON>,
  TbClock,
  TbQuestionMark,
  TbTrophy,
  TbPlayerPlay,
  TbBrain,
  TbTarget,
  TbCheck,
  TbX
} from 'react-icons/tb';
import { getAllExams } from '../../../apicalls/exams';
import { getAllReportsByUser } from '../../../apicalls/reports';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import './responsive.css';
import './style.css';

const Quiz = () => {
  const [exams, setExams] = useState([]);
  const [filteredExams, setFilteredExams] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedClass, setSelectedClass] = useState('');
  const [userResults, setUserResults] = useState({});
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  const getUserResults = async () => {
    try {
      if (!user?._id) return;

      const response = await getAllReportsByUser({ userId: user._id });
      if (response.success) {
        const resultsMap = {};
        response.data.forEach(report => {
          const examId = report.exam?._id;
          if (!examId) return;
          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {
            resultsMap[examId] = {
              verdict: report.verdict,
              percentage: report.percentage,
              correctAnswers: report.correctAnswers,
              wrongAnswers: report.wrongAnswers,
              totalQuestions: report.totalQuestions,
              obtainedMarks: report.obtainedMarks,
              totalMarks: report.totalMarks,
              timeTaken: report.timeTaken,
              completedAt: report.createdAt,
            };
          }
        });
        setUserResults(resultsMap);
      }
    } catch (error) {
      console.error('Error fetching user results:', error);
    }
  };

  useEffect(() => {
    const getExams = async () => {
      try {
        dispatch(ShowLoading());
        const response = await getAllExams();
        dispatch(HideLoading());

        if (response.success) {
          // Filter exams by user's level
          const userLevelExams = response.data.filter(exam => {
            if (!exam.level || !user.level) return false;
            return exam.level.toLowerCase() === user.level.toLowerCase();
          });

          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
          setExams(sortedExams);

          // Set default class filter to user's class
          if (user?.class) {
            setSelectedClass(String(user.class));
          }
        } else {
          message.error(response.message);
        }
      } catch (error) {
        dispatch(HideLoading());
        message.error(error.message);
      } finally {
        setLoading(false);
      }
    };

    getExams();
    getUserResults();
  }, [dispatch, user?.level, user?.class, user?._id]);

  useEffect(() => {
    let filtered = exams;
    if (searchTerm) {
      filtered = filtered.filter(exam =>
        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    if (selectedClass) {
      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));
    }
    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    setFilteredExams(filtered);
  }, [exams, searchTerm, selectedClass]);

  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();

  const handleQuizStart = (quiz) => {
    navigate(`/quiz/${quiz._id}/play`);
  };

  // Custom Quiz Card Component
  const QuizCard = ({ quiz, userResult, onStart }) => {
    const getStatusInfo = () => {
      if (!userResult) {
        return {
          status: 'not-attempted',
          color: 'bg-blue-100 text-blue-800',
          icon: TbTarget,
          text: 'Not Attempted'
        };
      }

      if (userResult.verdict === 'Pass') {
        return {
          status: 'passed',
          color: 'bg-green-100 text-green-800',
          icon: TbCheck,
          text: `Passed (${userResult.percentage}%)`
        };
      } else {
        return {
          status: 'failed',
          color: 'bg-red-100 text-red-800',
          icon: TbX,
          text: `Failed (${userResult.percentage}%)`
        };
      }
    };

    const statusInfo = getStatusInfo();
    const StatusIcon = statusInfo.icon;

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -8, scale: 1.02 }}
        transition={{ duration: 0.3 }}
        className="bg-white rounded-2xl shadow-lg hover:shadow-2xl border border-gray-100 overflow-hidden group"
      >
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white relative overflow-hidden">
          <div className="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16"></div>
          <div className="relative z-10">
            <div className="flex items-start justify-between mb-4">
              <div className="bg-white bg-opacity-20 rounded-lg p-2">
                <TbBrain className="w-6 h-6" />
              </div>
              <div className={`px-3 py-1 rounded-full text-xs font-semibold ${statusInfo.color} bg-white`}>
                <div className="flex items-center gap-1">
                  <StatusIcon className="w-3 h-3" />
                  {statusInfo.text}
                </div>
              </div>
            </div>
            <h3 className="text-xl font-bold mb-2 line-clamp-2">{quiz.name}</h3>
            <div className="flex items-center gap-4 text-sm opacity-90">
              <span className="bg-white bg-opacity-20 px-2 py-1 rounded">{quiz.subject}</span>
              <span>Class {quiz.class}</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Quiz Stats */}
          <div className="grid grid-cols-3 gap-4 mb-6">
            <div className="text-center">
              <div className="bg-blue-50 rounded-lg p-3 mb-2">
                <TbQuestionMark className="w-5 h-5 text-blue-600 mx-auto" />
              </div>
              <div className="text-lg font-bold text-gray-900">{quiz.questions?.length || 0}</div>
              <div className="text-xs text-gray-500">Questions</div>
            </div>
            <div className="text-center">
              <div className="bg-green-50 rounded-lg p-3 mb-2">
                <TbClock className="w-5 h-5 text-green-600 mx-auto" />
              </div>
              <div className="text-lg font-bold text-gray-900">{quiz.duration || 0}</div>
              <div className="text-xs text-gray-500">Minutes</div>
            </div>
            <div className="text-center">
              <div className="bg-purple-50 rounded-lg p-3 mb-2">
                <TbTrophy className="w-5 h-5 text-purple-600 mx-auto" />
              </div>
              <div className="text-lg font-bold text-gray-900">{quiz.totalMarks || 0}</div>
              <div className="text-xs text-gray-500">Points</div>
            </div>
          </div>

          {/* Progress Bar (if attempted) */}
          {userResult && (
            <div className="mb-6">
              <div className="flex justify-between text-sm mb-2">
                <span className="text-gray-600">Your Progress</span>
                <span className="font-semibold">{userResult.percentage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-500 ${
                    userResult.verdict === 'Pass' ? 'bg-green-500' : 'bg-red-500'
                  }`}
                  style={{ width: `${userResult.percentage}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{userResult.correctAnswers} correct</span>
                <span>{userResult.wrongAnswers} wrong</span>
              </div>
            </div>
          )}

          {/* Action Button */}
          <button
            onClick={() => onStart(quiz)}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 group"
          >
            <TbPlayerPlay className="w-5 h-5 group-hover:scale-110 transition-transform" />
            {userResult ? 'Retake Quiz' : 'Start Quiz'}
          </button>
        </div>
      </motion.div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading quizzes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-6 shadow-lg">
            <TbBrain className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Challenge Your Brain, Beat the Rest
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-6">
            Test your knowledge with our comprehensive quizzes designed for Class {user?.class || 'All Classes'}
          </p>
          <div className="flex items-center justify-center gap-8 text-sm text-gray-500">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>{filteredExams.length} Available Quizzes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>Level: {user?.level || 'All Levels'}</span>
            </div>
          </div>
        </motion.div>

        {/* Search and Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="max-w-4xl mx-auto mb-12"
        >
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <TbSearch className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search quizzes by name or subject..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                />
              </div>
              <div className="md:w-64">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <TbFilter className="h-5 w-5 text-gray-400" />
                  </div>
                  <select
                    value={selectedClass}
                    onChange={(e) => setSelectedClass(e.target.value)}
                    className="block w-full pl-12 pr-8 py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none"
                  >
                    <option value="">All Classes</option>
                    {availableClasses.map((className) => (
                      <option key={className} value={className}>Class {className}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quiz Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          {filteredExams.length === 0 ? (
            <div className="text-center py-16">
              <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
                <TbTarget className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No Quizzes Found</h3>
                <p className="text-gray-600">
                  {searchTerm || selectedClass
                    ? "Try adjusting your search or filter criteria."
                    : "No quizzes are available for your level at the moment."
                  }
                </p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredExams.map((quiz, index) => (
                <motion.div
                  key={quiz._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <QuizCard
                    quiz={quiz}
                    userResult={userResults[quiz._id]}
                    onStart={handleQuizStart}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default Quiz;
