{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { TbUser, TbUsers, TbTrophy, TbPlayerPlay, TbPlayerPause, TbClock, TbSearch, TbFilter } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingList = ({\n  users = [],\n  currentUserId = null,\n  layout = 'horizontal',\n  // 'horizontal', 'vertical', 'grid'\n  size = 'medium',\n  showStats = true,\n  className = '',\n  currentUserRef = null,\n  showFindMe = false,\n  lastUpdated = null,\n  autoRefresh = false,\n  onAutoRefreshToggle = null\n}) => {\n  _s();\n  // State for search and filtering\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'\n  const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'\n  const [localShowFindMe, setLocalShowFindMe] = useState(false);\n  const localCurrentUserRef = useRef(null);\n\n  // Use passed refs or local ones\n  const userRef = currentUserRef || localCurrentUserRef;\n  const findMeActive = showFindMe || localShowFindMe;\n\n  // Filter and search users\n  const filteredUsers = users.filter(user => {\n    var _user$name, _user$email, _user$subscriptionSta;\n    // Search filter\n    const matchesSearch = ((_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Subscription filter\n    const userStatus = ((_user$subscriptionSta = user.subscriptionStatus) === null || _user$subscriptionSta === void 0 ? void 0 : _user$subscriptionSta.toLowerCase()) || 'free';\n    let matchesFilter = true;\n    switch (filterType) {\n      case 'premium':\n        matchesFilter = userStatus === 'premium' || userStatus === 'active';\n        break;\n      case 'expired':\n        matchesFilter = userStatus === 'expired';\n        break;\n      case 'free':\n        matchesFilter = userStatus === 'free';\n        break;\n      default:\n        matchesFilter = true;\n    }\n    return matchesSearch && matchesFilter;\n  }).sort((a, b) => {\n    switch (sortBy) {\n      case 'xp':\n        return (b.totalXP || 0) - (a.totalXP || 0);\n      case 'name':\n        return (a.name || '').localeCompare(b.name || '');\n      default:\n        return (a.rank || 0) - (b.rank || 0);\n    }\n  });\n\n  // Calculate class ranks for filtered users\n  const usersWithClassRank = filteredUsers.map(user => {\n    // Group users by class and calculate class rank\n    const sameClassUsers = filteredUsers.filter(u => u.class === user.class);\n    const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n    return {\n      ...user,\n      classRank\n    };\n  });\n\n  // Find current user\n  const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n\n  // Scroll to current user\n  const scrollToCurrentUser = () => {\n    if (userRef.current) {\n      userRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setLocalShowFindMe(true);\n      // Hide the highlight after 3 seconds\n      setTimeout(() => setLocalShowFindMe(false), 3000);\n    }\n  };\n\n  // Get layout classes\n  const getLayoutClasses = () => {\n    switch (layout) {\n      case 'vertical':\n        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n      case 'grid':\n        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n      case 'horizontal':\n      default:\n        return 'space-y-3';\n    }\n  };\n\n  // Container animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  // Stats summary with enhanced calculations\n  const totalUsers = users.length;\n  const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium' || u.normalizedSubscriptionStatus === 'premium').length;\n\n  // Use ranking score or XP as the primary metric\n  const topScore = users.length > 0 ? Math.max(...users.map(u => u.rankingScore || u.totalXP || u.totalPoints || 0)) : 0;\n\n  // Calculate additional stats\n  const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n  const averageXP = users.length > 0 ? Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200 animate-fadeInUp\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-white/50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search users by name or email...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbFilter, {\n              className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: filterType,\n              onChange: e => setFilterType(e.target.value),\n              className: \"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"all\",\n                children: \"All Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"premium\",\n                children: \"Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"free\",\n                children: \"Free\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"expired\",\n                children: \"Expired\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: sortBy,\n            onChange: e => setSortBy(e.target.value),\n            className: \"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"rank\",\n              children: \"Sort by Rank\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"xp\",\n              children: \"Sort by XP\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"name\",\n              children: \"Sort by Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 text-sm text-gray-600\",\n          children: [\"Showing \", usersWithClassRank.length, \" of \", users.length, \" users\", searchTerm && ` matching \"${searchTerm}\"`, filterType !== 'all' && ` (${filterType} only)`]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-7 h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent\",\n                children: \"Leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: \"Top performers across all levels\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 29\n          }, this), lastUpdated && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-blue-700 font-medium\",\n              children: [\"Updated \", new Date(lastUpdated).toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [onAutoRefreshToggle && /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: onAutoRefreshToggle,\n            className: `px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${autoRefresh ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`,\n            children: [autoRefresh ? /*#__PURE__*/_jsxDEV(TbPlayerPause, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 52\n            }, this) : /*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 92\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: autoRefresh ? 'Auto' : 'Manual'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 33\n          }, this), currentUserId && /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            onClick: scrollToCurrentUser,\n            className: \"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUser, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Find Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 sm:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-blue-700\",\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-blue-900 mb-1\",\n            children: totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-blue-600 font-medium\",\n            children: [activeUsers, \" active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-yellow-700\",\n              children: \"Premium Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-yellow-900 mb-1\",\n            children: premiumUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-yellow-600 font-medium\",\n            children: [totalUsers > 0 ? Math.round(premiumUsers / totalUsers * 100) : 0, \"% premium\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-green-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbUser, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-green-700\",\n              children: \"Top Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-green-900 mb-1\",\n            children: topScore.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-green-600 font-medium\",\n            children: \"ranking points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-purple-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-purple-700\",\n              children: \"Avg XP\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-purple-900 mb-1\",\n            children: averageXP.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: \"experience points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `animate-fadeInUp ${getLayoutClasses()}`,\n      children: usersWithClassRank.map((user, index) => {\n        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n        const rank = user.rank || index + 1;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: isCurrentUser ? userRef : null,\n          className: `animate-slideInLeft transition-all duration-300 ${isCurrentUser && findMeActive ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl' : isCurrentUser ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(UserRankingCard, {\n            user: user,\n            rank: rank,\n            classRank: user.classRank,\n            isCurrentUser: isCurrentUser,\n            layout: layout,\n            size: size,\n            showStats: showStats\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 33\n          }, this)\n        }, user.userId || user._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 25\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 13\n    }, this), usersWithClassRank.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12 bg-white rounded-xl border border-gray-200 animate-fadeInUp\",\n      children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-2\",\n        children: \"No users found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: users.length === 0 ? 'No ranking data available.' : 'Try adjusting your search or filter criteria.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 17\n    }, this), currentUserId && usersWithClassRank.length > 10 && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: scrollToCurrentUser,\n      className: \"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 hover:scale-110 active:scale-95 animate-bounce\",\n      title: \"Find me in ranking\",\n      children: /*#__PURE__*/_jsxDEV(TbUser, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 9\n  }, this);\n};\n_s(UserRankingList, \"leUq98Lm+jUc6TAm6eqSyBcgK5k=\");\n_c = UserRankingList;\nexport default UserRankingList;\nvar _c;\n$RefreshReg$(_c, \"UserRankingList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "TbUser", "TbUsers", "TbTrophy", "TbPlayerPlay", "TbPlayerPause", "TbClock", "TbSearch", "Tb<PERSON><PERSON>er", "UserRankingCard", "jsxDEV", "_jsxDEV", "UserRankingList", "users", "currentUserId", "layout", "size", "showStats", "className", "currentUserRef", "showFindMe", "lastUpdated", "autoRefresh", "onAutoRefreshToggle", "_s", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "sortBy", "setSortBy", "localShowFindMe", "setLocalShowFindMe", "localCurrentUserRef", "userRef", "findMeActive", "filteredUsers", "filter", "user", "_user$name", "_user$email", "_user$subscriptionSta", "matchesSearch", "name", "toLowerCase", "includes", "email", "userStatus", "subscriptionStatus", "matchesFilter", "sort", "a", "b", "totalXP", "localeCompare", "rank", "usersWithClassRank", "map", "sameClassUsers", "u", "class", "classRank", "findIndex", "_id", "userId", "currentUser", "find", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "setTimeout", "getLayoutClasses", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "totalUsers", "length", "premiumUsers", "normalizedSubscriptionStatus", "topScore", "Math", "max", "rankingScore", "totalPoints", "activeUsers", "totalQuizzesTaken", "averageXP", "round", "reduce", "sum", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "e", "target", "Date", "toLocaleTimeString", "motion", "button", "whileHover", "scale", "whileTap", "onClick", "toLocaleString", "index", "isCurrentUser", "ref", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingList.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { TbUser, TbUsers, Tb<PERSON>rophy, TbPlayerPlay, TbPlayerPause, Tb<PERSON>lock, TbSearch, TbFilter } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\n\nconst UserRankingList = ({\n    users = [],\n    currentUserId = null,\n    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'\n    size = 'medium',\n    showStats = true,\n    className = '',\n    currentUserRef = null,\n    showFindMe = false,\n    lastUpdated = null,\n    autoRefresh = false,\n    onAutoRefreshToggle = null\n}) => {\n    // State for search and filtering\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'\n    const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'\n    const [localShowFindMe, setLocalShowFindMe] = useState(false);\n    const localCurrentUserRef = useRef(null);\n\n    // Use passed refs or local ones\n    const userRef = currentUserRef || localCurrentUserRef;\n    const findMeActive = showFindMe || localShowFindMe;\n\n    // Filter and search users\n    const filteredUsers = users.filter(user => {\n        // Search filter\n        const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            user.email?.toLowerCase().includes(searchTerm.toLowerCase());\n\n        // Subscription filter\n        const userStatus = user.subscriptionStatus?.toLowerCase() || 'free';\n        let matchesFilter = true;\n\n        switch (filterType) {\n            case 'premium':\n                matchesFilter = userStatus === 'premium' || userStatus === 'active';\n                break;\n            case 'expired':\n                matchesFilter = userStatus === 'expired';\n                break;\n            case 'free':\n                matchesFilter = userStatus === 'free';\n                break;\n            default:\n                matchesFilter = true;\n        }\n\n        return matchesSearch && matchesFilter;\n    }).sort((a, b) => {\n        switch (sortBy) {\n            case 'xp':\n                return (b.totalXP || 0) - (a.totalXP || 0);\n            case 'name':\n                return (a.name || '').localeCompare(b.name || '');\n            default:\n                return (a.rank || 0) - (b.rank || 0);\n        }\n    });\n\n    // Calculate class ranks for filtered users\n    const usersWithClassRank = filteredUsers.map(user => {\n        // Group users by class and calculate class rank\n        const sameClassUsers = filteredUsers.filter(u => u.class === user.class);\n        const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n        return { ...user, classRank };\n    });\n\n    // Find current user\n    const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n\n    // Scroll to current user\n    const scrollToCurrentUser = () => {\n        if (userRef.current) {\n            userRef.current.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n            setLocalShowFindMe(true);\n            // Hide the highlight after 3 seconds\n            setTimeout(() => setLocalShowFindMe(false), 3000);\n        }\n    };\n\n    // Get layout classes\n    const getLayoutClasses = () => {\n        switch (layout) {\n            case 'vertical':\n                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n            case 'grid':\n                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n            case 'horizontal':\n            default:\n                return 'space-y-3';\n        }\n    };\n\n    // Container animation variants\n    const containerVariants = {\n        hidden: { opacity: 0 },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n\n    // Stats summary with enhanced calculations\n    const totalUsers = users.length;\n    const premiumUsers = users.filter(u =>\n        u.subscriptionStatus === 'active' ||\n        u.subscriptionStatus === 'premium' ||\n        u.normalizedSubscriptionStatus === 'premium'\n    ).length;\n\n    // Use ranking score or XP as the primary metric\n    const topScore = users.length > 0 ? Math.max(...users.map(u =>\n        u.rankingScore || u.totalXP || u.totalPoints || 0\n    )) : 0;\n\n    // Calculate additional stats\n    const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n    const averageXP = users.length > 0 ?\n        Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n\n    return (\n        <div className={`space-y-6 ${className}`}>\n            {/* Header with Stats */}\n            {showStats && (\n                <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200 animate-fadeInUp\">\n                    {/* Search and Filter Section */}\n                    <div className=\"mb-6 bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-white/50\">\n                        <div className=\"flex flex-col sm:flex-row gap-4\">\n                            {/* Search Input */}\n                            <div className=\"flex-1 relative\">\n                                <TbSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                                <input\n                                    type=\"text\"\n                                    placeholder=\"Search users by name or email...\"\n                                    value={searchTerm}\n                                    onChange={(e) => setSearchTerm(e.target.value)}\n                                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                                />\n                            </div>\n\n                            {/* Filter Dropdown */}\n                            <div className=\"relative\">\n                                <TbFilter className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                                <select\n                                    value={filterType}\n                                    onChange={(e) => setFilterType(e.target.value)}\n                                    className=\"pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white\"\n                                >\n                                    <option value=\"all\">All Users</option>\n                                    <option value=\"premium\">Premium</option>\n                                    <option value=\"free\">Free</option>\n                                    <option value=\"expired\">Expired</option>\n                                </select>\n                            </div>\n\n                            {/* Sort Dropdown */}\n                            <select\n                                value={sortBy}\n                                onChange={(e) => setSortBy(e.target.value)}\n                                className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white\"\n                            >\n                                <option value=\"rank\">Sort by Rank</option>\n                                <option value=\"xp\">Sort by XP</option>\n                                <option value=\"name\">Sort by Name</option>\n                            </select>\n                        </div>\n\n                        {/* Results Count */}\n                        <div className=\"mt-3 text-sm text-gray-600\">\n                            Showing {usersWithClassRank.length} of {users.length} users\n                            {searchTerm && ` matching \"${searchTerm}\"`}\n                            {filterType !== 'all' && ` (${filterType} only)`}\n                        </div>\n                    </div>\n                    <div className=\"flex items-center justify-between mb-6\">\n                        <div className=\"flex items-center space-x-4\">\n                            <div className=\"flex items-center space-x-3\">\n                                <div className=\"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg\">\n                                    <TbTrophy className=\"w-7 h-7 text-white\" />\n                                </div>\n                                <div>\n                                    <h2 className=\"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent\">\n                                        Leaderboard\n                                    </h2>\n                                    <p className=\"text-sm text-gray-600 font-medium\">Top performers across all levels</p>\n                                </div>\n                            </div>\n\n                            {lastUpdated && (\n                                <div className=\"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200\">\n                                    <TbClock className=\"w-4 h-4 text-blue-600\" />\n                                    <span className=\"text-sm text-blue-700 font-medium\">\n                                        Updated {new Date(lastUpdated).toLocaleTimeString()}\n                                    </span>\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"flex items-center space-x-2\">\n                            {/* Auto-refresh toggle */}\n                            {onAutoRefreshToggle && (\n                                <motion.button\n                                    whileHover={{ scale: 1.05 }}\n                                    whileTap={{ scale: 0.95 }}\n                                    onClick={onAutoRefreshToggle}\n                                    className={`px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${\n                                        autoRefresh\n                                            ? 'bg-green-500 hover:bg-green-600 text-white'\n                                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'\n                                    }`}\n                                >\n                                    {autoRefresh ? <TbPlayerPause className=\"w-4 h-4\" /> : <TbPlayerPlay className=\"w-4 h-4\" />}\n                                    <span className=\"hidden sm:inline\">\n                                        {autoRefresh ? 'Auto' : 'Manual'}\n                                    </span>\n                                </motion.button>\n                            )}\n\n\n\n                            {/* Find Me button */}\n                            {currentUserId && (\n                                <motion.button\n                                    whileHover={{ scale: 1.05 }}\n                                    whileTap={{ scale: 0.95 }}\n                                    onClick={scrollToCurrentUser}\n                                    className=\"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\"\n                                >\n                                    <TbUser className=\"w-4 h-4\" />\n                                    <span className=\"hidden sm:inline\">Find Me</span>\n                                </motion.button>\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4\">\n                        <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-blue-500 rounded-lg\">\n                                    <TbUsers className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-blue-700\">Total Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-blue-900 mb-1\">{totalUsers}</div>\n                            <div className=\"text-xs text-blue-600 font-medium\">{activeUsers} active</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-yellow-700\">Premium Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-yellow-900 mb-1\">{premiumUsers}</div>\n                            <div className=\"text-xs text-yellow-600 font-medium\">\n                                {totalUsers > 0 ? Math.round((premiumUsers / totalUsers) * 100) : 0}% premium\n                            </div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-green-500 rounded-lg\">\n                                    <TbUser className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-green-700\">Top Score</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-green-900 mb-1\">{topScore.toLocaleString()}</div>\n                            <div className=\"text-xs text-green-600 font-medium\">ranking points</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-purple-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-purple-700\">Avg XP</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-purple-900 mb-1\">{averageXP.toLocaleString()}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">experience points</div>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* User List */}\n            <div className={`animate-fadeInUp ${getLayoutClasses()}`}>\n                {usersWithClassRank.map((user, index) => {\n                    const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n                    const rank = user.rank || index + 1;\n\n                    return (\n                        <div\n                            key={user.userId || user._id}\n                            ref={isCurrentUser ? userRef : null}\n                            className={`animate-slideInLeft transition-all duration-300 ${\n                                isCurrentUser && findMeActive\n                                    ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl'\n                                    : isCurrentUser\n                                    ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg'\n                                        : ''\n                                }`}\n                            >\n                                <UserRankingCard\n                                    user={user}\n                                    rank={rank}\n                                    classRank={user.classRank}\n                                    isCurrentUser={isCurrentUser}\n                                    layout={layout}\n                                    size={size}\n                                    showStats={showStats}\n                                />\n                            </div>\n                        );\n                    })}\n            </div>\n\n            {/* Empty State */}\n            {usersWithClassRank.length === 0 && (\n                <div className=\"text-center py-12 bg-white rounded-xl border border-gray-200 animate-fadeInUp\">\n                    <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No users found</h3>\n                    <p className=\"text-gray-500\">\n                        {users.length === 0 ? 'No ranking data available.' : 'Try adjusting your search or filter criteria.'}\n                    </p>\n                </div>\n            )}\n\n            {/* Floating Action Button for Current User */}\n            {currentUserId && usersWithClassRank.length > 10 && (\n                <button\n                    onClick={scrollToCurrentUser}\n                    className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 hover:scale-110 active:scale-95 animate-bounce\"\n                    title=\"Find me in ranking\"\n                >\n                    <TbUser className=\"w-6 h-6\" />\n                </button>\n            )}\n        </div>\n    );\n};\n\nexport default UserRankingList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACpH,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,eAAe,GAAGA,CAAC;EACrBC,KAAK,GAAG,EAAE;EACVC,aAAa,GAAG,IAAI;EACpBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG,EAAE;EACdC,cAAc,GAAG,IAAI;EACrBC,UAAU,GAAG,KAAK;EAClBC,WAAW,GAAG,IAAI;EAClBC,WAAW,GAAG,KAAK;EACnBC,mBAAmB,GAAG;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMkC,mBAAmB,GAAGjC,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMkC,OAAO,GAAGf,cAAc,IAAIc,mBAAmB;EACrD,MAAME,YAAY,GAAGf,UAAU,IAAIW,eAAe;;EAElD;EACA,MAAMK,aAAa,GAAGvB,KAAK,CAACwB,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,qBAAA;IACvC;IACA,MAAMC,aAAa,GAAG,EAAAH,UAAA,GAAAD,IAAI,CAACK,IAAI,cAAAJ,UAAA,uBAATA,UAAA,CAAWK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,UAAU,CAACmB,WAAW,CAAC,CAAC,CAAC,OAAAJ,WAAA,GAC7DF,IAAI,CAACQ,KAAK,cAAAN,WAAA,uBAAVA,WAAA,CAAYI,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpB,UAAU,CAACmB,WAAW,CAAC,CAAC,CAAC;;IAEhF;IACA,MAAMG,UAAU,GAAG,EAAAN,qBAAA,GAAAH,IAAI,CAACU,kBAAkB,cAAAP,qBAAA,uBAAvBA,qBAAA,CAAyBG,WAAW,CAAC,CAAC,KAAI,MAAM;IACnE,IAAIK,aAAa,GAAG,IAAI;IAExB,QAAQtB,UAAU;MACd,KAAK,SAAS;QACVsB,aAAa,GAAGF,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,QAAQ;QACnE;MACJ,KAAK,SAAS;QACVE,aAAa,GAAGF,UAAU,KAAK,SAAS;QACxC;MACJ,KAAK,MAAM;QACPE,aAAa,GAAGF,UAAU,KAAK,MAAM;QACrC;MACJ;QACIE,aAAa,GAAG,IAAI;IAC5B;IAEA,OAAOP,aAAa,IAAIO,aAAa;EACzC,CAAC,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACd,QAAQvB,MAAM;MACV,KAAK,IAAI;QACL,OAAO,CAACuB,CAAC,CAACC,OAAO,IAAI,CAAC,KAAKF,CAAC,CAACE,OAAO,IAAI,CAAC,CAAC;MAC9C,KAAK,MAAM;QACP,OAAO,CAACF,CAAC,CAACR,IAAI,IAAI,EAAE,EAAEW,aAAa,CAACF,CAAC,CAACT,IAAI,IAAI,EAAE,CAAC;MACrD;QACI,OAAO,CAACQ,CAAC,CAACI,IAAI,IAAI,CAAC,KAAKH,CAAC,CAACG,IAAI,IAAI,CAAC,CAAC;IAC5C;EACJ,CAAC,CAAC;;EAEF;EACA,MAAMC,kBAAkB,GAAGpB,aAAa,CAACqB,GAAG,CAACnB,IAAI,IAAI;IACjD;IACA,MAAMoB,cAAc,GAAGtB,aAAa,CAACC,MAAM,CAACsB,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKtB,IAAI,CAACsB,KAAK,CAAC;IACxE,MAAMC,SAAS,GAAGH,cAAc,CAACI,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,GAAG,KAAKzB,IAAI,CAACyB,GAAG,IAAIJ,CAAC,CAACK,MAAM,KAAK1B,IAAI,CAAC0B,MAAM,CAAC,GAAG,CAAC;IACnG,OAAO;MAAE,GAAG1B,IAAI;MAAEuB;IAAU,CAAC;EACjC,CAAC,CAAC;;EAEF;EACA,MAAMI,WAAW,GAAGT,kBAAkB,CAACU,IAAI,CAAC5B,IAAI,IAAIA,IAAI,CAACyB,GAAG,KAAKjD,aAAa,IAAIwB,IAAI,CAAC0B,MAAM,KAAKlD,aAAa,CAAC;;EAEhH;EACA,MAAMqD,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIjC,OAAO,CAACkC,OAAO,EAAE;MACjBlC,OAAO,CAACkC,OAAO,CAACC,cAAc,CAAC;QAC3BC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACFvC,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACAwC,UAAU,CAAC,MAAMxC,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACrD;EACJ,CAAC;;EAED;EACA,MAAMyC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,QAAQ1D,MAAM;MACV,KAAK,UAAU;QACX,OAAO,qEAAqE;MAChF,KAAK,MAAM;QACP,OAAO,sDAAsD;MACjE,KAAK,YAAY;MACjB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;;EAED;EACA,MAAM2D,iBAAiB,GAAG;IACtBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACLD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACRC,eAAe,EAAE;MACrB;IACJ;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAGnE,KAAK,CAACoE,MAAM;EAC/B,MAAMC,YAAY,GAAGrE,KAAK,CAACwB,MAAM,CAACsB,CAAC,IAC/BA,CAAC,CAACX,kBAAkB,KAAK,QAAQ,IACjCW,CAAC,CAACX,kBAAkB,KAAK,SAAS,IAClCW,CAAC,CAACwB,4BAA4B,KAAK,SACvC,CAAC,CAACF,MAAM;;EAER;EACA,MAAMG,QAAQ,GAAGvE,KAAK,CAACoE,MAAM,GAAG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAGzE,KAAK,CAAC4C,GAAG,CAACE,CAAC,IACvDA,CAAC,CAAC4B,YAAY,IAAI5B,CAAC,CAACN,OAAO,IAAIM,CAAC,CAAC6B,WAAW,IAAI,CACpD,CAAC,CAAC,GAAG,CAAC;;EAEN;EACA,MAAMC,WAAW,GAAG5E,KAAK,CAACwB,MAAM,CAACsB,CAAC,IAAI,CAACA,CAAC,CAAC+B,iBAAiB,IAAI,CAAC,IAAI,CAAC,CAAC,CAACT,MAAM;EAC5E,MAAMU,SAAS,GAAG9E,KAAK,CAACoE,MAAM,GAAG,CAAC,GAC9BI,IAAI,CAACO,KAAK,CAAC/E,KAAK,CAACgF,MAAM,CAAC,CAACC,GAAG,EAAEnC,CAAC,KAAKmC,GAAG,IAAInC,CAAC,CAACN,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGxC,KAAK,CAACoE,MAAM,CAAC,GAAG,CAAC;EAEtF,oBACItE,OAAA;IAAKO,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAA6E,QAAA,GAEpC9E,SAAS,iBACNN,OAAA;MAAKO,SAAS,EAAC,mGAAmG;MAAA6E,QAAA,gBAE9GpF,OAAA;QAAKO,SAAS,EAAC,yEAAyE;QAAA6E,QAAA,gBACpFpF,OAAA;UAAKO,SAAS,EAAC,iCAAiC;UAAA6E,QAAA,gBAE5CpF,OAAA;YAAKO,SAAS,EAAC,iBAAiB;YAAA6E,QAAA,gBAC5BpF,OAAA,CAACJ,QAAQ;cAACW,SAAS,EAAC;YAA0E;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjGxF,OAAA;cACIyF,IAAI,EAAC,MAAM;cACXC,WAAW,EAAC,kCAAkC;cAC9CC,KAAK,EAAE7E,UAAW;cAClB8E,QAAQ,EAAGC,CAAC,IAAK9E,aAAa,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CpF,SAAS,EAAC;YAAgJ;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7J,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNxF,OAAA;YAAKO,SAAS,EAAC,UAAU;YAAA6E,QAAA,gBACrBpF,OAAA,CAACH,QAAQ;cAACU,SAAS,EAAC;YAA0E;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjGxF,OAAA;cACI2F,KAAK,EAAE3E,UAAW;cAClB4E,QAAQ,EAAGC,CAAC,IAAK5E,aAAa,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/CpF,SAAS,EAAC,kJAAkJ;cAAA6E,QAAA,gBAE5JpF,OAAA;gBAAQ2F,KAAK,EAAC,KAAK;gBAAAP,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtCxF,OAAA;gBAAQ2F,KAAK,EAAC,SAAS;gBAAAP,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCxF,OAAA;gBAAQ2F,KAAK,EAAC,MAAM;gBAAAP,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCxF,OAAA;gBAAQ2F,KAAK,EAAC,SAAS;gBAAAP,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGNxF,OAAA;YACI2F,KAAK,EAAEzE,MAAO;YACd0E,QAAQ,EAAGC,CAAC,IAAK1E,SAAS,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAC3CpF,SAAS,EAAC,4IAA4I;YAAA6E,QAAA,gBAEtJpF,OAAA;cAAQ2F,KAAK,EAAC,MAAM;cAAAP,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1CxF,OAAA;cAAQ2F,KAAK,EAAC,IAAI;cAAAP,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCxF,OAAA;cAAQ2F,KAAK,EAAC,MAAM;cAAAP,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAGNxF,OAAA;UAAKO,SAAS,EAAC,4BAA4B;UAAA6E,QAAA,GAAC,UAChC,EAACvC,kBAAkB,CAACyB,MAAM,EAAC,MAAI,EAACpE,KAAK,CAACoE,MAAM,EAAC,QACrD,EAACxD,UAAU,IAAK,cAAaA,UAAW,GAAE,EACzCE,UAAU,KAAK,KAAK,IAAK,KAAIA,UAAW,QAAO;QAAA;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNxF,OAAA;QAAKO,SAAS,EAAC,wCAAwC;QAAA6E,QAAA,gBACnDpF,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAA6E,QAAA,gBACxCpF,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA6E,QAAA,gBACxCpF,OAAA;cAAKO,SAAS,EAAC,yFAAyF;cAAA6E,QAAA,eACpGpF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNxF,OAAA;cAAAoF,QAAA,gBACIpF,OAAA;gBAAIO,SAAS,EAAC,2HAA2H;gBAAA6E,QAAA,EAAC;cAE1I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxF,OAAA;gBAAGO,SAAS,EAAC,mCAAmC;gBAAA6E,QAAA,EAAC;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAEL9E,WAAW,iBACRV,OAAA;YAAKO,SAAS,EAAC,oFAAoF;YAAA6E,QAAA,gBAC/FpF,OAAA,CAACL,OAAO;cAACY,SAAS,EAAC;YAAuB;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CxF,OAAA;cAAMO,SAAS,EAAC,mCAAmC;cAAA6E,QAAA,GAAC,UACxC,EAAC,IAAIW,IAAI,CAACrF,WAAW,CAAC,CAACsF,kBAAkB,CAAC,CAAC;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAENxF,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAA6E,QAAA,GAEvCxE,mBAAmB,iBAChBZ,OAAA,CAACiG,MAAM,CAACC,MAAM;YACVC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BE,OAAO,EAAE1F,mBAAoB;YAC7BL,SAAS,EAAG,+FACRI,WAAW,GACL,4CAA4C,GAC5C,6CACT,EAAE;YAAAyE,QAAA,GAEFzE,WAAW,gBAAGX,OAAA,CAACN,aAAa;cAACa,SAAS,EAAC;YAAS;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGxF,OAAA,CAACP,YAAY;cAACc,SAAS,EAAC;YAAS;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3FxF,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAA6E,QAAA,EAC7BzE,WAAW,GAAG,MAAM,GAAG;YAAQ;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAClB,EAKArF,aAAa,iBACVH,OAAA,CAACiG,MAAM,CAACC,MAAM;YACVC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BE,OAAO,EAAE9C,mBAAoB;YAC7BjD,SAAS,EAAC,0IAA0I;YAAA6E,QAAA,gBAEpJpF,OAAA,CAACV,MAAM;cAACiB,SAAS,EAAC;YAAS;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9BxF,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAA6E,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAClB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENxF,OAAA;QAAKO,SAAS,EAAC,uCAAuC;QAAA6E,QAAA,gBAClDpF,OAAA;UAAKO,SAAS,EAAC,yJAAyJ;UAAA6E,QAAA,gBACpKpF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,gBAC7CpF,OAAA;cAAKO,SAAS,EAAC,4BAA4B;cAAA6E,QAAA,eACvCpF,OAAA,CAACT,OAAO;gBAACgB,SAAS,EAAC;cAAoB;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNxF,OAAA;cAAMO,SAAS,EAAC,qCAAqC;cAAA6E,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNxF,OAAA;YAAKO,SAAS,EAAC,wCAAwC;YAAA6E,QAAA,EAAEf;UAAU;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1ExF,OAAA;YAAKO,SAAS,EAAC,mCAAmC;YAAA6E,QAAA,GAAEN,WAAW,EAAC,SAAO;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAENxF,OAAA;UAAKO,SAAS,EAAC,6JAA6J;UAAA6E,QAAA,gBACxKpF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,gBAC7CpF,OAAA;cAAKO,SAAS,EAAC,+DAA+D;cAAA6E,QAAA,eAC1EpF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNxF,OAAA;cAAMO,SAAS,EAAC,uCAAuC;cAAA6E,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACNxF,OAAA;YAAKO,SAAS,EAAC,0CAA0C;YAAA6E,QAAA,EAAEb;UAAY;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9ExF,OAAA;YAAKO,SAAS,EAAC,qCAAqC;YAAA6E,QAAA,GAC/Cf,UAAU,GAAG,CAAC,GAAGK,IAAI,CAACO,KAAK,CAAEV,YAAY,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,WACxE;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENxF,OAAA;UAAKO,SAAS,EAAC,4JAA4J;UAAA6E,QAAA,gBACvKpF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,gBAC7CpF,OAAA;cAAKO,SAAS,EAAC,6BAA6B;cAAA6E,QAAA,eACxCpF,OAAA,CAACV,MAAM;gBAACiB,SAAS,EAAC;cAAoB;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACNxF,OAAA;cAAMO,SAAS,EAAC,sCAAsC;cAAA6E,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACNxF,OAAA;YAAKO,SAAS,EAAC,yCAAyC;YAAA6E,QAAA,EAAEX,QAAQ,CAAC8B,cAAc,CAAC;UAAC;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1FxF,OAAA;YAAKO,SAAS,EAAC,oCAAoC;YAAA6E,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAENxF,OAAA;UAAKO,SAAS,EAAC,2JAA2J;UAAA6E,QAAA,gBACtKpF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAA6E,QAAA,gBAC7CpF,OAAA;cAAKO,SAAS,EAAC,8BAA8B;cAAA6E,QAAA,eACzCpF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNxF,OAAA;cAAMO,SAAS,EAAC,uCAAuC;cAAA6E,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACNxF,OAAA;YAAKO,SAAS,EAAC,0CAA0C;YAAA6E,QAAA,EAAEJ,SAAS,CAACuB,cAAc,CAAC;UAAC;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5FxF,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA6E,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAGDxF,OAAA;MAAKO,SAAS,EAAG,oBAAmBuD,gBAAgB,CAAC,CAAE,EAAE;MAAAsB,QAAA,EACpDvC,kBAAkB,CAACC,GAAG,CAAC,CAACnB,IAAI,EAAE6E,KAAK,KAAK;QACrC,MAAMC,aAAa,GAAG9E,IAAI,CAAC0B,MAAM,KAAKlD,aAAa,IAAIwB,IAAI,CAACyB,GAAG,KAAKjD,aAAa;QACjF,MAAMyC,IAAI,GAAGjB,IAAI,CAACiB,IAAI,IAAI4D,KAAK,GAAG,CAAC;QAEnC,oBACIxG,OAAA;UAEI0G,GAAG,EAAED,aAAa,GAAGlF,OAAO,GAAG,IAAK;UACpChB,SAAS,EAAG,mDACRkG,aAAa,IAAIjF,YAAY,GACvB,6GAA6G,GAC7GiF,aAAa,GACb,+CAA+C,GAC3C,EACT,EAAE;UAAArB,QAAA,eAEHpF,OAAA,CAACF,eAAe;YACZ6B,IAAI,EAAEA,IAAK;YACXiB,IAAI,EAAEA,IAAK;YACXM,SAAS,EAAEvB,IAAI,CAACuB,SAAU;YAC1BuD,aAAa,EAAEA,aAAc;YAC7BrG,MAAM,EAAEA,MAAO;YACfC,IAAI,EAAEA,IAAK;YACXC,SAAS,EAAEA;UAAU;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC,GAlBD7D,IAAI,CAAC0B,MAAM,IAAI1B,IAAI,CAACyB,GAAG;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBvB,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL3C,kBAAkB,CAACyB,MAAM,KAAK,CAAC,iBAC5BtE,OAAA;MAAKO,SAAS,EAAC,+EAA+E;MAAA6E,QAAA,gBAC1FpF,OAAA,CAACT,OAAO;QAACgB,SAAS,EAAC;MAAsC;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5DxF,OAAA;QAAIO,SAAS,EAAC,0CAA0C;QAAA6E,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5ExF,OAAA;QAAGO,SAAS,EAAC,eAAe;QAAA6E,QAAA,EACvBlF,KAAK,CAACoE,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;MAA+C;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,EAGArF,aAAa,IAAI0C,kBAAkB,CAACyB,MAAM,GAAG,EAAE,iBAC5CtE,OAAA;MACIsG,OAAO,EAAE9C,mBAAoB;MAC7BjD,SAAS,EAAC,4LAA4L;MACtMoG,KAAK,EAAC,oBAAoB;MAAAvB,QAAA,eAE1BpF,OAAA,CAACV,MAAM;QAACiB,SAAS,EAAC;MAAS;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC3E,EAAA,CA1VIZ,eAAe;AAAA2G,EAAA,GAAf3G,eAAe;AA4VrB,eAAeA,eAAe;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}