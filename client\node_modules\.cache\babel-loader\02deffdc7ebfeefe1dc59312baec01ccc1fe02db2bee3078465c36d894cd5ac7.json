{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingCard.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { TbTrophy, TbMedal, TbCrown, TbStar, TbFlame, TbBolt } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport XPProgressBar from './XPProgressBar';\nimport LevelBadge from './LevelBadge';\nimport EnhancedAchievementBadge from './EnhancedAchievementBadge';\nimport './UserRankingCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingCard = ({\n  user,\n  rank,\n  classRank,\n  isCurrentUser = false,\n  layout = 'horizontal',\n  // 'horizontal' or 'vertical'\n  size = 'medium',\n  // 'small', 'medium', 'large'\n  showStats = true,\n  className = ''\n}) => {\n  // Size configurations - Optimized profile circle sizes for better visibility\n  const sizeConfig = {\n    small: {\n      avatar: 'w-12 h-12',\n      text: 'text-sm',\n      subtext: 'text-xs',\n      padding: 'p-3',\n      spacing: 'space-x-3'\n    },\n    medium: {\n      avatar: 'w-14 h-14',\n      text: 'text-base',\n      subtext: 'text-sm',\n      padding: 'p-4',\n      spacing: 'space-x-4'\n    },\n    large: {\n      avatar: 'w-16 h-16',\n      text: 'text-lg',\n      subtext: 'text-base',\n      padding: 'p-5',\n      spacing: 'space-x-5'\n    }\n  };\n  const config = sizeConfig[size];\n\n  // Get subscription status styling with improved status detection\n  const getSubscriptionStyling = () => {\n    const subscriptionStatus = (user === null || user === void 0 ? void 0 : user.subscriptionStatus) || (user === null || user === void 0 ? void 0 : user.normalizedSubscriptionStatus) || 'free';\n\n    // Normalize status for better handling\n    const normalizedStatus = subscriptionStatus.toLowerCase();\n    if (normalizedStatus === 'active' || normalizedStatus === 'premium') {\n      return {\n        avatarClass: 'avatar-premium premium-glow',\n        badge: 'status-premium',\n        glow: 'shadow-lg shadow-yellow-400/50',\n        statusText: 'Premium',\n        borderClass: 'ring-2 ring-yellow-400',\n        bgClass: 'bg-gradient-to-r from-yellow-400 to-orange-400 text-white'\n      };\n    } else if (normalizedStatus === 'free') {\n      return {\n        avatarClass: 'avatar-free',\n        badge: 'status-free',\n        glow: 'shadow-md shadow-blue-400/30',\n        statusText: 'Free',\n        borderClass: 'ring-2 ring-blue-400',\n        bgClass: 'bg-blue-100 text-blue-700'\n      };\n    } else {\n      return {\n        avatarClass: 'avatar-expired',\n        badge: 'status-expired',\n        glow: 'shadow-sm',\n        statusText: 'Expired',\n        borderClass: 'ring-2 ring-red-400',\n        bgClass: 'bg-red-100 text-red-700'\n      };\n    }\n  };\n  const styling = getSubscriptionStyling();\n\n  // Get rank icon and color\n  const getRankDisplay = () => {\n    if (rank === 1) {\n      return {\n        icon: TbCrown,\n        color: 'text-yellow-500',\n        bg: 'bg-yellow-50'\n      };\n    } else if (rank === 2) {\n      return {\n        icon: TbMedal,\n        color: 'text-gray-400',\n        bg: 'bg-gray-50'\n      };\n    } else if (rank === 3) {\n      return {\n        icon: TbTrophy,\n        color: 'text-amber-600',\n        bg: 'bg-amber-50'\n      };\n    } else if (rank <= 10) {\n      return {\n        icon: TbStar,\n        color: 'text-blue-500',\n        bg: 'bg-blue-50'\n      };\n    } else {\n      return {\n        icon: null,\n        color: 'text-gray-500',\n        bg: 'bg-gray-50'\n      };\n    }\n  };\n  const rankDisplay = getRankDisplay();\n  const RankIcon = rankDisplay.icon;\n\n  // Animation variants\n  const cardVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.3\n      }\n    },\n    hover: {\n      scale: 1.02,\n      transition: {\n        duration: 0.2\n      }\n    }\n  };\n  const avatarVariants = {\n    hover: {\n      scale: 1.1,\n      transition: {\n        duration: 0.2\n      }\n    }\n  };\n\n  // Avatar wrapper with subscription styling\n  const StyledAvatar = ({\n    children\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${config.avatar} ${styling.avatarClass} ${styling.glow}`,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 13\n    }, this);\n  };\n  if (layout === 'vertical') {\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: cardVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      whileHover: \"hover\",\n      className: `\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    bg-white rounded-xl border border-gray-200\n                    ${isCurrentUser ? 'current-user-card' : ''}\n                    ${className}\n                `,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ${rankDisplay.bg} ${rankDisplay.color}\n                    `,\n          children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold\",\n            children: [\"#\", rank]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 21\n        }, this), classRank && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold\",\n            children: [\"C\", classRank]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: avatarVariants,\n        whileHover: \"hover\",\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                        ${styling.avatarClass} ${styling.borderClass}\n                    `,\n          children: user !== null && user !== void 0 && user.profilePicture || user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: user.profilePicture || user.profileImage,\n            alt: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n            className: \"w-full h-full object-cover object-center\",\n            style: {\n              objectFit: 'cover'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-bold text-white\",\n              children: ((user === null || user === void 0 ? void 0 : user.name) || 'U').charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate max-w-24`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(LevelBadge, {\n            level: (user === null || user === void 0 ? void 0 : user.currentLevel) || 1,\n            size: \"small\",\n            showTitle: false,\n            animated: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: `${config.subtext} text-blue-600 font-medium`,\n              children: [(user === null || user === void 0 ? void 0 : user.totalXP) || 0, \" XP\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 29\n            }, this), (user === null || user === void 0 ? void 0 : user.xpToNextLevel) > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs text-gray-400`,\n              children: [user.xpToNextLevel, \" to next\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs text-gray-400`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" pts (legacy)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 21\n        }, this), (user === null || user === void 0 ? void 0 : user.averageScore) && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `${config.subtext} text-gray-500`,\n          children: [\"Avg: \", user.averageScore, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.currentStreak) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.achievements) && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [user.achievements.slice(0, 3).map((achievement, index) => /*#__PURE__*/_jsxDEV(EnhancedAchievementBadge, {\n            achievement: achievement,\n            size: \"small\",\n            showTooltip: true,\n            animated: true,\n            showXP: false\n          }, achievement.id || achievement.type || index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 33\n          }, this)), user.achievements.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500 ml-1\",\n            children: [\"+\", user.achievements.length - 3]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `\n                        inline-block px-2 py-1 rounded-full text-xs font-medium\n                        ${styling.bgClass}\n                    `,\n          children: styling.statusText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Horizontal layout (default)\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    variants: cardVariants,\n    initial: \"hidden\",\n    animate: \"visible\",\n    whileHover: \"hover\",\n    className: `\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200\n                ${isCurrentUser ? 'current-user-card' : ''}\n                ${className}\n            `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 flex-shrink-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `,\n        children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-bold\",\n          children: [\"#\", rank]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 17\n      }, this), classRank && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-bold\",\n          children: [\"C\", classRank]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: avatarVariants,\n      whileHover: \"hover\",\n      className: \"flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                    ${styling.avatarClass} ${styling.borderClass}\n                `,\n        children: user !== null && user !== void 0 && user.profilePicture || user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: user.profilePicture || user.profileImage,\n          alt: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n          className: \"w-full h-full object-cover object-center\",\n          style: {\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold text-white\",\n            children: ((user === null || user === void 0 ? void 0 : user.name) || 'U').charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(LevelBadge, {\n          level: (user === null || user === void 0 ? void 0 : user.currentLevel) || 1,\n          size: \"small\",\n          showTitle: false,\n          animated: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.bgClass}\n                    `,\n          children: styling.statusText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 17\n      }, this), showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbBolt, {\n            className: \"w-3 h-3 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-blue-600 font-medium`,\n            children: [(user === null || user === void 0 ? void 0 : user.totalXP) || 0, \" XP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-xs text-gray-400`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" pts\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.passedExamsCount) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-green-600`,\n          children: [user.passedExamsCount, \" passed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.quizzesTaken) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-blue-600`,\n          children: [user.quizzesTaken, \" quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 373,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.averageScore) && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-gray-600`,\n          children: [user.averageScore, \"% avg\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.currentStreak) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 21\n      }, this), (user === null || user === void 0 ? void 0 : user.xpToNextLevel) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(XPProgressBar, {\n          currentXP: user.totalXP || 0,\n          totalXP: (user.totalXP || 0) + (user.xpToNextLevel || 0),\n          currentLevel: user.currentLevel || 1,\n          xpToNextLevel: user.xpToNextLevel || 0,\n          size: \"small\",\n          showLevel: false,\n          showXPNumbers: false,\n          showAnimation: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 21\n      }, this), (user === null || user === void 0 ? void 0 : user.achievements) && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(AchievementList, {\n          achievements: user.achievements,\n          maxDisplay: 5,\n          size: \"small\",\n          layout: \"horizontal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 412,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 411,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-right flex-shrink-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`,\n        children: (user.rankingScore || user.score || user.totalXP || user.totalPoints || 0).toLocaleString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${config.subtext} text-gray-500`,\n        children: user.rankingScore ? 'ranking pts' : user.totalXP ? 'XP' : 'points'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 17\n      }, this), user.breakdown && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-400 mt-1\",\n        children: [\"XP: \", (user.totalXP || 0).toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 431,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 423,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 9\n  }, this);\n};\n_c = UserRankingCard;\nexport default UserRankingCard;\nvar _c;\n$RefreshReg$(_c, \"UserRankingCard\");", "map": {"version": 3, "names": ["React", "motion", "TbTrophy", "TbMedal", "TbCrown", "TbStar", "TbFlame", "TbBolt", "AchievementList", "XPProgressBar", "LevelBadge", "EnhancedAchievementBadge", "jsxDEV", "_jsxDEV", "UserRankingCard", "user", "rank", "classRank", "isCurrentUser", "layout", "size", "showStats", "className", "sizeConfig", "small", "avatar", "text", "subtext", "padding", "spacing", "medium", "large", "config", "getSubscriptionStyling", "subscriptionStatus", "normalizedSubscriptionStatus", "normalizedStatus", "toLowerCase", "avatarClass", "badge", "glow", "statusText", "borderClass", "bgClass", "styling", "getRankDisplay", "icon", "color", "bg", "rankDisplay", "RankIcon", "cardVariants", "hidden", "opacity", "y", "visible", "transition", "duration", "hover", "scale", "avatar<PERSON><PERSON><PERSON>", "Styled<PERSON>vatar", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "variants", "initial", "animate", "whileHover", "profilePicture", "profileImage", "src", "alt", "name", "style", "objectFit", "char<PERSON>t", "toUpperCase", "level", "currentLevel", "showTitle", "animated", "totalXP", "xpToNextLevel", "totalPoints", "averageScore", "currentStreak", "achievements", "length", "slice", "map", "achievement", "index", "showTooltip", "showXP", "id", "type", "passedExamsCount", "undefined", "quizzesTaken", "currentXP", "showLevel", "showXPNumbers", "showAnimation", "maxDisplay", "rankingScore", "score", "toLocaleString", "breakdown", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingCard.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>rophy, TbMedal, TbCrown, TbStar, TbFlame, TbBolt } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport XPProgressBar from './XPProgressBar';\nimport LevelBadge from './LevelBadge';\nimport EnhancedAchievementBadge from './EnhancedAchievementBadge';\nimport './UserRankingCard.css';\n\nconst UserRankingCard = ({\n    user,\n    rank,\n    classRank,\n    isCurrentUser = false,\n    layout = 'horizontal', // 'horizontal' or 'vertical'\n    size = 'medium', // 'small', 'medium', 'large'\n    showStats = true,\n    className = ''\n}) => {\n    // Size configurations - Optimized profile circle sizes for better visibility\n    const sizeConfig = {\n        small: {\n            avatar: 'w-12 h-12',\n            text: 'text-sm',\n            subtext: 'text-xs',\n            padding: 'p-3',\n            spacing: 'space-x-3'\n        },\n        medium: {\n            avatar: 'w-14 h-14',\n            text: 'text-base',\n            subtext: 'text-sm',\n            padding: 'p-4',\n            spacing: 'space-x-4'\n        },\n        large: {\n            avatar: 'w-16 h-16',\n            text: 'text-lg',\n            subtext: 'text-base',\n            padding: 'p-5',\n            spacing: 'space-x-5'\n        }\n    };\n\n    const config = sizeConfig[size];\n\n    // Get subscription status styling with improved status detection\n    const getSubscriptionStyling = () => {\n        const subscriptionStatus = user?.subscriptionStatus || user?.normalizedSubscriptionStatus || 'free';\n\n        // Normalize status for better handling\n        const normalizedStatus = subscriptionStatus.toLowerCase();\n\n        if (normalizedStatus === 'active' || normalizedStatus === 'premium') {\n            return {\n                avatarClass: 'avatar-premium premium-glow',\n                badge: 'status-premium',\n                glow: 'shadow-lg shadow-yellow-400/50',\n                statusText: 'Premium',\n                borderClass: 'ring-2 ring-yellow-400',\n                bgClass: 'bg-gradient-to-r from-yellow-400 to-orange-400 text-white'\n            };\n        } else if (normalizedStatus === 'free') {\n            return {\n                avatarClass: 'avatar-free',\n                badge: 'status-free',\n                glow: 'shadow-md shadow-blue-400/30',\n                statusText: 'Free',\n                borderClass: 'ring-2 ring-blue-400',\n                bgClass: 'bg-blue-100 text-blue-700'\n            };\n        } else {\n            return {\n                avatarClass: 'avatar-expired',\n                badge: 'status-expired',\n                glow: 'shadow-sm',\n                statusText: 'Expired',\n                borderClass: 'ring-2 ring-red-400',\n                bgClass: 'bg-red-100 text-red-700'\n            };\n        }\n    };\n\n    const styling = getSubscriptionStyling();\n\n    // Get rank icon and color\n    const getRankDisplay = () => {\n        if (rank === 1) {\n            return { icon: TbCrown, color: 'text-yellow-500', bg: 'bg-yellow-50' };\n        } else if (rank === 2) {\n            return { icon: TbMedal, color: 'text-gray-400', bg: 'bg-gray-50' };\n        } else if (rank === 3) {\n            return { icon: TbTrophy, color: 'text-amber-600', bg: 'bg-amber-50' };\n        } else if (rank <= 10) {\n            return { icon: TbStar, color: 'text-blue-500', bg: 'bg-blue-50' };\n        } else {\n            return { icon: null, color: 'text-gray-500', bg: 'bg-gray-50' };\n        }\n    };\n\n    const rankDisplay = getRankDisplay();\n    const RankIcon = rankDisplay.icon;\n\n    // Animation variants\n    const cardVariants = {\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n            opacity: 1, \n            y: 0,\n            transition: { duration: 0.3 }\n        },\n        hover: { \n            scale: 1.02,\n            transition: { duration: 0.2 }\n        }\n    };\n\n    const avatarVariants = {\n        hover: { \n            scale: 1.1,\n            transition: { duration: 0.2 }\n        }\n    };\n\n    // Avatar wrapper with subscription styling\n    const StyledAvatar = ({ children }) => {\n        return (\n            <div className={`${config.avatar} ${styling.avatarClass} ${styling.glow}`}>\n                {children}\n            </div>\n        );\n    };\n\n    if (layout === 'vertical') {\n        return (\n            <motion.div\n                variants={cardVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                whileHover=\"hover\"\n                className={`\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    bg-white rounded-xl border border-gray-200\n                    ${isCurrentUser ? 'current-user-card' : ''}\n                    ${className}\n                `}\n            >\n                {/* Rank Badges */}\n                <div className=\"flex items-center space-x-2 mb-3\">\n                    {/* Overall Rank */}\n                    <div className={`\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ${rankDisplay.bg} ${rankDisplay.color}\n                    `}>\n                        {RankIcon ? (\n                            <RankIcon className=\"w-4 h-4\" />\n                        ) : (\n                            <span className=\"text-xs font-bold\">#{rank}</span>\n                        )}\n                    </div>\n\n                    {/* Class Rank */}\n                    {classRank && (\n                        <div className=\"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700\">\n                            <span className=\"text-xs font-bold\">C{classRank}</span>\n                        </div>\n                    )}\n                </div>\n\n                {/* Avatar - Instagram Style Small Circle */}\n                <motion.div variants={avatarVariants} whileHover=\"hover\" className=\"mb-3\">\n                    <div className={`\n                        ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                        ${styling.avatarClass} ${styling.borderClass}\n                    `}>\n                        {user?.profilePicture || user?.profileImage ? (\n                            <img\n                                src={user.profilePicture || user.profileImage}\n                                alt={user?.name || 'User'}\n                                className=\"w-full h-full object-cover object-center\"\n                                style={{ objectFit: 'cover' }}\n                            />\n                        ) : (\n                            <div className=\"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\">\n                                <span className=\"text-xs font-bold text-white\">\n                                    {(user?.name || 'U').charAt(0).toUpperCase()}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                </motion.div>\n\n                {/* User Info */}\n                <div className=\"space-y-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate max-w-24`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n\n                    {/* XP and Level Info */}\n                    <div className=\"flex items-center space-x-2\">\n                        <LevelBadge\n                            level={user?.currentLevel || 1}\n                            size=\"small\"\n                            showTitle={false}\n                            animated={true}\n                        />\n                        <div className=\"flex flex-col\">\n                            <p className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </p>\n                            {user?.xpToNextLevel > 0 && (\n                                <p className={`text-xs text-gray-400`}>\n                                    {user.xpToNextLevel} to next\n                                </p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Legacy Points (smaller) */}\n                    <p className={`text-xs text-gray-400`}>\n                        {user?.totalPoints || 0} pts (legacy)\n                    </p>\n\n                    {/* Enhanced Stats */}\n                    {user?.averageScore && (\n                        <p className={`${config.subtext} text-gray-500`}>\n                            Avg: {user.averageScore}%\n                        </p>\n                    )}\n\n                    {user?.currentStreak > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                            <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                {user.currentStreak}\n                            </span>\n                        </div>\n                    )}\n\n                    {/* Enhanced Achievements */}\n                    {user?.achievements && user.achievements.length > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            {user.achievements.slice(0, 3).map((achievement, index) => (\n                                <EnhancedAchievementBadge\n                                    key={achievement.id || achievement.type || index}\n                                    achievement={achievement}\n                                    size=\"small\"\n                                    showTooltip={true}\n                                    animated={true}\n                                    showXP={false}\n                                />\n                            ))}\n                            {user.achievements.length > 3 && (\n                                <span className=\"text-xs text-gray-500 ml-1\">\n                                    +{user.achievements.length - 3}\n                                </span>\n                            )}\n                        </div>\n                    )}\n\n                    {/* Subscription Badge */}\n                    <span className={`\n                        inline-block px-2 py-1 rounded-full text-xs font-medium\n                        ${styling.bgClass}\n                    `}>\n                        {styling.statusText}\n                    </span>\n                </div>\n            </motion.div>\n        );\n    }\n\n    // Horizontal layout (default)\n    return (\n        <motion.div\n            variants={cardVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            whileHover=\"hover\"\n            className={`\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200\n                ${isCurrentUser ? 'current-user-card' : ''}\n                ${className}\n            `}\n        >\n            {/* Rank Badges */}\n            <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                {/* Overall Rank */}\n                <div className={`\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `}>\n                    {RankIcon ? (\n                        <RankIcon className=\"w-5 h-5\" />\n                    ) : (\n                        <span className=\"text-sm font-bold\">#{rank}</span>\n                    )}\n                </div>\n\n                {/* Class Rank */}\n                {classRank && (\n                    <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700\">\n                        <span className=\"text-sm font-bold\">C{classRank}</span>\n                    </div>\n                )}\n            </div>\n\n            {/* Avatar - Instagram Style Small Circle */}\n            <motion.div variants={avatarVariants} whileHover=\"hover\" className=\"flex-shrink-0\">\n                <div className={`\n                    ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                    ${styling.avatarClass} ${styling.borderClass}\n                `}>\n                    {user?.profilePicture || user?.profileImage ? (\n                        <img\n                            src={user.profilePicture || user.profileImage}\n                            alt={user?.name || 'User'}\n                            className=\"w-full h-full object-cover object-center\"\n                            style={{ objectFit: 'cover' }}\n                        />\n                    ) : (\n                        <div className=\"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\">\n                            <span className=\"text-xs font-bold text-white\">\n                                {(user?.name || 'U').charAt(0).toUpperCase()}\n                            </span>\n                        </div>\n                    )}\n                </div>\n            </motion.div>\n\n            {/* User Info */}\n            <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2 mb-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n                    <LevelBadge\n                        level={user?.currentLevel || 1}\n                        size=\"small\"\n                        showTitle={false}\n                        animated={true}\n                    />\n                    <span className={`\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.bgClass}\n                    `}>\n                        {styling.statusText}\n                    </span>\n                </div>\n                \n                {showStats && (\n                    <div className=\"flex items-center space-x-4\">\n                        {/* XP Display */}\n                        <div className=\"flex items-center space-x-1\">\n                            <TbBolt className=\"w-3 h-3 text-blue-500\" />\n                            <span className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </span>\n                        </div>\n\n                        {/* Legacy Points (smaller) */}\n                        <span className={`text-xs text-gray-400`}>\n                            {user?.totalPoints || 0} pts\n                        </span>\n\n                        {user?.passedExamsCount !== undefined && (\n                            <span className={`${config.subtext} text-green-600`}>\n                                {user.passedExamsCount} passed\n                            </span>\n                        )}\n                        {user?.quizzesTaken !== undefined && (\n                            <span className={`${config.subtext} text-blue-600`}>\n                                {user.quizzesTaken} quizzes\n                            </span>\n                        )}\n                        {user?.averageScore && (\n                            <span className={`${config.subtext} text-gray-600`}>\n                                {user.averageScore}% avg\n                            </span>\n                        )}\n                        {user?.currentStreak > 0 && (\n                            <div className=\"flex items-center space-x-1\">\n                                <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                                <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                    {user.currentStreak}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                )}\n\n                {/* XP Progress Bar */}\n                {user?.xpToNextLevel > 0 && (\n                    <div className=\"mt-2\">\n                        <XPProgressBar\n                            currentXP={user.totalXP || 0}\n                            totalXP={(user.totalXP || 0) + (user.xpToNextLevel || 0)}\n                            currentLevel={user.currentLevel || 1}\n                            xpToNextLevel={user.xpToNextLevel || 0}\n                            size=\"small\"\n                            showLevel={false}\n                            showXPNumbers={false}\n                            showAnimation={false}\n                        />\n                    </div>\n                )}\n\n                {/* Achievements for horizontal layout */}\n                {user?.achievements && user.achievements.length > 0 && (\n                    <div className=\"mt-2\">\n                        <AchievementList\n                            achievements={user.achievements}\n                            maxDisplay={5}\n                            size=\"small\"\n                            layout=\"horizontal\"\n                        />\n                    </div>\n                )}\n            </div>\n\n            {/* Score */}\n            <div className=\"text-right flex-shrink-0\">\n                <div className={`font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`}>\n                    {(user.rankingScore || user.score || user.totalXP || user.totalPoints || 0).toLocaleString()}\n                </div>\n                <div className={`${config.subtext} text-gray-500`}>\n                    {user.rankingScore ? 'ranking pts' : user.totalXP ? 'XP' : 'points'}\n                </div>\n                {user.breakdown && (\n                    <div className=\"text-xs text-gray-400 mt-1\">\n                        XP: {(user.totalXP || 0).toLocaleString()}\n                    </div>\n                )}\n            </div>\n        </motion.div>\n    );\n};\n\nexport default UserRankingCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACpF,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EACrBC,IAAI;EACJC,IAAI;EACJC,SAAS;EACTC,aAAa,GAAG,KAAK;EACrBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EAAE;EACjBC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG;AAChB,CAAC,KAAK;EACF;EACA,MAAMC,UAAU,GAAG;IACfC,KAAK,EAAE;MACHC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACJL,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDE,KAAK,EAAE;MACHN,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb;EACJ,CAAC;EAED,MAAMG,MAAM,GAAGT,UAAU,CAACH,IAAI,CAAC;;EAE/B;EACA,MAAMa,sBAAsB,GAAGA,CAAA,KAAM;IACjC,MAAMC,kBAAkB,GAAG,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,MAAInB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,4BAA4B,KAAI,MAAM;;IAEnG;IACA,MAAMC,gBAAgB,GAAGF,kBAAkB,CAACG,WAAW,CAAC,CAAC;IAEzD,IAAID,gBAAgB,KAAK,QAAQ,IAAIA,gBAAgB,KAAK,SAAS,EAAE;MACjE,OAAO;QACHE,WAAW,EAAE,6BAA6B;QAC1CC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,gCAAgC;QACtCC,UAAU,EAAE,SAAS;QACrBC,WAAW,EAAE,wBAAwB;QACrCC,OAAO,EAAE;MACb,CAAC;IACL,CAAC,MAAM,IAAIP,gBAAgB,KAAK,MAAM,EAAE;MACpC,OAAO;QACHE,WAAW,EAAE,aAAa;QAC1BC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,8BAA8B;QACpCC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE,sBAAsB;QACnCC,OAAO,EAAE;MACb,CAAC;IACL,CAAC,MAAM;MACH,OAAO;QACHL,WAAW,EAAE,gBAAgB;QAC7BC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,WAAW;QACjBC,UAAU,EAAE,SAAS;QACrBC,WAAW,EAAE,qBAAqB;QAClCC,OAAO,EAAE;MACb,CAAC;IACL;EACJ,CAAC;EAED,MAAMC,OAAO,GAAGX,sBAAsB,CAAC,CAAC;;EAExC;EACA,MAAMY,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAI7B,IAAI,KAAK,CAAC,EAAE;MACZ,OAAO;QAAE8B,IAAI,EAAE1C,OAAO;QAAE2C,KAAK,EAAE,iBAAiB;QAAEC,EAAE,EAAE;MAAe,CAAC;IAC1E,CAAC,MAAM,IAAIhC,IAAI,KAAK,CAAC,EAAE;MACnB,OAAO;QAAE8B,IAAI,EAAE3C,OAAO;QAAE4C,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACtE,CAAC,MAAM,IAAIhC,IAAI,KAAK,CAAC,EAAE;MACnB,OAAO;QAAE8B,IAAI,EAAE5C,QAAQ;QAAE6C,KAAK,EAAE,gBAAgB;QAAEC,EAAE,EAAE;MAAc,CAAC;IACzE,CAAC,MAAM,IAAIhC,IAAI,IAAI,EAAE,EAAE;MACnB,OAAO;QAAE8B,IAAI,EAAEzC,MAAM;QAAE0C,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACrE,CAAC,MAAM;MACH,OAAO;QAAEF,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACnE;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGJ,cAAc,CAAC,CAAC;EACpC,MAAMK,QAAQ,GAAGD,WAAW,CAACH,IAAI;;EAEjC;EACA,MAAMK,YAAY,GAAG;IACjBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAC;IAC7BC,OAAO,EAAE;MACLF,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC,CAAC;IACDC,KAAK,EAAE;MACHC,KAAK,EAAE,IAAI;MACXH,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC;EACJ,CAAC;EAED,MAAMG,cAAc,GAAG;IACnBF,KAAK,EAAE;MACHC,KAAK,EAAE,GAAG;MACVH,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC;EACJ,CAAC;;EAED;EACA,MAAMI,YAAY,GAAGA,CAAC;IAAEC;EAAS,CAAC,KAAK;IACnC,oBACIjD,OAAA;MAAKS,SAAS,EAAG,GAAEU,MAAM,CAACP,MAAO,IAAGmB,OAAO,CAACN,WAAY,IAAGM,OAAO,CAACJ,IAAK,EAAE;MAAAsB,QAAA,EACrEA;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd,CAAC;EAED,IAAI/C,MAAM,KAAK,UAAU,EAAE;IACvB,oBACIN,OAAA,CAACZ,MAAM,CAACkE,GAAG;MACPC,QAAQ,EAAEjB,YAAa;MACvBkB,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,SAAS;MACjBC,UAAU,EAAC,OAAO;MAClBjD,SAAS,EAAG;AAC5B,0EAA0EU,MAAM,CAACJ,OAAQ;AACzF;AACA,sBAAsBV,aAAa,GAAG,mBAAmB,GAAG,EAAG;AAC/D,sBAAsBI,SAAU;AAChC,iBAAkB;MAAAwC,QAAA,gBAGFjD,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAwC,QAAA,gBAE7CjD,OAAA;UAAKS,SAAS,EAAG;AACrC;AACA,0BAA0B2B,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AAC9D,qBAAsB;UAAAe,QAAA,EACGZ,QAAQ,gBACLrC,OAAA,CAACqC,QAAQ;YAAC5B,SAAS,EAAC;UAAS;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhCrD,OAAA;YAAMS,SAAS,EAAC,mBAAmB;YAAAwC,QAAA,GAAC,GAAC,EAAC9C,IAAI;UAAA;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QACpD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGLjD,SAAS,iBACNJ,OAAA;UAAKS,SAAS,EAAC,iFAAiF;UAAAwC,QAAA,eAC5FjD,OAAA;YAAMS,SAAS,EAAC,mBAAmB;YAAAwC,QAAA,GAAC,GAAC,EAAC7C,SAAS;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGNrD,OAAA,CAACZ,MAAM,CAACkE,GAAG;QAACC,QAAQ,EAAER,cAAe;QAACW,UAAU,EAAC,OAAO;QAACjD,SAAS,EAAC,MAAM;QAAAwC,QAAA,eACrEjD,OAAA;UAAKS,SAAS,EAAG;AACrC,0BAA0BU,MAAM,CAACP,MAAO;AACxC,0BAA0BmB,OAAO,CAACN,WAAY,IAAGM,OAAO,CAACF,WAAY;AACrE,qBAAsB;UAAAoB,QAAA,EACG/C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyD,cAAc,IAAIzD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0D,YAAY,gBACvC5D,OAAA;YACI6D,GAAG,EAAE3D,IAAI,CAACyD,cAAc,IAAIzD,IAAI,CAAC0D,YAAa;YAC9CE,GAAG,EAAE,CAAA5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI,KAAI,MAAO;YAC1BtD,SAAS,EAAC,0CAA0C;YACpDuD,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAQ;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,gBAEFrD,OAAA;YAAKS,SAAS,EAAC,8FAA8F;YAAAwC,QAAA,eACzGjD,OAAA;cAAMS,SAAS,EAAC,8BAA8B;cAAAwC,QAAA,EACzC,CAAC,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI,KAAI,GAAG,EAAEG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGbrD,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAwC,QAAA,gBACtBjD,OAAA;UAAIS,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,kCAAkC;UAAAoC,QAAA,EACzE,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI,KAAI;QAAc;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAGLrD,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAwC,QAAA,gBACxCjD,OAAA,CAACH,UAAU;YACPuE,KAAK,EAAE,CAAAlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,YAAY,KAAI,CAAE;YAC/B9D,IAAI,EAAC,OAAO;YACZ+D,SAAS,EAAE,KAAM;YACjBC,QAAQ,EAAE;UAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACFrD,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAwC,QAAA,gBAC1BjD,OAAA;cAAGS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,4BAA4B;cAAAmC,QAAA,GACvD,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,OAAO,KAAI,CAAC,EAAC,KACxB;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACH,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,aAAa,IAAG,CAAC,iBACpBzE,OAAA;cAAGS,SAAS,EAAG,uBAAuB;cAAAwC,QAAA,GACjC/C,IAAI,CAACuE,aAAa,EAAC,UACxB;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNrD,OAAA;UAAGS,SAAS,EAAG,uBAAuB;UAAAwC,QAAA,GACjC,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,WAAW,KAAI,CAAC,EAAC,eAC5B;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGH,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,YAAY,kBACf3E,OAAA;UAAGS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAAmC,QAAA,GAAC,OACxC,EAAC/C,IAAI,CAACyE,YAAY,EAAC,GAC5B;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACN,EAEA,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,aAAa,IAAG,CAAC,iBACpB5E,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAwC,QAAA,gBACxCjD,OAAA,CAACP,OAAO;YAACgB,SAAS,EAAC;UAAyB;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CrD,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAAmC,QAAA,EAC5D/C,IAAI,CAAC0E;UAAa;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR,EAGA,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,YAAY,KAAI3E,IAAI,CAAC2E,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC/C9E,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAwC,QAAA,GACvC/C,IAAI,CAAC2E,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAClDlF,OAAA,CAACF,wBAAwB;YAErBmF,WAAW,EAAEA,WAAY;YACzB1E,IAAI,EAAC,OAAO;YACZ4E,WAAW,EAAE,IAAK;YAClBZ,QAAQ,EAAE,IAAK;YACfa,MAAM,EAAE;UAAM,GALTH,WAAW,CAACI,EAAE,IAAIJ,WAAW,CAACK,IAAI,IAAIJ,KAAK;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMnD,CACJ,CAAC,EACDnD,IAAI,CAAC2E,YAAY,CAACC,MAAM,GAAG,CAAC,iBACzB9E,OAAA;YAAMS,SAAS,EAAC,4BAA4B;YAAAwC,QAAA,GAAC,GACxC,EAAC/C,IAAI,CAAC2E,YAAY,CAACC,MAAM,GAAG,CAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR,eAGDrD,OAAA;UAAMS,SAAS,EAAG;AACtC;AACA,0BAA0BsB,OAAO,CAACD,OAAQ;AAC1C,qBAAsB;UAAAmB,QAAA,EACGlB,OAAO,CAACH;QAAU;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAErB;;EAEA;EACA,oBACIrD,OAAA,CAACZ,MAAM,CAACkE,GAAG;IACPC,QAAQ,EAAEjB,YAAa;IACvBkB,OAAO,EAAC,QAAQ;IAChBC,OAAO,EAAC,SAAS;IACjBC,UAAU,EAAC,OAAO;IAClBjD,SAAS,EAAG;AACxB,iDAAiDU,MAAM,CAACH,OAAQ,IAAGG,MAAM,CAACJ,OAAQ;AAClF;AACA,kBAAkBV,aAAa,GAAG,mBAAmB,GAAG,EAAG;AAC3D,kBAAkBI,SAAU;AAC5B,aAAc;IAAAwC,QAAA,gBAGFjD,OAAA;MAAKS,SAAS,EAAC,2CAA2C;MAAAwC,QAAA,gBAEtDjD,OAAA;QAAKS,SAAS,EAAG;AACjC;AACA,sBAAsB2B,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AAC1D,iBAAkB;QAAAe,QAAA,EACGZ,QAAQ,gBACLrC,OAAA,CAACqC,QAAQ;UAAC5B,SAAS,EAAC;QAAS;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEhCrD,OAAA;UAAMS,SAAS,EAAC,mBAAmB;UAAAwC,QAAA,GAAC,GAAC,EAAC9C,IAAI;QAAA;UAAA+C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACpD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGLjD,SAAS,iBACNJ,OAAA;QAAKS,SAAS,EAAC,mFAAmF;QAAAwC,QAAA,eAC9FjD,OAAA;UAAMS,SAAS,EAAC,mBAAmB;UAAAwC,QAAA,GAAC,GAAC,EAAC7C,SAAS;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNrD,OAAA,CAACZ,MAAM,CAACkE,GAAG;MAACC,QAAQ,EAAER,cAAe;MAACW,UAAU,EAAC,OAAO;MAACjD,SAAS,EAAC,eAAe;MAAAwC,QAAA,eAC9EjD,OAAA;QAAKS,SAAS,EAAG;AACjC,sBAAsBU,MAAM,CAACP,MAAO;AACpC,sBAAsBmB,OAAO,CAACN,WAAY,IAAGM,OAAO,CAACF,WAAY;AACjE,iBAAkB;QAAAoB,QAAA,EACG/C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEyD,cAAc,IAAIzD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE0D,YAAY,gBACvC5D,OAAA;UACI6D,GAAG,EAAE3D,IAAI,CAACyD,cAAc,IAAIzD,IAAI,CAAC0D,YAAa;UAC9CE,GAAG,EAAE,CAAA5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI,KAAI,MAAO;UAC1BtD,SAAS,EAAC,0CAA0C;UACpDuD,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAQ;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,gBAEFrD,OAAA;UAAKS,SAAS,EAAC,8FAA8F;UAAAwC,QAAA,eACzGjD,OAAA;YAAMS,SAAS,EAAC,8BAA8B;YAAAwC,QAAA,EACzC,CAAC,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI,KAAI,GAAG,EAAEG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGbrD,OAAA;MAAKS,SAAS,EAAC,gBAAgB;MAAAwC,QAAA,gBAC3BjD,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAwC,QAAA,gBAC7CjD,OAAA;UAAIS,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,yBAAyB;UAAAoC,QAAA,EAChE,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,IAAI,KAAI;QAAc;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACLrD,OAAA,CAACH,UAAU;UACPuE,KAAK,EAAE,CAAAlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,YAAY,KAAI,CAAE;UAC/B9D,IAAI,EAAC,OAAO;UACZ+D,SAAS,EAAE,KAAM;UACjBC,QAAQ,EAAE;QAAK;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACFrD,OAAA;UAAMS,SAAS,EAAG;AACtC;AACA,0BAA0BsB,OAAO,CAACD,OAAQ;AAC1C,qBAAsB;UAAAmB,QAAA,EACGlB,OAAO,CAACH;QAAU;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL7C,SAAS,iBACNR,OAAA;QAAKS,SAAS,EAAC,6BAA6B;QAAAwC,QAAA,gBAExCjD,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAwC,QAAA,gBACxCjD,OAAA,CAACN,MAAM;YAACe,SAAS,EAAC;UAAuB;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CrD,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,4BAA4B;YAAAmC,QAAA,GAC1D,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,OAAO,KAAI,CAAC,EAAC,KACxB;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNrD,OAAA;UAAMS,SAAS,EAAG,uBAAuB;UAAAwC,QAAA,GACpC,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,WAAW,KAAI,CAAC,EAAC,MAC5B;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAEN,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqF,gBAAgB,MAAKC,SAAS,iBACjCxF,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,iBAAiB;UAAAmC,QAAA,GAC/C/C,IAAI,CAACqF,gBAAgB,EAAC,SAC3B;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuF,YAAY,MAAKD,SAAS,iBAC7BxF,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAAmC,QAAA,GAC9C/C,IAAI,CAACuF,YAAY,EAAC,UACvB;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,YAAY,kBACf3E,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAAmC,QAAA,GAC9C/C,IAAI,CAACyE,YAAY,EAAC,OACvB;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,aAAa,IAAG,CAAC,iBACpB5E,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAwC,QAAA,gBACxCjD,OAAA,CAACP,OAAO;YAACgB,SAAS,EAAC;UAAyB;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CrD,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAAmC,QAAA,EAC5D/C,IAAI,CAAC0E;UAAa;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR,EAGA,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,aAAa,IAAG,CAAC,iBACpBzE,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAwC,QAAA,eACjBjD,OAAA,CAACJ,aAAa;UACV8F,SAAS,EAAExF,IAAI,CAACsE,OAAO,IAAI,CAAE;UAC7BA,OAAO,EAAE,CAACtE,IAAI,CAACsE,OAAO,IAAI,CAAC,KAAKtE,IAAI,CAACuE,aAAa,IAAI,CAAC,CAAE;UACzDJ,YAAY,EAAEnE,IAAI,CAACmE,YAAY,IAAI,CAAE;UACrCI,aAAa,EAAEvE,IAAI,CAACuE,aAAa,IAAI,CAAE;UACvClE,IAAI,EAAC,OAAO;UACZoF,SAAS,EAAE,KAAM;UACjBC,aAAa,EAAE,KAAM;UACrBC,aAAa,EAAE;QAAM;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGA,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,YAAY,KAAI3E,IAAI,CAAC2E,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC/C9E,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAwC,QAAA,eACjBjD,OAAA,CAACL,eAAe;UACZkF,YAAY,EAAE3E,IAAI,CAAC2E,YAAa;UAChCiB,UAAU,EAAE,CAAE;UACdvF,IAAI,EAAC,OAAO;UACZD,MAAM,EAAC;QAAY;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNrD,OAAA;MAAKS,SAAS,EAAC,0BAA0B;MAAAwC,QAAA,gBACrCjD,OAAA;QAAKS,SAAS,EAAG,aAAYU,MAAM,CAACN,IAAK,IAAGR,aAAa,GAAG,eAAe,GAAG,eAAgB,EAAE;QAAA4C,QAAA,EAC3F,CAAC/C,IAAI,CAAC6F,YAAY,IAAI7F,IAAI,CAAC8F,KAAK,IAAI9F,IAAI,CAACsE,OAAO,IAAItE,IAAI,CAACwE,WAAW,IAAI,CAAC,EAAEuB,cAAc,CAAC;MAAC;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3F,CAAC,eACNrD,OAAA;QAAKS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;QAAAmC,QAAA,EAC7C/C,IAAI,CAAC6F,YAAY,GAAG,aAAa,GAAG7F,IAAI,CAACsE,OAAO,GAAG,IAAI,GAAG;MAAQ;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,EACLnD,IAAI,CAACgG,SAAS,iBACXlG,OAAA;QAAKS,SAAS,EAAC,4BAA4B;QAAAwC,QAAA,GAAC,MACpC,EAAC,CAAC/C,IAAI,CAACsE,OAAO,IAAI,CAAC,EAAEyB,cAAc,CAAC,CAAC;MAAA;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAAC8C,EAAA,GA5aIlG,eAAe;AA8arB,eAAeA,eAAe;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}