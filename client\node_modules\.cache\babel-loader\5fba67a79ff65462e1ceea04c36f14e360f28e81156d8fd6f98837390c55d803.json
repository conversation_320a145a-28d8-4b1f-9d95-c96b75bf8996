{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbSearch, Tb<PERSON><PERSON>er, TbUser, TbUsers, TbTrophy } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingList = ({\n  users = [],\n  currentUserId = null,\n  layout = 'horizontal',\n  // 'horizontal', 'vertical', 'grid'\n  size = 'medium',\n  showStats = true,\n  className = '',\n  currentUserRef = null,\n  showFindMe = false\n}) => {\n  _s();\n  const [localShowFindMe, setLocalShowFindMe] = useState(false);\n  const localCurrentUserRef = useRef(null);\n\n  // Use passed refs or local ones\n  const userRef = currentUserRef || localCurrentUserRef;\n  const findMeActive = showFindMe || localShowFindMe;\n\n  // Sort users by rank (no filtering)\n  const sortedUsers = users.sort((a, b) => (a.rank || 0) - (b.rank || 0));\n\n  // Calculate class ranks\n  const usersWithClassRank = sortedUsers.map(user => {\n    // Group users by class and calculate class rank\n    const sameClassUsers = sortedUsers.filter(u => u.class === user.class);\n    const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n    return {\n      ...user,\n      classRank\n    };\n  });\n\n  // Find current user\n  const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n  const currentUserIndex = currentUser ? usersWithClassRank.findIndex(user => user._id === currentUserId || user.userId === currentUserId) : -1;\n\n  // Scroll to current user\n  const scrollToCurrentUser = () => {\n    if (userRef.current) {\n      userRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setLocalShowFindMe(true);\n      // Hide the highlight after 3 seconds\n      setTimeout(() => setLocalShowFindMe(false), 3000);\n    }\n  };\n\n  // Get layout classes\n  const getLayoutClasses = () => {\n    switch (layout) {\n      case 'vertical':\n        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n      case 'grid':\n        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n      case 'horizontal':\n      default:\n        return 'space-y-3';\n    }\n  };\n\n  // Container animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  // Stats summary with enhanced calculations\n  const totalUsers = users.length;\n  const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium' || u.normalizedSubscriptionStatus === 'premium').length;\n\n  // Use ranking score or XP as the primary metric\n  const topScore = users.length > 0 ? Math.max(...users.map(u => u.rankingScore || u.totalXP || u.totalPoints || 0)) : 0;\n\n  // Calculate additional stats\n  const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n  const averageXP = users.length > 0 ? Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [showStats && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: -20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n            className: \"w-6 h-6 text-yellow-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Leaderboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 25\n        }, this), currentUserId && /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: scrollToCurrentUser,\n          className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbUser, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Find Me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 sm:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n              className: \"w-5 h-5 text-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: [activeUsers, \" active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-5 h-5 text-yellow-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Premium Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: premiumUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: [totalUsers > 0 ? Math.round(premiumUsers / totalUsers * 100) : 0, \"% premium\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbUser, {\n              className: \"w-5 h-5 text-green-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Top Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: topScore.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: \"ranking points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg p-4 border border-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-5 h-5 text-purple-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Avg XP\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-gray-900 mt-1\",\n            children: averageXP.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: \"experience points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: containerVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      className: getLayoutClasses(),\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        children: usersWithClassRank.map((user, index) => {\n          const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n          const rank = user.rank || index + 1;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            ref: isCurrentUser ? userRef : null,\n            layout: true,\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              scale: 0.9\n            },\n            transition: {\n              duration: 0.2\n            },\n            className: `${isCurrentUser && findMeActive ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl' : isCurrentUser ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(UserRankingCard, {\n              user: user,\n              rank: rank,\n              classRank: user.classRank,\n              isCurrentUser: isCurrentUser,\n              layout: layout,\n              size: size,\n              showStats: showStats\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 33\n            }, this)\n          }, user.userId || user._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 13\n    }, this), usersWithClassRank.length === 0 && /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0\n      },\n      animate: {\n        opacity: 1\n      },\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n        className: \"w-16 h-16 text-gray-300 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-medium text-gray-900 mb-2\",\n        children: \"No users found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: \"No users available to display\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 17\n    }, this), currentUserId && usersWithClassRank.length > 10 && /*#__PURE__*/_jsxDEV(motion.button, {\n      initial: {\n        opacity: 0,\n        scale: 0\n      },\n      animate: {\n        opacity: 1,\n        scale: 1\n      },\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      onClick: scrollToCurrentUser,\n      className: \"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\",\n      title: \"Find me in ranking\",\n      children: /*#__PURE__*/_jsxDEV(TbUser, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 9\n  }, this);\n};\n_s(UserRankingList, \"RTQ97dkuZ8K2q/XxV28t5FHTxeI=\");\n_c = UserRankingList;\nexport default UserRankingList;\nvar _c;\n$RefreshReg$(_c, \"UserRankingList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "motion", "AnimatePresence", "TbSearch", "Tb<PERSON><PERSON>er", "TbUser", "TbUsers", "TbTrophy", "UserRankingCard", "jsxDEV", "_jsxDEV", "UserRankingList", "users", "currentUserId", "layout", "size", "showStats", "className", "currentUserRef", "showFindMe", "_s", "localShowFindMe", "setLocalShowFindMe", "localCurrentUserRef", "userRef", "findMeActive", "sortedUsers", "sort", "a", "b", "rank", "usersWithClassRank", "map", "user", "sameClassUsers", "filter", "u", "class", "classRank", "findIndex", "_id", "userId", "currentUser", "find", "currentUserIndex", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "setTimeout", "getLayoutClasses", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "totalUsers", "length", "premiumUsers", "subscriptionStatus", "normalizedSubscriptionStatus", "topScore", "Math", "max", "rankingScore", "totalXP", "totalPoints", "activeUsers", "totalQuizzesTaken", "averageXP", "round", "reduce", "sum", "children", "div", "initial", "y", "animate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "button", "whileHover", "scale", "whileTap", "onClick", "toLocaleString", "variants", "index", "isCurrentUser", "ref", "exit", "duration", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingList.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Tb<PERSON><PERSON>ch, Tb<PERSON><PERSON>er, TbUser, TbUsers, TbTrophy } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\n\nconst UserRankingList = ({\n    users = [],\n    currentUserId = null,\n    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'\n    size = 'medium',\n    showStats = true,\n    className = '',\n    currentUserRef = null,\n    showFindMe = false\n}) => {\n    const [localShowFindMe, setLocalShowFindMe] = useState(false);\n    const localCurrentUserRef = useRef(null);\n\n    // Use passed refs or local ones\n    const userRef = currentUserRef || localCurrentUserRef;\n    const findMeActive = showFindMe || localShowFindMe;\n\n    // Sort users by rank (no filtering)\n    const sortedUsers = users.sort((a, b) => (a.rank || 0) - (b.rank || 0));\n\n    // Calculate class ranks\n    const usersWithClassRank = sortedUsers.map(user => {\n        // Group users by class and calculate class rank\n        const sameClassUsers = sortedUsers.filter(u => u.class === user.class);\n        const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n        return { ...user, classRank };\n    });\n\n    // Find current user\n    const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n    const currentUserIndex = currentUser ? usersWithClassRank.findIndex(user => user._id === currentUserId || user.userId === currentUserId) : -1;\n\n    // Scroll to current user\n    const scrollToCurrentUser = () => {\n        if (userRef.current) {\n            userRef.current.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n            setLocalShowFindMe(true);\n            // Hide the highlight after 3 seconds\n            setTimeout(() => setLocalShowFindMe(false), 3000);\n        }\n    };\n\n    // Get layout classes\n    const getLayoutClasses = () => {\n        switch (layout) {\n            case 'vertical':\n                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n            case 'grid':\n                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';\n            case 'horizontal':\n            default:\n                return 'space-y-3';\n        }\n    };\n\n    // Container animation variants\n    const containerVariants = {\n        hidden: { opacity: 0 },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n\n    // Stats summary with enhanced calculations\n    const totalUsers = users.length;\n    const premiumUsers = users.filter(u =>\n        u.subscriptionStatus === 'active' ||\n        u.subscriptionStatus === 'premium' ||\n        u.normalizedSubscriptionStatus === 'premium'\n    ).length;\n\n    // Use ranking score or XP as the primary metric\n    const topScore = users.length > 0 ? Math.max(...users.map(u =>\n        u.rankingScore || u.totalXP || u.totalPoints || 0\n    )) : 0;\n\n    // Calculate additional stats\n    const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n    const averageXP = users.length > 0 ?\n        Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n\n    return (\n        <div className={`space-y-6 ${className}`}>\n            {/* Header with Stats */}\n            {showStats && (\n                <motion.div\n                    initial={{ opacity: 0, y: -20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200\"\n                >\n                    <div className=\"flex items-center justify-between mb-4\">\n                        <h2 className=\"text-2xl font-bold text-gray-900 flex items-center space-x-2\">\n                            <TbTrophy className=\"w-6 h-6 text-yellow-500\" />\n                            <span>Leaderboard</span>\n                        </h2>\n                        \n                        {currentUserId && (\n                            <motion.button\n                                whileHover={{ scale: 1.05 }}\n                                whileTap={{ scale: 0.95 }}\n                                onClick={scrollToCurrentUser}\n                                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2\"\n                            >\n                                <TbUser className=\"w-4 h-4\" />\n                                <span>Find Me</span>\n                            </motion.button>\n                        )}\n                    </div>\n\n                    <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4\">\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbUsers className=\"w-5 h-5 text-blue-500\" />\n                                <span className=\"text-sm text-gray-600\">Total Users</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{totalUsers}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">{activeUsers} active</div>\n                        </div>\n\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbTrophy className=\"w-5 h-5 text-yellow-500\" />\n                                <span className=\"text-sm text-gray-600\">Premium Users</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{premiumUsers}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">\n                                {totalUsers > 0 ? Math.round((premiumUsers / totalUsers) * 100) : 0}% premium\n                            </div>\n                        </div>\n\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbUser className=\"w-5 h-5 text-green-500\" />\n                                <span className=\"text-sm text-gray-600\">Top Score</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{topScore.toLocaleString()}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">ranking points</div>\n                        </div>\n\n                        <div className=\"bg-white rounded-lg p-4 border border-gray-200\">\n                            <div className=\"flex items-center space-x-2\">\n                                <TbTrophy className=\"w-5 h-5 text-purple-500\" />\n                                <span className=\"text-sm text-gray-600\">Avg XP</span>\n                            </div>\n                            <div className=\"text-2xl font-bold text-gray-900 mt-1\">{averageXP.toLocaleString()}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">experience points</div>\n                        </div>\n                    </div>\n                </motion.div>\n            )}\n\n\n\n            {/* User List */}\n            <motion.div\n                variants={containerVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className={getLayoutClasses()}\n            >\n                <AnimatePresence>\n                    {usersWithClassRank.map((user, index) => {\n                        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n                        const rank = user.rank || index + 1;\n\n                        return (\n                            <motion.div\n                                key={user.userId || user._id}\n                                ref={isCurrentUser ? userRef : null}\n                                layout\n                                initial={{ opacity: 0, scale: 0.9 }}\n                                animate={{ opacity: 1, scale: 1 }}\n                                exit={{ opacity: 0, scale: 0.9 }}\n                                transition={{ duration: 0.2 }}\n                                className={`${\n                                    isCurrentUser && findMeActive\n                                        ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl'\n                                        : isCurrentUser\n                                        ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg'\n                                        : ''\n                                }`}\n                            >\n                                <UserRankingCard\n                                    user={user}\n                                    rank={rank}\n                                    classRank={user.classRank}\n                                    isCurrentUser={isCurrentUser}\n                                    layout={layout}\n                                    size={size}\n                                    showStats={showStats}\n                                />\n                            </motion.div>\n                        );\n                    })}\n                </AnimatePresence>\n            </motion.div>\n\n            {/* Empty State */}\n            {usersWithClassRank.length === 0 && (\n                <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    className=\"text-center py-12\"\n                >\n                    <TbUsers className=\"w-16 h-16 text-gray-300 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No users found</h3>\n                    <p className=\"text-gray-500\">\n                        No users available to display\n                    </p>\n                </motion.div>\n            )}\n\n            {/* Floating Action Button for Current User */}\n            {currentUserId && usersWithClassRank.length > 10 && (\n                <motion.button\n                    initial={{ opacity: 0, scale: 0 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    onClick={scrollToCurrentUser}\n                    className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50\"\n                    title=\"Find me in ranking\"\n                >\n                    <TbUser className=\"w-6 h-6\" />\n                </motion.button>\n            )}\n        </div>\n    );\n};\n\nexport default UserRankingList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,gBAAgB;AAC9E,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,eAAe,GAAGA,CAAC;EACrBC,KAAK,GAAG,EAAE;EACVC,aAAa,GAAG,IAAI;EACpBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG,EAAE;EACdC,cAAc,GAAG,IAAI;EACrBC,UAAU,GAAG;AACjB,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMwB,mBAAmB,GAAGvB,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMwB,OAAO,GAAGN,cAAc,IAAIK,mBAAmB;EACrD,MAAME,YAAY,GAAGN,UAAU,IAAIE,eAAe;;EAElD;EACA,MAAMK,WAAW,GAAGd,KAAK,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAACE,IAAI,IAAI,CAAC,KAAKD,CAAC,CAACC,IAAI,IAAI,CAAC,CAAC,CAAC;;EAEvE;EACA,MAAMC,kBAAkB,GAAGL,WAAW,CAACM,GAAG,CAACC,IAAI,IAAI;IAC/C;IACA,MAAMC,cAAc,GAAGR,WAAW,CAACS,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,KAAKJ,IAAI,CAACI,KAAK,CAAC;IACtE,MAAMC,SAAS,GAAGJ,cAAc,CAACK,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACI,GAAG,KAAKP,IAAI,CAACO,GAAG,IAAIJ,CAAC,CAACK,MAAM,KAAKR,IAAI,CAACQ,MAAM,CAAC,GAAG,CAAC;IACnG,OAAO;MAAE,GAAGR,IAAI;MAAEK;IAAU,CAAC;EACjC,CAAC,CAAC;;EAEF;EACA,MAAMI,WAAW,GAAGX,kBAAkB,CAACY,IAAI,CAACV,IAAI,IAAIA,IAAI,CAACO,GAAG,KAAK3B,aAAa,IAAIoB,IAAI,CAACQ,MAAM,KAAK5B,aAAa,CAAC;EAChH,MAAM+B,gBAAgB,GAAGF,WAAW,GAAGX,kBAAkB,CAACQ,SAAS,CAACN,IAAI,IAAIA,IAAI,CAACO,GAAG,KAAK3B,aAAa,IAAIoB,IAAI,CAACQ,MAAM,KAAK5B,aAAa,CAAC,GAAG,CAAC,CAAC;;EAE7I;EACA,MAAMgC,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIrB,OAAO,CAACsB,OAAO,EAAE;MACjBtB,OAAO,CAACsB,OAAO,CAACC,cAAc,CAAC;QAC3BC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACF3B,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACA4B,UAAU,CAAC,MAAM5B,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACrD;EACJ,CAAC;;EAED;EACA,MAAM6B,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,QAAQrC,MAAM;MACV,KAAK,UAAU;QACX,OAAO,qEAAqE;MAChF,KAAK,MAAM;QACP,OAAO,sDAAsD;MACjE,KAAK,YAAY;MACjB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;;EAED;EACA,MAAMsC,iBAAiB,GAAG;IACtBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACLD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACRC,eAAe,EAAE;MACrB;IACJ;EACJ,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG9C,KAAK,CAAC+C,MAAM;EAC/B,MAAMC,YAAY,GAAGhD,KAAK,CAACuB,MAAM,CAACC,CAAC,IAC/BA,CAAC,CAACyB,kBAAkB,KAAK,QAAQ,IACjCzB,CAAC,CAACyB,kBAAkB,KAAK,SAAS,IAClCzB,CAAC,CAAC0B,4BAA4B,KAAK,SACvC,CAAC,CAACH,MAAM;;EAER;EACA,MAAMI,QAAQ,GAAGnD,KAAK,CAAC+C,MAAM,GAAG,CAAC,GAAGK,IAAI,CAACC,GAAG,CAAC,GAAGrD,KAAK,CAACoB,GAAG,CAACI,CAAC,IACvDA,CAAC,CAAC8B,YAAY,IAAI9B,CAAC,CAAC+B,OAAO,IAAI/B,CAAC,CAACgC,WAAW,IAAI,CACpD,CAAC,CAAC,GAAG,CAAC;;EAEN;EACA,MAAMC,WAAW,GAAGzD,KAAK,CAACuB,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACkC,iBAAiB,IAAI,CAAC,IAAI,CAAC,CAAC,CAACX,MAAM;EAC5E,MAAMY,SAAS,GAAG3D,KAAK,CAAC+C,MAAM,GAAG,CAAC,GAC9BK,IAAI,CAACQ,KAAK,CAAC5D,KAAK,CAAC6D,MAAM,CAAC,CAACC,GAAG,EAAEtC,CAAC,KAAKsC,GAAG,IAAItC,CAAC,CAAC+B,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGvD,KAAK,CAAC+C,MAAM,CAAC,GAAG,CAAC;EAEtF,oBACIjD,OAAA;IAAKO,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAA0D,QAAA,GAEpC3D,SAAS,iBACNN,OAAA,CAACT,MAAM,CAAC2E,GAAG;MACPC,OAAO,EAAE;QAAEvB,OAAO,EAAE,CAAC;QAAEwB,CAAC,EAAE,CAAC;MAAG,CAAE;MAChCC,OAAO,EAAE;QAAEzB,OAAO,EAAE,CAAC;QAAEwB,CAAC,EAAE;MAAE,CAAE;MAC9B7D,SAAS,EAAC,kFAAkF;MAAA0D,QAAA,gBAE5FjE,OAAA;QAAKO,SAAS,EAAC,wCAAwC;QAAA0D,QAAA,gBACnDjE,OAAA;UAAIO,SAAS,EAAC,8DAA8D;UAAA0D,QAAA,gBACxEjE,OAAA,CAACH,QAAQ;YAACU,SAAS,EAAC;UAAyB;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChDzE,OAAA;YAAAiE,QAAA,EAAM;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,EAEJtE,aAAa,iBACVH,OAAA,CAACT,MAAM,CAACmF,MAAM;UACVC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BE,OAAO,EAAE3C,mBAAoB;UAC7B5B,SAAS,EAAC,sIAAsI;UAAA0D,QAAA,gBAEhJjE,OAAA,CAACL,MAAM;YAACY,SAAS,EAAC;UAAS;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BzE,OAAA;YAAAiE,QAAA,EAAM;UAAO;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAClB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAENzE,OAAA;QAAKO,SAAS,EAAC,uCAAuC;QAAA0D,QAAA,gBAClDjE,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA0D,QAAA,gBAC3DjE,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA0D,QAAA,gBACxCjE,OAAA,CAACJ,OAAO;cAACW,SAAS,EAAC;YAAuB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CzE,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA0D,QAAA,EAAC;YAAW;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNzE,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA0D,QAAA,EAAEjB;UAAU;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzEzE,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA0D,QAAA,GAAEN,WAAW,EAAC,SAAO;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eAENzE,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA0D,QAAA,gBAC3DjE,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA0D,QAAA,gBACxCjE,OAAA,CAACH,QAAQ;cAACU,SAAS,EAAC;YAAyB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDzE,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA0D,QAAA,EAAC;YAAa;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNzE,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA0D,QAAA,EAAEf;UAAY;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3EzE,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA0D,QAAA,GACtCjB,UAAU,GAAG,CAAC,GAAGM,IAAI,CAACQ,KAAK,CAAEZ,YAAY,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,WACxE;UAAA;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENzE,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA0D,QAAA,gBAC3DjE,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA0D,QAAA,gBACxCjE,OAAA,CAACL,MAAM;cAACY,SAAS,EAAC;YAAwB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CzE,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA0D,QAAA,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNzE,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA0D,QAAA,EAAEZ,QAAQ,CAAC0B,cAAc,CAAC;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxFzE,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA0D,QAAA,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAENzE,OAAA;UAAKO,SAAS,EAAC,gDAAgD;UAAA0D,QAAA,gBAC3DjE,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAA0D,QAAA,gBACxCjE,OAAA,CAACH,QAAQ;cAACU,SAAS,EAAC;YAAyB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDzE,OAAA;cAAMO,SAAS,EAAC,uBAAuB;cAAA0D,QAAA,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACNzE,OAAA;YAAKO,SAAS,EAAC,uCAAuC;YAAA0D,QAAA,EAAEJ,SAAS,CAACkB,cAAc,CAAC;UAAC;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzFzE,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAA0D,QAAA,EAAC;UAAiB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACf,eAKDzE,OAAA,CAACT,MAAM,CAAC2E,GAAG;MACPc,QAAQ,EAAEtC,iBAAkB;MAC5ByB,OAAO,EAAC,QAAQ;MAChBE,OAAO,EAAC,SAAS;MACjB9D,SAAS,EAAEkC,gBAAgB,CAAC,CAAE;MAAAwB,QAAA,eAE9BjE,OAAA,CAACR,eAAe;QAAAyE,QAAA,EACX5C,kBAAkB,CAACC,GAAG,CAAC,CAACC,IAAI,EAAE0D,KAAK,KAAK;UACrC,MAAMC,aAAa,GAAG3D,IAAI,CAACQ,MAAM,KAAK5B,aAAa,IAAIoB,IAAI,CAACO,GAAG,KAAK3B,aAAa;UACjF,MAAMiB,IAAI,GAAGG,IAAI,CAACH,IAAI,IAAI6D,KAAK,GAAG,CAAC;UAEnC,oBACIjF,OAAA,CAACT,MAAM,CAAC2E,GAAG;YAEPiB,GAAG,EAAED,aAAa,GAAGpE,OAAO,GAAG,IAAK;YACpCV,MAAM;YACN+D,OAAO,EAAE;cAAEvB,OAAO,EAAE,CAAC;cAAEgC,KAAK,EAAE;YAAI,CAAE;YACpCP,OAAO,EAAE;cAAEzB,OAAO,EAAE,CAAC;cAAEgC,KAAK,EAAE;YAAE,CAAE;YAClCQ,IAAI,EAAE;cAAExC,OAAO,EAAE,CAAC;cAAEgC,KAAK,EAAE;YAAI,CAAE;YACjC9B,UAAU,EAAE;cAAEuC,QAAQ,EAAE;YAAI,CAAE;YAC9B9E,SAAS,EAAG,GACR2E,aAAa,IAAInE,YAAY,GACvB,6GAA6G,GAC7GmE,aAAa,GACb,+CAA+C,GAC/C,EACT,EAAE;YAAAjB,QAAA,eAEHjE,OAAA,CAACF,eAAe;cACZyB,IAAI,EAAEA,IAAK;cACXH,IAAI,EAAEA,IAAK;cACXQ,SAAS,EAAEL,IAAI,CAACK,SAAU;cAC1BsD,aAAa,EAAEA,aAAc;cAC7B9E,MAAM,EAAEA,MAAO;cACfC,IAAI,EAAEA,IAAK;cACXC,SAAS,EAAEA;YAAU;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC,GAvBGlD,IAAI,CAACQ,MAAM,IAAIR,IAAI,CAACO,GAAG;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBpB,CAAC;QAErB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGZpD,kBAAkB,CAAC4B,MAAM,KAAK,CAAC,iBAC5BjD,OAAA,CAACT,MAAM,CAAC2E,GAAG;MACPC,OAAO,EAAE;QAAEvB,OAAO,EAAE;MAAE,CAAE;MACxByB,OAAO,EAAE;QAAEzB,OAAO,EAAE;MAAE,CAAE;MACxBrC,SAAS,EAAC,mBAAmB;MAAA0D,QAAA,gBAE7BjE,OAAA,CAACJ,OAAO;QAACW,SAAS,EAAC;MAAsC;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5DzE,OAAA;QAAIO,SAAS,EAAC,wCAAwC;QAAA0D,QAAA,EAAC;MAAc;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EzE,OAAA;QAAGO,SAAS,EAAC,eAAe;QAAA0D,QAAA,EAAC;MAE7B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACf,EAGAtE,aAAa,IAAIkB,kBAAkB,CAAC4B,MAAM,GAAG,EAAE,iBAC5CjD,OAAA,CAACT,MAAM,CAACmF,MAAM;MACVP,OAAO,EAAE;QAAEvB,OAAO,EAAE,CAAC;QAAEgC,KAAK,EAAE;MAAE,CAAE;MAClCP,OAAO,EAAE;QAAEzB,OAAO,EAAE,CAAC;QAAEgC,KAAK,EAAE;MAAE,CAAE;MAClCD,UAAU,EAAE;QAAEC,KAAK,EAAE;MAAI,CAAE;MAC3BC,QAAQ,EAAE;QAAED,KAAK,EAAE;MAAI,CAAE;MACzBE,OAAO,EAAE3C,mBAAoB;MAC7B5B,SAAS,EAAC,6IAA6I;MACvJ+E,KAAK,EAAC,oBAAoB;MAAArB,QAAA,eAE1BjE,OAAA,CAACL,MAAM;QAACY,SAAS,EAAC;MAAS;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAClB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC/D,EAAA,CA1OIT,eAAe;AAAAsF,EAAA,GAAftF,eAAe;AA4OrB,eAAeA,eAAe;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}