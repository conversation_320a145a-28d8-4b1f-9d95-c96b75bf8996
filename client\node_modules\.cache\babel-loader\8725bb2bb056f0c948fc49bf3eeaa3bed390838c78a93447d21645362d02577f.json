{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\RankingErrorBoundary.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { TbAlertTriangle, TbRefresh, TbHome } from 'react-icons/tb';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass RankingErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n\n    // Log error to console for debugging\n    console.error('Ranking Error:', error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(RankingErrorFallback, {\n        error: this.state.error,\n        resetError: () => this.setState({\n          hasError: false,\n          error: null,\n          errorInfo: null\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 14\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nconst RankingErrorFallback = ({\n  error,\n  resetError\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const handleRetry = () => {\n    resetError();\n    window.location.reload();\n  };\n  const handleGoHome = () => {\n    navigate('/user/hub');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n        children: /*#__PURE__*/_jsxDEV(TbAlertTriangle, {\n          className: \"w-10 h-10 text-red-600\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900 mb-4\",\n        children: \"Ranking System Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-6\",\n        children: \"We encountered an error while loading the rankings. This might be a temporary issue with the server connection.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRetry,\n          className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Refresh Rankings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleGoHome,\n          className: \"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(TbHome, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Go to Hub\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), process.env.NODE_ENV === 'development' && error && /*#__PURE__*/_jsxDEV(\"details\", {\n        className: \"mt-6 text-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n          className: \"cursor-pointer text-sm text-gray-500 hover:text-gray-700\",\n          children: \"Error Details (Development)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"mt-2 text-xs bg-gray-100 p-3 rounded overflow-auto max-h-32\",\n          children: error.toString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(RankingErrorFallback, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = RankingErrorFallback;\nexport default RankingErrorBoundary;\nvar _c;\n$RefreshReg$(_c, \"RankingErrorFallback\");", "map": {"version": 3, "names": ["React", "TbAlertTriangle", "TbRefresh", "TbHome", "useNavigate", "jsxDEV", "_jsxDEV", "RankingError<PERSON><PERSON><PERSON>ry", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "getDerivedStateFromError", "componentDidCatch", "setState", "console", "render", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "resetError", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "_s", "navigate", "handleRetry", "window", "location", "reload", "handleGoHome", "className", "onClick", "process", "env", "NODE_ENV", "toString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/RankingErrorBoundary.js"], "sourcesContent": ["import React from 'react';\nimport { TbAlertTriangle, TbRefresh, TbHome } from 'react-icons/tb';\nimport { useNavigate } from 'react-router-dom';\n\nclass RankingErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n    \n    // Log error to console for debugging\n    console.error('Ranking Error:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return <RankingErrorFallback \n        error={this.state.error} \n        resetError={() => this.setState({ hasError: false, error: null, errorInfo: null })}\n      />;\n    }\n\n    return this.props.children;\n  }\n}\n\nconst RankingErrorFallback = ({ error, resetError }) => {\n  const navigate = useNavigate();\n\n  const handleRetry = () => {\n    resetError();\n    window.location.reload();\n  };\n\n  const handleGoHome = () => {\n    navigate('/user/hub');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center\">\n        <div className=\"w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n          <TbAlertTriangle className=\"w-10 h-10 text-red-600\" />\n        </div>\n        \n        <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">\n          Ranking System Error\n        </h1>\n        \n        <p className=\"text-gray-600 mb-6\">\n          We encountered an error while loading the rankings. This might be a temporary issue with the server connection.\n        </p>\n        \n        <div className=\"space-y-3\">\n          <button\n            onClick={handleRetry}\n            className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\"\n          >\n            <TbRefresh className=\"w-5 h-5\" />\n            <span>Refresh Rankings</span>\n          </button>\n          \n          <button\n            onClick={handleGoHome}\n            className=\"w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2\"\n          >\n            <TbHome className=\"w-5 h-5\" />\n            <span>Go to Hub</span>\n          </button>\n        </div>\n        \n        {process.env.NODE_ENV === 'development' && error && (\n          <details className=\"mt-6 text-left\">\n            <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n              Error Details (Development)\n            </summary>\n            <pre className=\"mt-2 text-xs bg-gray-100 p-3 rounded overflow-auto max-h-32\">\n              {error.toString()}\n            </pre>\n          </details>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default RankingErrorBoundary;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gBAAgB;AACnE,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,oBAAoB,SAASP,KAAK,CAACQ,SAAS,CAAC;EACjDC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;EAChE;EAEA,OAAOC,wBAAwBA,CAACF,KAAK,EAAE;IACrC,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAI,iBAAiBA,CAACH,KAAK,EAAEC,SAAS,EAAE;IAClC,IAAI,CAACG,QAAQ,CAAC;MACZJ,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;;IAEF;IACAI,OAAO,CAACL,KAAK,CAAC,gBAAgB,EAAEA,KAAK,EAAEC,SAAS,CAAC;EACnD;EAEAK,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACC,QAAQ,EAAE;MACvB,oBAAON,OAAA,CAACc,oBAAoB;QAC1BP,KAAK,EAAE,IAAI,CAACF,KAAK,CAACE,KAAM;QACxBQ,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACJ,QAAQ,CAAC;UAAEL,QAAQ,EAAE,KAAK;UAAEC,KAAK,EAAE,IAAI;UAAEC,SAAS,EAAE;QAAK,CAAC;MAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CAAC;IACJ;IAEA,OAAO,IAAI,CAACf,KAAK,CAACgB,QAAQ;EAC5B;AACF;AAEA,MAAMN,oBAAoB,GAAGA,CAAC;EAAEP,KAAK;EAAEQ;AAAW,CAAC,KAAK;EAAAM,EAAA;EACtD,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAE9B,MAAMyB,WAAW,GAAGA,CAAA,KAAM;IACxBR,UAAU,CAAC,CAAC;IACZS,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBL,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;EAED,oBACEtB,OAAA;IAAK4B,SAAS,EAAC,+FAA+F;IAAAR,QAAA,eAC5GpB,OAAA;MAAK4B,SAAS,EAAC,gEAAgE;MAAAR,QAAA,gBAC7EpB,OAAA;QAAK4B,SAAS,EAAC,iFAAiF;QAAAR,QAAA,eAC9FpB,OAAA,CAACL,eAAe;UAACiC,SAAS,EAAC;QAAwB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eAENnB,OAAA;QAAI4B,SAAS,EAAC,uCAAuC;QAAAR,QAAA,EAAC;MAEtD;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAELnB,OAAA;QAAG4B,SAAS,EAAC,oBAAoB;QAAAR,QAAA,EAAC;MAElC;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJnB,OAAA;QAAK4B,SAAS,EAAC,WAAW;QAAAR,QAAA,gBACxBpB,OAAA;UACE6B,OAAO,EAAEN,WAAY;UACrBK,SAAS,EAAC,8JAA8J;UAAAR,QAAA,gBAExKpB,OAAA,CAACJ,SAAS;YAACgC,SAAS,EAAC;UAAS;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjCnB,OAAA;YAAAoB,QAAA,EAAM;UAAgB;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAETnB,OAAA;UACE6B,OAAO,EAAEF,YAAa;UACtBC,SAAS,EAAC,iKAAiK;UAAAR,QAAA,gBAE3KpB,OAAA,CAACH,MAAM;YAAC+B,SAAS,EAAC;UAAS;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BnB,OAAA;YAAAoB,QAAA,EAAM;UAAS;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIzB,KAAK,iBAC9CP,OAAA;QAAS4B,SAAS,EAAC,gBAAgB;QAAAR,QAAA,gBACjCpB,OAAA;UAAS4B,SAAS,EAAC,0DAA0D;UAAAR,QAAA,EAAC;QAE9E;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACVnB,OAAA;UAAK4B,SAAS,EAAC,6DAA6D;UAAAR,QAAA,EACzEb,KAAK,CAAC0B,QAAQ,CAAC;QAAC;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,CA1DIP,oBAAoB;EAAA,QACPhB,WAAW;AAAA;AAAAoC,EAAA,GADxBpB,oBAAoB;AA4D1B,eAAeb,oBAAoB;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}