{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\WriteExam\\\\index.js\",\n  _s = $RefreshSig$();\nimport { message } from \"antd\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useNavigate, useParams } from \"react-router-dom\";\nimport { getExamById } from \"../../../apicalls/exams\";\nimport { addReport } from \"../../../apicalls/reports\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport Instructions from \"./Instructions\";\nimport Pass from \"../../../assets/pass.gif\";\nimport Fail from \"../../../assets/fail.gif\";\nimport Confetti from \"react-confetti\";\nimport useWindowSize from \"react-use/lib/useWindowSize\";\nimport PassSound from \"../../../assets/pass.mp3\";\nimport FailSound from \"../../../assets/fail.mp3\";\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\n\n// Minimal Safe Quiz Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MinimalQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  onAnswerSelect,\n  onNext,\n  onPrevious,\n  timeLeft,\n  examTitle\n}) => {\n  // Safety checks\n  if (!question) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"Loading question...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Convert everything to safe strings\n  const questionText = question.name ? String(question.name) : 'Question text not available';\n  const answerType = question.answerType ? String(question.answerType) : 'Options';\n\n  // Process options safely\n  let options = [];\n  if (question.options) {\n    if (Array.isArray(question.options)) {\n      options = question.options.map(opt => String(opt || ''));\n    } else if (typeof question.options === 'object') {\n      options = Object.values(question.options).map(opt => String(opt || ''));\n    }\n  }\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return mins + ':' + (secs < 10 ? '0' : '') + secs;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        borderBottom: '1px solid #e5e7eb',\n        padding: '16px',\n        position: 'sticky',\n        top: 0,\n        zIndex: 50\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          maxWidth: '1200px',\n          margin: '0 auto',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              fontSize: '18px',\n              fontWeight: '600',\n              color: '#111827',\n              margin: 0\n            },\n            children: examTitle ? String(examTitle) : 'Quiz'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              fontSize: '14px',\n              color: '#6b7280',\n              margin: 0\n            },\n            children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: timeLeft <= 60 ? '#dc2626' : '#2563eb',\n            color: 'white',\n            padding: '12px 24px',\n            borderRadius: '12px',\n            fontFamily: 'monospace',\n            fontWeight: 'bold'\n          },\n          children: [\"TIME: \", formatTime(timeLeft)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        maxWidth: '800px',\n        margin: '0 auto',\n        padding: '32px 16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: 'white',\n          borderRadius: '16px',\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n          padding: '32px',\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '24px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              background: '#dbeafe',\n              color: '#1e40af',\n              padding: '8px 16px',\n              borderRadius: '20px',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '32px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: '20px',\n              fontWeight: '500',\n              color: '#111827',\n              lineHeight: '1.6',\n              margin: 0\n            },\n            children: questionText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '32px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: question.image || question.imageUrl,\n            alt: \"Question\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '400px',\n              objectFit: 'contain',\n              borderRadius: '12px',\n              border: '1px solid #e5e7eb'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            flexDirection: 'column',\n            gap: '16px'\n          },\n          children: answerType === \"Options\" && options.length > 0 ? options.map((option, index) => {\n            const letter = String.fromCharCode(65 + index);\n            const isSelected = selectedAnswer === index;\n            return /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => onAnswerSelect(index),\n              style: {\n                width: '100%',\n                textAlign: 'left',\n                padding: '16px',\n                borderRadius: '12px',\n                border: isSelected ? '2px solid #2563eb' : '2px solid #e5e7eb',\n                background: isSelected ? '#eff6ff' : 'white',\n                color: isSelected ? '#1e40af' : '#111827',\n                cursor: 'pointer',\n                transition: 'all 0.3s',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '16px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '32px',\n                  height: '32px',\n                  borderRadius: '50%',\n                  background: isSelected ? '#2563eb' : '#f3f4f6',\n                  color: isSelected ? 'white' : '#6b7280',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontWeight: 'bold',\n                  fontSize: '14px'\n                },\n                children: letter\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  flex: 1,\n                  fontWeight: '500'\n                },\n                children: option\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this), isSelected && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '24px',\n                  height: '24px',\n                  borderRadius: '50%',\n                  background: '#2563eb',\n                  color: 'white',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                },\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 19\n            }, this);\n          }) : /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: 'block',\n                fontSize: '14px',\n                fontWeight: '500',\n                color: '#374151',\n                marginBottom: '8px'\n              },\n              children: \"Your Answer:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: selectedAnswer || '',\n              onChange: e => onAnswerSelect(e.target.value),\n              placeholder: \"Type your answer here...\",\n              style: {\n                width: '100%',\n                padding: '16px',\n                border: '2px solid #e5e7eb',\n                borderRadius: '12px',\n                fontSize: '16px',\n                outline: 'none'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onPrevious,\n          disabled: questionIndex === 0,\n          style: {\n            padding: '12px 24px',\n            borderRadius: '12px',\n            fontWeight: '600',\n            border: 'none',\n            cursor: questionIndex === 0 ? 'not-allowed' : 'pointer',\n            background: questionIndex === 0 ? '#e5e7eb' : '#4b5563',\n            color: questionIndex === 0 ? '#9ca3af' : 'white'\n          },\n          children: \"\\u2190 Previous\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '14px',\n              color: '#6b7280',\n              marginBottom: '8px'\n            },\n            children: \"Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '200px',\n              height: '8px',\n              background: '#e5e7eb',\n              borderRadius: '4px',\n              overflow: 'hidden'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                height: '100%',\n                background: '#2563eb',\n                borderRadius: '4px',\n                width: (questionIndex + 1) / totalQuestions * 100 + '%',\n                transition: 'width 0.3s'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onNext,\n          style: {\n            padding: '12px 24px',\n            borderRadius: '12px',\n            fontWeight: '600',\n            border: 'none',\n            cursor: 'pointer',\n            background: '#2563eb',\n            color: 'white'\n          },\n          children: questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next →'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n\n// Simple Review Renderer Component - Safe from object rendering issues\n_c = MinimalQuizRenderer;\nconst SimpleReviewRenderer = ({\n  questions,\n  selectedOptions,\n  explanations,\n  fetchExplanation,\n  setView,\n  examData,\n  setSelectedQuestionIndex,\n  setSelectedOptions,\n  setResult,\n  setTimeUp,\n  setSecondsLeft,\n  setExplanations\n}) => {\n  if (!questions || !Array.isArray(questions)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl p-8 shadow-lg text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-xl font-bold text-red-600 mb-4\",\n          children: \"Review Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-500\",\n          children: \"No questions available for review.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 297,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 296,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-blue-600 mb-2\",\n            children: \"Answer Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-slate-600\",\n            children: \"Review your answers and get explanations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4 mb-6\",\n        children: questions.map((question, index) => {\n          // Safety check\n          if (!question || typeof question !== 'object') {\n            return null;\n          }\n\n          // Safely extract data\n          const questionText = String(question.name || '');\n          const answerType = String(question.answerType || '');\n          const correctOption = question.correctOption;\n          const correctAnswer = question.correctAnswer;\n          const userAnswer = selectedOptions[index];\n\n          // Determine if answer is correct\n          let isCorrect = false;\n          let correctAnswerText = '';\n          let userAnswerText = '';\n          if (answerType === \"Options\") {\n            isCorrect = correctOption === userAnswer;\n\n            // Get correct answer text\n            if (question.options && correctOption !== undefined) {\n              const optionValue = question.options[correctOption];\n              correctAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || correctOption || \"Unknown\");\n            } else {\n              correctAnswerText = String(correctOption || \"Unknown\");\n            }\n\n            // Get user answer text\n            if (question.options && userAnswer !== undefined) {\n              const optionValue = question.options[userAnswer];\n              userAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\n            } else {\n              userAnswerText = String(userAnswer || \"Not answered\");\n            }\n          } else {\n            isCorrect = correctAnswer === userAnswer;\n            correctAnswerText = String(correctAnswer || \"Unknown\");\n            userAnswerText = String(userAnswer || \"Not answered\");\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `rounded-lg shadow-md border-2 p-4 ${isCorrect ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\",\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-slate-800 font-medium leading-relaxed\",\n                    children: questionText\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-semibold text-slate-600\",\n                children: \"Your Answer: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`,\n                children: userAnswerText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), isCorrect ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-3 text-green-600 text-xl\",\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-3 text-red-600 text-xl\",\n                children: \"\\u2717\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 17\n            }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-semibold text-slate-600\",\n                children: \"Correct Answer: \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-green-700\",\n                children: correctAnswerText\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-3 text-green-500 text-xl\",\n                children: \"\\u2713\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 19\n            }, this), !isCorrect && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\",\n                onClick: () => {\n                  fetchExplanation(questionText, correctAnswerText, userAnswerText, question.image || question.imageUrl || '');\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\uD83D\\uDCA1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Get Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 19\n            }, this), explanations[questionText] && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-blue-600 text-lg mr-2\",\n                  children: \"\\uD83D\\uDCA1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                  className: \"font-bold text-gray-800 text-base\",\n                  children: \"Explanation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 21\n              }, this), (question.image || question.imageUrl) && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3 p-2 bg-gray-50 rounded border border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-1\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-700 text-sm font-medium\",\n                    children: \"\\uD83D\\uDCCA Reference Diagram:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 435,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: question.image || question.imageUrl,\n                    alt: \"Question diagram\",\n                    className: \"max-w-full max-h-48 object-contain rounded border border-gray-300\",\n                    style: {\n                      maxWidth: '350px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded\",\n                children: String(explanations[questionText] || '')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 19\n            }, this)]\n          }, String(question._id || index), true, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-8 py-4 bg-gray-600 hover:bg-gray-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg\",\n          onClick: () => setView(\"result\"),\n          children: \"\\u2190 Back to Results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"px-8 py-4 bg-green-600 hover:bg-green-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg\",\n          onClick: () => {\n            // Reset exam state and restart\n            setView(\"instructions\");\n            setSelectedQuestionIndex(0);\n            setSelectedOptions({});\n            setResult({});\n            setTimeUp(false);\n            setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n            setExplanations({});\n          },\n          children: \"\\uD83D\\uDD04 Retake Quiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 306,\n    columnNumber: 5\n  }, this);\n};\n_c2 = SimpleReviewRenderer;\nfunction WriteExam() {\n  _s();\n  var _result$correctAnswer, _result$correctAnswer2, _result$correctAnswer3, _result$correctAnswer4;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [result, setResult] = useState({});\n  const params = useParams();\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const [view, setView] = useState(\"instructions\");\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [startTime, setStartTime] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    width,\n    height\n  } = useWindowSize();\n  const [explanations, setExplanations] = useState({});\n  const getExamData = useCallback(async () => {\n    try {\n      setIsLoading(true);\n      dispatch(ShowLoading());\n      console.log(\"Fetching exam data for ID:\", params.id);\n      const response = await getExamById({\n        examId: params.id\n      });\n      console.log(\"Exam API Response:\", response);\n      dispatch(HideLoading());\n      setIsLoading(false);\n      if (response.success) {\n        const examData = response.data;\n\n        // Check different possible question locations\n        let questions = [];\n        if (examData !== null && examData !== void 0 && examData.questions && Array.isArray(examData.questions)) {\n          questions = examData.questions;\n        } else if (examData !== null && examData !== void 0 && examData.question && Array.isArray(examData.question)) {\n          questions = examData.question;\n        } else if (examData && Array.isArray(examData)) {\n          questions = examData;\n        }\n        console.log(\"Exam Data:\", examData);\n        console.log(\"Questions found:\", questions.length);\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\n        setQuestions(questions);\n        setExamData(examData);\n        setSecondsLeft((examData === null || examData === void 0 ? void 0 : examData.duration) || 0);\n        if (questions.length === 0) {\n          console.warn(\"No questions found in exam data\");\n          console.log(\"Full response for debugging:\", response);\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\n        }\n      } else {\n        console.error(\"API Error:\", response.message);\n        console.log(\"Full error response:\", response);\n        message.error(response.message || \"Failed to load exam data\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      setIsLoading(false);\n      console.error(\"Exception in getExamData:\", error);\n      message.error(error.message || \"Failed to load exam. Please try again.\");\n    }\n  }, [params.id, dispatch]);\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      // Check if user is available\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      const indexMap = [];\n      questions.forEach((q, idx) => {\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          indexMap.push(idx);\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      const wrongPayload = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: q.correctAnswer || q.correctOption,\n              userAnswer: userAnswerKey\n            });\n          }\n        } else if (q.answerType === \"Options\") {\n          const correctKey = q.correctOption;\n          const correctValue = q.options && q.options[correctKey] || correctKey;\n          const userValue = q.options && q.options[userAnswerKey] || userAnswerKey || \"\";\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n            wrongPayload.push({\n              question: q.name,\n              expectedAnswer: correctValue,\n              userAnswer: userValue\n            });\n          }\n        }\n      });\n\n      // Calculate time spent\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60; // Convert minutes to seconds\n\n      // Calculate score and points\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round(correctCount / totalQuestions * 100);\n      const points = correctCount * 10; // 10 points per correct answer\n\n      // Determine pass/fail based on percentage\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers: correctAnswers || [],\n        wrongAnswers: wrongAnswers || [],\n        verdict: verdict || \"Fail\",\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n      setResult(tempResult);\n      const response = await addReport({\n        exam: params.id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        // Include XP data in the result\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n        setResult(resultWithXP);\n        setView(\"result\");\n        window.scrollTo(0, 0);\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\n      } else {\n        message.error(response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await chatWithChatGPTToExplainAns({\n        question,\n        expectedAnswer,\n        userAnswer,\n        imageUrl\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        setExplanations(prev => ({\n          ...prev,\n          [question]: response.explanation\n        }));\n      } else {\n        message.error(response.error || \"Failed to fetch explanation.\");\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  };\n  const startTimer = () => {\n    const totalSeconds = (examData === null || examData === void 0 ? void 0 : examData.duration) || 0;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now()); // Record start time for XP calculation\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  };\n  useEffect(() => {\n    if (timeUp && view === \"questions\") {\n      clearInterval(intervalId);\n      calculateResult();\n    }\n  }, [timeUp, view, intervalId, calculateResult]);\n  useEffect(() => {\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\n    if (params.id) {\n      getExamData();\n    } else {\n      console.error(\"No exam ID provided in URL parameters\");\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\n      navigate('/user/quiz');\n    }\n  }, [params.id, getExamData, navigate]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n      }\n    };\n  }, [intervalId]);\n\n  // Add fullscreen class for all quiz views (instructions, questions, results)\n  useEffect(() => {\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\n      document.body.classList.add(\"quiz-fullscreen\");\n    } else {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.classList.remove(\"quiz-fullscreen\");\n    };\n  }, [view]);\n\n  // Repair function for fixing orphaned questions\n  const repairExamQuestions = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await fetch('/api/exams/repair-exam-questions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          examId: params.id\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        message.success(data.message);\n        // Reload the exam data\n        getExamData();\n      } else {\n        message.error(data.message);\n      }\n    } catch (error) {\n      message.error(\"Failed to repair exam questions\");\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Check if user is authenticated\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-10 h-10 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900 mb-4\",\n          children: \"Authentication Required\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 811,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-8\",\n          children: \"Please log in to access the exam and start your learning journey.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n          onClick: () => navigate(\"/login\"),\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 813,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 805,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 804,\n      columnNumber: 7\n    }, this);\n  }\n  return examData ? /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\",\n    children: [view === \"instructions\" && /*#__PURE__*/_jsxDEV(Instructions, {\n      examData: examData,\n      setView: setView,\n      startTimer: startTimer,\n      questions: questions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 828,\n      columnNumber: 9\n    }, this), view === \"questions\" && (isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white animate-spin\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n              className: \"opacity-25\",\n              cx: \"12\",\n              cy: \"12\",\n              r: \"10\",\n              stroke: \"currentColor\",\n              strokeWidth: \"4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 842,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n              className: \"opacity-75\",\n              fill: \"currentColor\",\n              d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 841,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 840,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-blue-800 mb-4\",\n          children: \"Loading Quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-blue-600 text-lg\",\n          children: \"Please wait while we prepare your questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 847,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 838,\n      columnNumber: 11\n    }, this) : questions.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-12 h-12 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 855,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-amber-800 mb-4\",\n          children: \"No Questions Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 860,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-amber-700 mb-6 text-lg leading-relaxed\",\n          children: \"This exam appears to have no questions. This could be due to:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 861,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"text-left text-amber-700 mb-8 space-y-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Questions not properly linked to this exam\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Database connection issues\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 866,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"\\u2022 Exam configuration problems\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 864,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: repairExamQuestions,\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD27 Repair Questions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              console.log(\"Retrying exam data fetch...\");\n              getExamData();\n            },\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\uD83D\\uDD04 Retry Loading\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 876,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/user/quiz'),\n            className: \"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\",\n            children: \"\\u2190 Back to Quiz List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 885,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 854,\n        columnNumber: 13\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 853,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(SimpleQuizRenderer, {\n      question: questions[selectedQuestionIndex],\n      questionIndex: selectedQuestionIndex,\n      totalQuestions: questions.length,\n      selectedAnswer: selectedOptions[selectedQuestionIndex],\n      onAnswerSelect: answer => setSelectedOptions({\n        ...selectedOptions,\n        [selectedQuestionIndex]: answer\n      }),\n      onNext: () => {\n        if (selectedQuestionIndex === questions.length - 1) {\n          calculateResult();\n        } else {\n          setSelectedQuestionIndex(selectedQuestionIndex + 1);\n        }\n      },\n      onPrevious: () => {\n        if (selectedQuestionIndex > 0) {\n          setSelectedQuestionIndex(selectedQuestionIndex - 1);\n        }\n      },\n      timeLeft: secondsLeft,\n      examTitle: (examData === null || examData === void 0 ? void 0 : examData.name) || \"Quiz\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 895,\n      columnNumber: 11\n    }, this)), view === \"result\" && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\",\n      children: [result.verdict === \"Pass\" && /*#__PURE__*/_jsxDEV(Confetti, {\n        width: width,\n        height: height\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 921,\n        columnNumber: 41\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `px-8 py-10 text-center relative ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\" : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500 to-green-600\" : \"bg-gradient-to-br from-amber-500 to-orange-600\"}`,\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: result.verdict === \"Pass\" ? Pass : Fail,\n                  alt: result.verdict,\n                  className: \"w-12 h-12 object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: `text-4xl font-black mb-4 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"}`,\n                children: result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\",\n                children: result.verdict === \"Pass\" ? \"You've mastered this exam with flying colors!\" : \"Every challenge makes you stronger. Try again!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 926,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 961,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-blue-600 mb-2 tracking-tight\",\n                    children: [Math.round((((_result$correctAnswer = result.correctAnswers) === null || _result$correctAnswer === void 0 ? void 0 : _result$correctAnswer.length) || 0) / questions.length * 100), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 963,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-blue-700/80 uppercase tracking-wider\",\n                    children: \"Your Score\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 966,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-4xl font-black text-emerald-600 mb-2 tracking-tight\",\n                    children: [((_result$correctAnswer2 = result.correctAnswers) === null || _result$correctAnswer2 === void 0 ? void 0 : _result$correctAnswer2.length) || 0, \"/\", questions.length]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 974,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\",\n                    children: \"Correct\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 977,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 973,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${result.verdict === \"Pass\" ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\" : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"} to-transparent`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-4xl font-black mb-2 tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                    children: result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `text-sm font-bold uppercase tracking-wider ${result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"}`,\n                    children: result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 996,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 990,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 958,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative bg-slate-100 rounded-2xl p-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-bold text-slate-700 mb-1\",\n                    children: \"Performance Overview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-slate-500\",\n                    children: \"Your achievement level\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1010,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${result.verdict === \"Pass\" ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\" : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"}`,\n                      style: {\n                        width: `${(((_result$correctAnswer3 = result.correctAnswers) === null || _result$correctAnswer3 === void 0 ? void 0 : _result$correctAnswer3.length) || 0) / questions.length * 100}%`\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1022,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1014,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex justify-between items-center mt-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"0%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1026,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `text-lg font-black tracking-tight ${result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"}`,\n                      children: [Math.round((((_result$correctAnswer4 = result.correctAnswers) === null || _result$correctAnswer4 === void 0 ? void 0 : _result$correctAnswer4.length) || 0) / questions.length * 100), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1027,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs font-medium text-slate-500\",\n                      children: \"100%\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1032,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1025,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1012,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 17\n            }, this), result.xpData && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: /*#__PURE__*/_jsxDEV(XPResultDisplay, {\n                xpData: result.xpData\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                onClick: () => setView(\"review\"),\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1051,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"relative\",\n                  children: \"Review Answers\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1047,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1046,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 923,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 920,\n      columnNumber: 9\n    }, this), view === \"review\" && /*#__PURE__*/_jsxDEV(SimpleReviewRenderer, {\n      questions: questions,\n      selectedOptions: selectedOptions,\n      explanations: explanations,\n      fetchExplanation: fetchExplanation,\n      setView: setView,\n      examData: examData,\n      setSelectedQuestionIndex: setSelectedQuestionIndex,\n      setSelectedOptions: setSelectedOptions,\n      setResult: setResult,\n      setTimeUp: setTimeUp,\n      setSecondsLeft: setSecondsLeft,\n      setExplanations: setExplanations\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1064,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 825,\n    columnNumber: 5\n  }, this) : null;\n}\n_s(WriteExam, \"O6w+2GWMw4fZ1y7nA7sammn5z5g=\", false, function () {\n  return [useParams, useDispatch, useNavigate, useSelector, useWindowSize];\n});\n_c3 = WriteExam;\nexport default WriteExam;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MinimalQuizRenderer\");\n$RefreshReg$(_c2, \"SimpleReviewRenderer\");\n$RefreshReg$(_c3, \"WriteExam\");", "map": {"version": 3, "names": ["message", "React", "useCallback", "useEffect", "useState", "useDispatch", "useSelector", "useNavigate", "useParams", "getExamById", "addReport", "HideLoading", "ShowLoading", "Instructions", "Pass", "Fail", "Confetti", "useWindowSize", "PassSound", "FailSound", "chatWithChatGPTToGetAns", "chatWithChatGPTToExplainAns", "XPResultDisplay", "jsxDEV", "_jsxDEV", "MinimalQuiz<PERSON><PERSON><PERSON>", "question", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerSelect", "onNext", "onPrevious", "timeLeft", "examTitle", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "questionText", "name", "String", "answerType", "options", "Array", "isArray", "map", "opt", "Object", "values", "formatTime", "seconds", "mins", "Math", "floor", "secs", "style", "minHeight", "background", "borderBottom", "padding", "position", "top", "zIndex", "max<PERSON><PERSON><PERSON>", "margin", "display", "justifyContent", "alignItems", "fontSize", "fontWeight", "color", "borderRadius", "fontFamily", "boxShadow", "marginBottom", "lineHeight", "image", "imageUrl", "textAlign", "src", "alt", "maxHeight", "objectFit", "border", "flexDirection", "gap", "length", "option", "index", "letter", "fromCharCode", "isSelected", "onClick", "width", "cursor", "transition", "height", "flex", "type", "value", "onChange", "e", "target", "placeholder", "outline", "disabled", "overflow", "_c", "SimpleReviewRenderer", "questions", "selectedOptions", "explanations", "fetchExplanation", "<PERSON><PERSON><PERSON><PERSON>", "examData", "setSelectedQuestionIndex", "setSelectedOptions", "setResult", "setTimeUp", "setSecondsLeft", "setExplanations", "className", "correctOption", "<PERSON><PERSON><PERSON><PERSON>", "userAnswer", "isCorrect", "correctAnswerText", "userAnswerText", "undefined", "optionValue", "_id", "duration", "_c2", "WriteExam", "_s", "_result$correctAnswer", "_result$correctAnswer2", "_result$correctAnswer3", "_result$correctAnswer4", "setExamData", "setQuestions", "selectedQuestionIndex", "result", "params", "dispatch", "navigate", "view", "secondsLeft", "timeUp", "intervalId", "setIntervalId", "isLoading", "setIsLoading", "startTime", "setStartTime", "user", "state", "getExamData", "console", "log", "id", "response", "examId", "success", "data", "keys", "warn", "warning", "error", "checkFreeTextAnswers", "payload", "calculateResult", "freeTextPayload", "indexMap", "for<PERSON>ach", "q", "idx", "push", "expectedAnswer", "gptResults", "gptMap", "r", "reason", "correctAnswers", "wrongAnswers", "wrongPayload", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "correctValue", "userValue", "timeSpent", "Date", "now", "totalTimeAllowed", "correctCount", "scorePercentage", "round", "points", "passingPercentage", "passingMarks", "verdict", "tempResult", "score", "exam", "resultWithXP", "xpData", "window", "scrollTo", "Audio", "play", "prev", "explanation", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "repairExamQuestions", "fetch", "method", "headers", "localStorage", "getItem", "JSON", "stringify", "json", "fill", "viewBox", "fillRule", "d", "clipRule", "cx", "cy", "stroke", "strokeWidth", "SimpleQuiz<PERSON><PERSON><PERSON>", "answer", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/WriteExam/index.js"], "sourcesContent": ["import { message } from \"antd\";\r\nimport React, { useCallback, useEffect, useState } from \"react\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useNavigate, useParams } from \"react-router-dom\";\r\nimport { getExamById } from \"../../../apicalls/exams\";\r\nimport { addReport } from \"../../../apicalls/reports\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport Instructions from \"./Instructions\";\r\nimport Pass from \"../../../assets/pass.gif\";\r\nimport Fail from \"../../../assets/fail.gif\";\r\nimport Confetti from \"react-confetti\";\r\nimport useWindowSize from \"react-use/lib/useWindowSize\";\r\nimport PassSound from \"../../../assets/pass.mp3\";\r\nimport FailSound from \"../../../assets/fail.mp3\";\r\nimport { chatWithChatGPTToGetAns, chatWithChatGPTToExplainAns } from \"../../../apicalls/chat\";\r\nimport XPResultDisplay from \"../../../components/modern/XPResultDisplay\";\r\n\r\n// Minimal Safe Quiz Component\r\nconst MinimalQuizRenderer = ({ question, questionIndex, totalQuestions, selectedAnswer, onAnswerSelect, onNext, onPrevious, timeLeft, examTitle }) => {\r\n  // Safety checks\r\n  if (!question) {\r\n    return <div>Loading question...</div>;\r\n  }\r\n\r\n  // Convert everything to safe strings\r\n  const questionText = question.name ? String(question.name) : 'Question text not available';\r\n  const answerType = question.answerType ? String(question.answerType) : 'Options';\r\n\r\n  // Process options safely\r\n  let options = [];\r\n  if (question.options) {\r\n    if (Array.isArray(question.options)) {\r\n      options = question.options.map(opt => String(opt || ''));\r\n    } else if (typeof question.options === 'object') {\r\n      options = Object.values(question.options).map(opt => String(opt || ''));\r\n    }\r\n  }\r\n\r\n  const formatTime = (seconds) => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = seconds % 60;\r\n    return mins + ':' + (secs < 10 ? '0' : '') + secs;\r\n  };\r\n\r\n  return (\r\n    <div style={{ minHeight: '100vh', background: 'linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 100%)' }}>\r\n      {/* Simple Header */}\r\n      <div style={{\r\n        background: 'white',\r\n        borderBottom: '1px solid #e5e7eb',\r\n        padding: '16px',\r\n        position: 'sticky',\r\n        top: 0,\r\n        zIndex: 50\r\n      }}>\r\n        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          <div>\r\n            <h1 style={{ fontSize: '18px', fontWeight: '600', color: '#111827', margin: 0 }}>\r\n              {examTitle ? String(examTitle) : 'Quiz'}\r\n            </h1>\r\n            <p style={{ fontSize: '14px', color: '#6b7280', margin: 0 }}>\r\n              Question {questionIndex + 1} of {totalQuestions}\r\n            </p>\r\n          </div>\r\n\r\n          <div style={{\r\n            background: timeLeft <= 60 ? '#dc2626' : '#2563eb',\r\n            color: 'white',\r\n            padding: '12px 24px',\r\n            borderRadius: '12px',\r\n            fontFamily: 'monospace',\r\n            fontWeight: 'bold'\r\n          }}>\r\n            TIME: {formatTime(timeLeft)}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Question Content */}\r\n      <div style={{ maxWidth: '800px', margin: '0 auto', padding: '32px 16px' }}>\r\n        <div style={{\r\n          background: 'white',\r\n          borderRadius: '16px',\r\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\r\n          padding: '32px',\r\n          marginBottom: '24px'\r\n        }}>\r\n          {/* Question Number Badge */}\r\n          <div style={{ marginBottom: '24px' }}>\r\n            <span style={{\r\n              background: '#dbeafe',\r\n              color: '#1e40af',\r\n              padding: '8px 16px',\r\n              borderRadius: '20px',\r\n              fontSize: '14px',\r\n              fontWeight: '600'\r\n            }}>\r\n              Question {questionIndex + 1} of {totalQuestions}\r\n            </span>\r\n          </div>\r\n\r\n          {/* Question Text */}\r\n          <div style={{ marginBottom: '32px' }}>\r\n            <h2 style={{\r\n              fontSize: '20px',\r\n              fontWeight: '500',\r\n              color: '#111827',\r\n              lineHeight: '1.6',\r\n              margin: 0\r\n            }}>\r\n              {questionText}\r\n            </h2>\r\n          </div>\r\n\r\n          {/* Image */}\r\n          {(question.image || question.imageUrl) && (\r\n            <div style={{ marginBottom: '32px', textAlign: 'center' }}>\r\n              <img\r\n                src={question.image || question.imageUrl}\r\n                alt=\"Question\"\r\n                style={{\r\n                  maxWidth: '100%',\r\n                  maxHeight: '400px',\r\n                  objectFit: 'contain',\r\n                  borderRadius: '12px',\r\n                  border: '1px solid #e5e7eb'\r\n                }}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {/* Answer Options */}\r\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>\r\n            {answerType === \"Options\" && options.length > 0 ? (\r\n              options.map((option, index) => {\r\n                const letter = String.fromCharCode(65 + index);\r\n                const isSelected = selectedAnswer === index;\r\n\r\n                return (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => onAnswerSelect(index)}\r\n                    style={{\r\n                      width: '100%',\r\n                      textAlign: 'left',\r\n                      padding: '16px',\r\n                      borderRadius: '12px',\r\n                      border: isSelected ? '2px solid #2563eb' : '2px solid #e5e7eb',\r\n                      background: isSelected ? '#eff6ff' : 'white',\r\n                      color: isSelected ? '#1e40af' : '#111827',\r\n                      cursor: 'pointer',\r\n                      transition: 'all 0.3s',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: '16px'\r\n                    }}\r\n                  >\r\n                    <div style={{\r\n                      width: '32px',\r\n                      height: '32px',\r\n                      borderRadius: '50%',\r\n                      background: isSelected ? '#2563eb' : '#f3f4f6',\r\n                      color: isSelected ? 'white' : '#6b7280',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      justifyContent: 'center',\r\n                      fontWeight: 'bold',\r\n                      fontSize: '14px'\r\n                    }}>\r\n                      {letter}\r\n                    </div>\r\n                    <span style={{ flex: 1, fontWeight: '500' }}>{option}</span>\r\n                    {isSelected && (\r\n                      <div style={{\r\n                        width: '24px',\r\n                        height: '24px',\r\n                        borderRadius: '50%',\r\n                        background: '#2563eb',\r\n                        color: 'white',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        justifyContent: 'center'\r\n                      }}>\r\n                        ✓\r\n                      </div>\r\n                    )}\r\n                  </button>\r\n                );\r\n              })\r\n            ) : (\r\n              <div>\r\n                <label style={{\r\n                  display: 'block',\r\n                  fontSize: '14px',\r\n                  fontWeight: '500',\r\n                  color: '#374151',\r\n                  marginBottom: '8px'\r\n                }}>\r\n                  Your Answer:\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={selectedAnswer || ''}\r\n                  onChange={(e) => onAnswerSelect(e.target.value)}\r\n                  placeholder=\"Type your answer here...\"\r\n                  style={{\r\n                    width: '100%',\r\n                    padding: '16px',\r\n                    border: '2px solid #e5e7eb',\r\n                    borderRadius: '12px',\r\n                    fontSize: '16px',\r\n                    outline: 'none'\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n          <button\r\n            onClick={onPrevious}\r\n            disabled={questionIndex === 0}\r\n            style={{\r\n              padding: '12px 24px',\r\n              borderRadius: '12px',\r\n              fontWeight: '600',\r\n              border: 'none',\r\n              cursor: questionIndex === 0 ? 'not-allowed' : 'pointer',\r\n              background: questionIndex === 0 ? '#e5e7eb' : '#4b5563',\r\n              color: questionIndex === 0 ? '#9ca3af' : 'white'\r\n            }}\r\n          >\r\n            ← Previous\r\n          </button>\r\n\r\n          <div style={{ textAlign: 'center' }}>\r\n            <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px' }}>Progress</div>\r\n            <div style={{\r\n              width: '200px',\r\n              height: '8px',\r\n              background: '#e5e7eb',\r\n              borderRadius: '4px',\r\n              overflow: 'hidden'\r\n            }}>\r\n              <div\r\n                style={{\r\n                  height: '100%',\r\n                  background: '#2563eb',\r\n                  borderRadius: '4px',\r\n                  width: ((questionIndex + 1) / totalQuestions) * 100 + '%',\r\n                  transition: 'width 0.3s'\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <button\r\n            onClick={onNext}\r\n            style={{\r\n              padding: '12px 24px',\r\n              borderRadius: '12px',\r\n              fontWeight: '600',\r\n              border: 'none',\r\n              cursor: 'pointer',\r\n              background: '#2563eb',\r\n              color: 'white'\r\n            }}\r\n          >\r\n            {questionIndex === totalQuestions - 1 ? 'Submit Quiz' : 'Next →'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Simple Review Renderer Component - Safe from object rendering issues\r\nconst SimpleReviewRenderer = ({\r\n  questions,\r\n  selectedOptions,\r\n  explanations,\r\n  fetchExplanation,\r\n  setView,\r\n  examData,\r\n  setSelectedQuestionIndex,\r\n  setSelectedOptions,\r\n  setResult,\r\n  setTimeUp,\r\n  setSecondsLeft,\r\n  setExplanations\r\n}) => {\r\n  if (!questions || !Array.isArray(questions)) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center\">\r\n        <div className=\"bg-white rounded-xl p-8 shadow-lg text-center\">\r\n          <h3 className=\"text-xl font-bold text-red-600 mb-4\">Review Error</h3>\r\n          <p className=\"text-red-500\">No questions available for review.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-6\">\r\n      <div className=\"max-w-4xl mx-auto px-4\">\r\n        {/* Header */}\r\n        <div className=\"text-center mb-6\">\r\n          <div className=\"bg-white/95 backdrop-blur-md rounded-xl p-6 shadow-lg border border-slate-200/50\">\r\n            <h2 className=\"text-2xl font-bold text-blue-600 mb-2\">Answer Review</h2>\r\n            <p className=\"text-slate-600\">Review your answers and get explanations</p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Questions Review */}\r\n        <div className=\"space-y-4 mb-6\">\r\n          {questions.map((question, index) => {\r\n            // Safety check\r\n            if (!question || typeof question !== 'object') {\r\n              return null;\r\n            }\r\n\r\n            // Safely extract data\r\n            const questionText = String(question.name || '');\r\n            const answerType = String(question.answerType || '');\r\n            const correctOption = question.correctOption;\r\n            const correctAnswer = question.correctAnswer;\r\n            const userAnswer = selectedOptions[index];\r\n\r\n            // Determine if answer is correct\r\n            let isCorrect = false;\r\n            let correctAnswerText = '';\r\n            let userAnswerText = '';\r\n\r\n            if (answerType === \"Options\") {\r\n              isCorrect = correctOption === userAnswer;\r\n\r\n              // Get correct answer text\r\n              if (question.options && correctOption !== undefined) {\r\n                const optionValue = question.options[correctOption];\r\n                correctAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || correctOption || \"Unknown\");\r\n              } else {\r\n                correctAnswerText = String(correctOption || \"Unknown\");\r\n              }\r\n\r\n              // Get user answer text\r\n              if (question.options && userAnswer !== undefined) {\r\n                const optionValue = question.options[userAnswer];\r\n                userAnswerText = typeof optionValue === 'string' ? optionValue : String(optionValue || userAnswer || \"Not answered\");\r\n              } else {\r\n                userAnswerText = String(userAnswer || \"Not answered\");\r\n              }\r\n            } else {\r\n              isCorrect = correctAnswer === userAnswer;\r\n              correctAnswerText = String(correctAnswer || \"Unknown\");\r\n              userAnswerText = String(userAnswer || \"Not answered\");\r\n            }\r\n\r\n            return (\r\n              <div\r\n                key={String(question._id || index)}\r\n                className={`rounded-lg shadow-md border-2 p-4 ${\r\n                  isCorrect\r\n                    ? 'bg-green-50 border-green-300'\r\n                    : 'bg-red-50 border-red-300'\r\n                }`}\r\n              >\r\n                {/* Question */}\r\n                <div className=\"mb-3\">\r\n                  <div className=\"flex items-start space-x-3\">\r\n                    <div className=\"w-8 h-8 rounded-lg flex items-center justify-center font-bold text-white text-sm bg-blue-600 flex-shrink-0 mt-1\">\r\n                      {index + 1}\r\n                    </div>\r\n                    <div className=\"flex-1\">\r\n                      <p className=\"text-slate-800 font-medium leading-relaxed\">{questionText}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Your Answer */}\r\n                <div className=\"mb-2\">\r\n                  <span className=\"text-sm font-semibold text-slate-600\">Your Answer: </span>\r\n                  <span className={`font-medium ${isCorrect ? 'text-green-700' : 'text-red-700'}`}>\r\n                    {userAnswerText}\r\n                  </span>\r\n                  {isCorrect ? (\r\n                    <span className=\"ml-3 text-green-600 text-xl\">✓</span>\r\n                  ) : (\r\n                    <span className=\"ml-3 text-red-600 text-xl\">✗</span>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Correct Answer (only for wrong answers) */}\r\n                {!isCorrect && (\r\n                  <div className=\"mb-2\">\r\n                    <span className=\"text-sm font-semibold text-slate-600\">Correct Answer: </span>\r\n                    <span className=\"font-medium text-green-700\">{correctAnswerText}</span>\r\n                    <span className=\"ml-3 text-green-500 text-xl\">✓</span>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation Button (only for wrong answers) */}\r\n                {!isCorrect && (\r\n                  <div className=\"mt-2\">\r\n                    <button\r\n                      className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2\"\r\n                      onClick={() => {\r\n                        fetchExplanation(\r\n                          questionText,\r\n                          correctAnswerText,\r\n                          userAnswerText,\r\n                          question.image || question.imageUrl || ''\r\n                        );\r\n                      }}\r\n                    >\r\n                      <span>💡</span>\r\n                      <span>Get Explanation</span>\r\n                    </button>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Explanation Display */}\r\n                {explanations[questionText] && (\r\n                  <div className=\"mt-3 p-3 bg-white rounded-lg border-l-4 border-l-blue-500 shadow-sm border border-gray-200\">\r\n                    <div className=\"flex items-center mb-2\">\r\n                      <span className=\"text-blue-600 text-lg mr-2\">💡</span>\r\n                      <h6 className=\"font-bold text-gray-800 text-base\">Explanation</h6>\r\n                    </div>\r\n\r\n                    {/* Image if present */}\r\n                    {(question.image || question.imageUrl) && (\r\n                      <div className=\"mb-3 p-2 bg-gray-50 rounded border border-gray-200\">\r\n                        <div className=\"flex items-center mb-1\">\r\n                          <span className=\"text-gray-700 text-sm font-medium\">📊 Reference Diagram:</span>\r\n                        </div>\r\n                        <div className=\"flex justify-center\">\r\n                          <img\r\n                            src={question.image || question.imageUrl}\r\n                            alt=\"Question diagram\"\r\n                            className=\"max-w-full max-h-48 object-contain rounded border border-gray-300\"\r\n                            style={{ maxWidth: '350px' }}\r\n                          />\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n\r\n                    <div className=\"text-sm text-gray-800 leading-relaxed bg-gray-50 p-2 rounded\">\r\n                      {String(explanations[questionText] || '')}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        {/* Navigation */}\r\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\r\n          <button\r\n            className=\"px-8 py-4 bg-gray-600 hover:bg-gray-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg\"\r\n            onClick={() => setView(\"result\")}\r\n          >\r\n            ← Back to Results\r\n          </button>\r\n\r\n          <button\r\n            className=\"px-8 py-4 bg-green-600 hover:bg-green-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg\"\r\n            onClick={() => {\r\n              // Reset exam state and restart\r\n              setView(\"instructions\");\r\n              setSelectedQuestionIndex(0);\r\n              setSelectedOptions({});\r\n              setResult({});\r\n              setTimeUp(false);\r\n              setSecondsLeft(examData?.duration || 0);\r\n              setExplanations({});\r\n            }}\r\n          >\r\n            🔄 Retake Quiz\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nfunction WriteExam() {\r\n  const [examData, setExamData] = useState(null);\r\n  const [questions, setQuestions] = useState([]);\r\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\r\n  const [selectedOptions, setSelectedOptions] = useState({});\r\n  const [result, setResult] = useState({});\r\n  const params = useParams();\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const [view, setView] = useState(\"instructions\");\r\n  const [secondsLeft, setSecondsLeft] = useState(0);\r\n  const [timeUp, setTimeUp] = useState(false);\r\n  const [intervalId, setIntervalId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [startTime, setStartTime] = useState(null);\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const { width, height } = useWindowSize();\r\n  const [explanations, setExplanations] = useState({});\r\n\r\n  const getExamData = useCallback(async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      dispatch(ShowLoading());\r\n      console.log(\"Fetching exam data for ID:\", params.id);\r\n\r\n      const response = await getExamById({ examId: params.id });\r\n      console.log(\"Exam API Response:\", response);\r\n\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n\r\n      if (response.success) {\r\n        const examData = response.data;\r\n\r\n        // Check different possible question locations\r\n        let questions = [];\r\n        if (examData?.questions && Array.isArray(examData.questions)) {\r\n          questions = examData.questions;\r\n        } else if (examData?.question && Array.isArray(examData.question)) {\r\n          questions = examData.question;\r\n        } else if (examData && Array.isArray(examData)) {\r\n          questions = examData;\r\n        }\r\n\r\n        console.log(\"Exam Data:\", examData);\r\n        console.log(\"Questions found:\", questions.length);\r\n        console.log(\"Exam Data structure:\", Object.keys(examData || {}));\r\n\r\n        setQuestions(questions);\r\n        setExamData(examData);\r\n        setSecondsLeft(examData?.duration || 0);\r\n\r\n        if (questions.length === 0) {\r\n          console.warn(\"No questions found in exam data\");\r\n          console.log(\"Full response for debugging:\", response);\r\n          message.warning(\"This exam has no questions. Please contact your instructor.\");\r\n        }\r\n      } else {\r\n        console.error(\"API Error:\", response.message);\r\n        console.log(\"Full error response:\", response);\r\n        message.error(response.message || \"Failed to load exam data\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      setIsLoading(false);\r\n      console.error(\"Exception in getExamData:\", error);\r\n      message.error(error.message || \"Failed to load exam. Please try again.\");\r\n    }\r\n  }, [params.id, dispatch]);\r\n\r\n  const checkFreeTextAnswers = async (payload) => {\r\n    if (!payload.length) return [];\r\n    const { data } = await chatWithChatGPTToGetAns(payload);\r\n    return data;\r\n  };\r\n\r\n  const calculateResult = useCallback(async () => {\r\n    try {\r\n      // Check if user is available\r\n      if (!user || !user._id) {\r\n        message.error(\"User not found. Please log in again.\");\r\n        navigate(\"/login\");\r\n        return;\r\n      }\r\n\r\n      dispatch(ShowLoading());\r\n\r\n      const freeTextPayload = [];\r\n      const indexMap = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          indexMap.push(idx);\r\n          freeTextPayload.push({\r\n            question: q.name,\r\n            expectedAnswer: q.correctAnswer || q.correctOption,\r\n            userAnswer: selectedOptions[idx] || \"\",\r\n          });\r\n        }\r\n      });\r\n\r\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\r\n      const gptMap = {};\r\n\r\n      gptResults.forEach((r) => {\r\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = r.result;\r\n        } else if (typeof r.isCorrect === \"boolean\") {\r\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\r\n        }\r\n      });\r\n\r\n      const correctAnswers = [];\r\n      const wrongAnswers = [];\r\n      const wrongPayload = [];\r\n\r\n      questions.forEach((q, idx) => {\r\n        const userAnswerKey = selectedOptions[idx] || \"\";\r\n\r\n        if (q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\r\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\r\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: q.correctAnswer || q.correctOption,\r\n              userAnswer: userAnswerKey,\r\n            });\r\n          }\r\n        } else if (q.answerType === \"Options\") {\r\n          const correctKey = q.correctOption;\r\n          const correctValue = (q.options && q.options[correctKey]) || correctKey;\r\n          const userValue = (q.options && q.options[userAnswerKey]) || userAnswerKey || \"\";\r\n\r\n          const isCorrect = correctKey === userAnswerKey;\r\n          const enriched = { ...q, userAnswer: userAnswerKey };\r\n\r\n          if (isCorrect) {\r\n            correctAnswers.push(enriched);\r\n          } else {\r\n            wrongAnswers.push(enriched);\r\n            wrongPayload.push({\r\n              question: q.name,\r\n              expectedAnswer: correctValue,\r\n              userAnswer: userValue,\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n      // Calculate time spent\r\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\r\n      const totalTimeAllowed = (examData?.duration || 0) * 60; // Convert minutes to seconds\r\n\r\n      // Calculate score and points\r\n      const totalQuestions = questions.length;\r\n      const correctCount = correctAnswers.length;\r\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\r\n      const points = correctCount * 10; // 10 points per correct answer\r\n\r\n      // Determine pass/fail based on percentage\r\n      const passingPercentage = examData.passingMarks || 70; // Default 70%\r\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\r\n\r\n      const tempResult = {\r\n        correctAnswers: correctAnswers || [],\r\n        wrongAnswers: wrongAnswers || [],\r\n        verdict: verdict || \"Fail\",\r\n        score: scorePercentage,\r\n        points: points,\r\n        totalQuestions: totalQuestions,\r\n        timeSpent: timeSpent,\r\n        totalTimeAllowed: totalTimeAllowed\r\n      };\r\n\r\n      setResult(tempResult);\r\n\r\n      const response = await addReport({\r\n        exam: params.id,\r\n        result: tempResult,\r\n        user: user._id,\r\n      });\r\n\r\n      if (response.success) {\r\n        // Include XP data in the result\r\n        const resultWithXP = {\r\n          ...tempResult,\r\n          xpData: response.xpData\r\n        };\r\n        setResult(resultWithXP);\r\n\r\n        setView(\"result\");\r\n        window.scrollTo(0, 0);\r\n        new Audio(verdict === \"Pass\" ? PassSound : FailSound).play();\r\n      } else {\r\n        message.error(response.message);\r\n      }\r\n      dispatch(HideLoading());\r\n\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  }, [questions, selectedOptions, examData, params.id, user, navigate, dispatch]);\r\n\r\n  const fetchExplanation = async (question, expectedAnswer, userAnswer, imageUrl) => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await chatWithChatGPTToExplainAns({ question, expectedAnswer, userAnswer, imageUrl });\r\n      dispatch(HideLoading());\r\n\r\n      if (response.success) {\r\n        setExplanations((prev) => ({ ...prev, [question]: response.explanation }));\r\n      } else {\r\n        message.error(response.error || \"Failed to fetch explanation.\");\r\n      }\r\n    } catch (error) {\r\n      dispatch(HideLoading());\r\n      message.error(error.message);\r\n    }\r\n  };\r\n\r\n  const startTimer = () => {\r\n    const totalSeconds = examData?.duration || 0;\r\n    setSecondsLeft(totalSeconds);\r\n    setStartTime(Date.now()); // Record start time for XP calculation\r\n\r\n    const newIntervalId = setInterval(() => {\r\n      setSecondsLeft((prevSeconds) => {\r\n        if (prevSeconds > 0) {\r\n          return prevSeconds - 1;\r\n        } else {\r\n          setTimeUp(true);\r\n          return 0;\r\n        }\r\n      });\r\n    }, 1000);\r\n    setIntervalId(newIntervalId);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (timeUp && view === \"questions\") {\r\n      clearInterval(intervalId);\r\n      calculateResult();\r\n    }\r\n  }, [timeUp, view, intervalId, calculateResult]);\r\n\r\n  useEffect(() => {\r\n    console.log(\"WriteExam useEffect - params.id:\", params.id);\r\n    if (params.id) {\r\n      getExamData();\r\n    } else {\r\n      console.error(\"No exam ID provided in URL parameters\");\r\n      message.error(\"Invalid exam ID. Please select a quiz from the list.\");\r\n      navigate('/user/quiz');\r\n    }\r\n  }, [params.id, getExamData, navigate]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      if (intervalId) {\r\n        clearInterval(intervalId);\r\n      }\r\n    };\r\n  }, [intervalId]);\r\n\r\n  // Add fullscreen class for all quiz views (instructions, questions, results)\r\n  useEffect(() => {\r\n    if (view === \"instructions\" || view === \"questions\" || view === \"result\") {\r\n      document.body.classList.add(\"quiz-fullscreen\");\r\n    } else {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      document.body.classList.remove(\"quiz-fullscreen\");\r\n    };\r\n  }, [view]);\r\n\r\n  // Repair function for fixing orphaned questions\r\n  const repairExamQuestions = async () => {\r\n    try {\r\n      dispatch(ShowLoading());\r\n      const response = await fetch('/api/exams/repair-exam-questions', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n        },\r\n        body: JSON.stringify({ examId: params.id })\r\n      });\r\n\r\n      const data = await response.json();\r\n      if (data.success) {\r\n        message.success(data.message);\r\n        // Reload the exam data\r\n        getExamData();\r\n      } else {\r\n        message.error(data.message);\r\n      }\r\n    } catch (error) {\r\n      message.error(\"Failed to repair exam questions\");\r\n    } finally {\r\n      dispatch(HideLoading());\r\n    }\r\n  };\r\n\r\n  // Check if user is authenticated\r\n  if (!user) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex justify-center items-center\">\r\n        <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-blue-100 p-12 text-center max-w-md mx-4\">\r\n          <div className=\"w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6\">\r\n            <svg className=\"w-10 h-10 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clipRule=\"evenodd\" />\r\n            </svg>\r\n          </div>\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Authentication Required</h2>\r\n          <p className=\"text-gray-600 mb-8\">Please log in to access the exam and start your learning journey.</p>\r\n          <button\r\n            className=\"w-full px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n            onClick={() => navigate(\"/login\")}\r\n          >\r\n            Go to Login\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return examData ? (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\r\n\r\n      {view === \"instructions\" && (\r\n        <Instructions\r\n          examData={examData}\r\n          setView={setView}\r\n          startTimer={startTimer}\r\n          questions={questions}\r\n        />\r\n      )}\r\n\r\n      {view === \"questions\" && (\r\n        isLoading ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-blue-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg animate-pulse\">\r\n                <svg className=\"w-12 h-12 text-white animate-spin\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-blue-800 mb-4\">Loading Quiz...</h3>\r\n              <p className=\"text-blue-600 text-lg\">\r\n                Please wait while we prepare your questions.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        ) : questions.length === 0 ? (\r\n          <div className=\"min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 flex items-center justify-center\">\r\n            <div className=\"bg-white/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-amber-200 max-w-lg mx-4 text-center\">\r\n              <div className=\"w-24 h-24 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg\">\r\n                <svg className=\"w-12 h-12 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-2xl font-bold text-amber-800 mb-4\">No Questions Found</h3>\r\n              <p className=\"text-amber-700 mb-6 text-lg leading-relaxed\">\r\n                This exam appears to have no questions. This could be due to:\r\n              </p>\r\n              <ul className=\"text-left text-amber-700 mb-8 space-y-2\">\r\n                <li>• Questions not properly linked to this exam</li>\r\n                <li>• Database connection issues</li>\r\n                <li>• Exam configuration problems</li>\r\n              </ul>\r\n              <div className=\"space-y-3\">\r\n                <button\r\n                  onClick={repairExamQuestions}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-amber-500 to-orange-500 text-white rounded-xl font-bold text-lg hover:from-amber-600 hover:to-orange-600 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔧 Repair Questions\r\n                </button>\r\n                <button\r\n                  onClick={() => {\r\n                    console.log(\"Retrying exam data fetch...\");\r\n                    getExamData();\r\n                  }}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-bold text-lg hover:from-blue-600 hover:to-blue-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  🔄 Retry Loading\r\n                </button>\r\n                <button\r\n                  onClick={() => navigate('/user/quiz')}\r\n                  className=\"w-full px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-bold text-lg hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-300 shadow-lg\"\r\n                >\r\n                  ← Back to Quiz List\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <SimpleQuizRenderer\r\n            question={questions[selectedQuestionIndex]}\r\n            questionIndex={selectedQuestionIndex}\r\n            totalQuestions={questions.length}\r\n            selectedAnswer={selectedOptions[selectedQuestionIndex]}\r\n            onAnswerSelect={(answer) => setSelectedOptions({...selectedOptions, [selectedQuestionIndex]: answer})}\r\n            onNext={() => {\r\n              if (selectedQuestionIndex === questions.length - 1) {\r\n                calculateResult();\r\n              } else {\r\n                setSelectedQuestionIndex(selectedQuestionIndex + 1);\r\n              }\r\n            }}\r\n            onPrevious={() => {\r\n              if (selectedQuestionIndex > 0) {\r\n                setSelectedQuestionIndex(selectedQuestionIndex - 1);\r\n              }\r\n            }}\r\n            timeLeft={secondsLeft}\r\n            examTitle={examData?.name || \"Quiz\"}\r\n          />\r\n        )\r\n      )}\r\n\r\n      {view === \"result\" && (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 py-8\">\r\n          {result.verdict === \"Pass\" && <Confetti width={width} height={height} />}\r\n\r\n          <div className=\"max-w-4xl mx-auto px-4\">\r\n            <div className=\"bg-white/95 backdrop-blur-md rounded-2xl shadow-xl border border-slate-200/50 overflow-hidden\">\r\n              {/* Modern Header */}\r\n              <div className={`px-8 py-10 text-center relative ${\r\n                result.verdict === \"Pass\"\r\n                  ? \"bg-gradient-to-br from-emerald-500/10 via-green-500/5 to-teal-500/10\"\r\n                  : \"bg-gradient-to-br from-amber-500/10 via-orange-500/5 to-red-500/10\"\r\n              }`}>\r\n                <div className=\"relative\">\r\n                  <div className={`w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500 to-green-600\"\r\n                      : \"bg-gradient-to-br from-amber-500 to-orange-600\"\r\n                  }`}>\r\n                    <img\r\n                      src={result.verdict === \"Pass\" ? Pass : Fail}\r\n                      alt={result.verdict}\r\n                      className=\"w-12 h-12 object-contain\"\r\n                    />\r\n                  </div>\r\n                  <h1 className={`text-4xl font-black mb-4 tracking-tight ${\r\n                    result.verdict === \"Pass\" ? \"text-emerald-700\" : \"text-amber-700\"\r\n                  }`}>\r\n                    {result.verdict === \"Pass\" ? \"Excellent Work!\" : \"Keep Pushing!\"}\r\n                  </h1>\r\n                  <p className=\"text-xl text-slate-600 font-medium max-w-md mx-auto leading-relaxed\">\r\n                    {result.verdict === \"Pass\"\r\n                      ? \"You've mastered this exam with flying colors!\"\r\n                      : \"Every challenge makes you stronger. Try again!\"}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Modern Statistics Cards */}\r\n              <div className=\"p-8\">\r\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\r\n                  {/* Score Card */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-blue-500/5 to-indigo-500/10 rounded-2xl border border-blue-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-blue-600 mb-2 tracking-tight\">\r\n                        {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-blue-700/80 uppercase tracking-wider\">Your Score</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Correct vs Total */}\r\n                  <div className=\"group relative overflow-hidden bg-gradient-to-br from-emerald-500/5 to-green-500/10 rounded-2xl border border-emerald-200/50 p-6 hover:shadow-lg transition-all duration-300\">\r\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className=\"text-4xl font-black text-emerald-600 mb-2 tracking-tight\">\r\n                        {result.correctAnswers?.length || 0}/{questions.length}\r\n                      </div>\r\n                      <div className=\"text-sm font-bold text-emerald-700/80 uppercase tracking-wider\">Correct</div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  {/* Pass Status */}\r\n                  <div className={`group relative overflow-hidden rounded-2xl border p-6 hover:shadow-lg transition-all duration-300 ${\r\n                    result.verdict === \"Pass\"\r\n                      ? \"bg-gradient-to-br from-emerald-500/5 to-green-500/10 border-emerald-200/50\"\r\n                      : \"bg-gradient-to-br from-amber-500/5 to-orange-500/10 border-amber-200/50\"\r\n                  }`}>\r\n                    <div className={`absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 ${\r\n                      result.verdict === \"Pass\" ? \"from-emerald-500/5\" : \"from-amber-500/5\"\r\n                    } to-transparent`}></div>\r\n                    <div className=\"relative text-center\">\r\n                      <div className={`text-4xl font-black mb-2 tracking-tight ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"PASS\" : \"RETRY\"}\r\n                      </div>\r\n                      <div className={`text-sm font-bold uppercase tracking-wider ${\r\n                        result.verdict === \"Pass\" ? \"text-emerald-700/80\" : \"text-amber-700/80\"\r\n                      }`}>\r\n                        {result.verdict === \"Pass\" ? \"Success!\" : `Need ${examData.passingMarks}`}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Modern Progress Visualization */}\r\n                <div className=\"mb-8\">\r\n                  <div className=\"relative bg-slate-100 rounded-2xl p-6\">\r\n                    <div className=\"text-center mb-4\">\r\n                      <h3 className=\"text-lg font-bold text-slate-700 mb-1\">Performance Overview</h3>\r\n                      <p className=\"text-sm text-slate-500\">Your achievement level</p>\r\n                    </div>\r\n                    <div className=\"relative\">\r\n                      <div className=\"w-full bg-slate-200 rounded-full h-4 shadow-inner overflow-hidden\">\r\n                        <div\r\n                          className={`h-full rounded-full transition-all duration-1000 shadow-sm relative overflow-hidden ${\r\n                            result.verdict === \"Pass\"\r\n                              ? \"bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500\"\r\n                              : \"bg-gradient-to-r from-amber-500 via-orange-500 to-red-500\"\r\n                          }`}\r\n                          style={{ width: `${((result.correctAnswers?.length || 0) / questions.length) * 100}%` }}\r\n                        >\r\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-white/20 to-transparent\"></div>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex justify-between items-center mt-3\">\r\n                        <span className=\"text-xs font-medium text-slate-500\">0%</span>\r\n                        <span className={`text-lg font-black tracking-tight ${\r\n                          result.verdict === \"Pass\" ? \"text-emerald-600\" : \"text-amber-600\"\r\n                        }`}>\r\n                          {Math.round(((result.correctAnswers?.length || 0) / questions.length) * 100)}%\r\n                        </span>\r\n                        <span className=\"text-xs font-medium text-slate-500\">100%</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* XP Display */}\r\n                {result.xpData && (\r\n                  <div className=\"mb-8\">\r\n                    <XPResultDisplay xpData={result.xpData} />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Modern Action Buttons */}\r\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n                  <button\r\n                    className=\"group relative overflow-hidden px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\"\r\n                    onClick={() => setView(\"review\")}\r\n                  >\r\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\r\n                    <span className=\"relative\">Review Answers</span>\r\n                  </button>\r\n\r\n\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {view === \"review\" && (\r\n        <SimpleReviewRenderer\r\n          questions={questions}\r\n          selectedOptions={selectedOptions}\r\n          explanations={explanations}\r\n          fetchExplanation={fetchExplanation}\r\n          setView={setView}\r\n          examData={examData}\r\n          setSelectedQuestionIndex={setSelectedQuestionIndex}\r\n          setSelectedOptions={setSelectedOptions}\r\n          setResult={setResult}\r\n          setTimeUp={setTimeUp}\r\n          setSecondsLeft={setSecondsLeft}\r\n          setExplanations={setExplanations}\r\n        />\r\n      )}\r\n    </div>\r\n  ) : null;\r\n}\r\n\r\nexport default WriteExam;"], "mappings": ";;AAAA,SAASA,OAAO,QAAQ,MAAM;AAC9B,OAAOC,KAAK,IAAIC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,OAAOC,SAAS,MAAM,0BAA0B;AAChD,SAASC,uBAAuB,EAAEC,2BAA2B,QAAQ,wBAAwB;AAC7F,OAAOC,eAAe,MAAM,4CAA4C;;AAExE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,aAAa;EAAEC,cAAc;EAAEC,cAAc;EAAEC,cAAc;EAAEC,MAAM;EAAEC,UAAU;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EACpJ;EACA,IAAI,CAACR,QAAQ,EAAE;IACb,oBAAOF,OAAA;MAAAW,QAAA,EAAK;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACvC;;EAEA;EACA,MAAMC,YAAY,GAAGd,QAAQ,CAACe,IAAI,GAAGC,MAAM,CAAChB,QAAQ,CAACe,IAAI,CAAC,GAAG,6BAA6B;EAC1F,MAAME,UAAU,GAAGjB,QAAQ,CAACiB,UAAU,GAAGD,MAAM,CAAChB,QAAQ,CAACiB,UAAU,CAAC,GAAG,SAAS;;EAEhF;EACA,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIlB,QAAQ,CAACkB,OAAO,EAAE;IACpB,IAAIC,KAAK,CAACC,OAAO,CAACpB,QAAQ,CAACkB,OAAO,CAAC,EAAE;MACnCA,OAAO,GAAGlB,QAAQ,CAACkB,OAAO,CAACG,GAAG,CAACC,GAAG,IAAIN,MAAM,CAACM,GAAG,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC,MAAM,IAAI,OAAOtB,QAAQ,CAACkB,OAAO,KAAK,QAAQ,EAAE;MAC/CA,OAAO,GAAGK,MAAM,CAACC,MAAM,CAACxB,QAAQ,CAACkB,OAAO,CAAC,CAACG,GAAG,CAACC,GAAG,IAAIN,MAAM,CAACM,GAAG,IAAI,EAAE,CAAC,CAAC;IACzE;EACF;EAEA,MAAMG,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACrC,MAAMI,IAAI,GAAGJ,OAAO,GAAG,EAAE;IACzB,OAAOC,IAAI,GAAG,GAAG,IAAIG,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGA,IAAI;EACnD,CAAC;EAED,oBACEhC,OAAA;IAAKiC,KAAK,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,UAAU,EAAE;IAAoD,CAAE;IAAAxB,QAAA,gBAElGX,OAAA;MAAKiC,KAAK,EAAE;QACVE,UAAU,EAAE,OAAO;QACnBC,YAAY,EAAE,mBAAmB;QACjCC,OAAO,EAAE,MAAM;QACfC,QAAQ,EAAE,QAAQ;QAClBC,GAAG,EAAE,CAAC;QACNC,MAAM,EAAE;MACV,CAAE;MAAA7B,QAAA,eACAX,OAAA;QAAKiC,KAAK,EAAE;UAAEQ,QAAQ,EAAE,QAAQ;UAAEC,MAAM,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAlC,QAAA,gBAC3HX,OAAA;UAAAW,QAAA,gBACEX,OAAA;YAAIiC,KAAK,EAAE;cAAEa,QAAQ,EAAE,MAAM;cAAEC,UAAU,EAAE,KAAK;cAAEC,KAAK,EAAE,SAAS;cAAEN,MAAM,EAAE;YAAE,CAAE;YAAA/B,QAAA,EAC7ED,SAAS,GAAGQ,MAAM,CAACR,SAAS,CAAC,GAAG;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACLf,OAAA;YAAGiC,KAAK,EAAE;cAAEa,QAAQ,EAAE,MAAM;cAAEE,KAAK,EAAE,SAAS;cAAEN,MAAM,EAAE;YAAE,CAAE;YAAA/B,QAAA,GAAC,WAClD,EAACR,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENf,OAAA;UAAKiC,KAAK,EAAE;YACVE,UAAU,EAAE1B,QAAQ,IAAI,EAAE,GAAG,SAAS,GAAG,SAAS;YAClDuC,KAAK,EAAE,OAAO;YACdX,OAAO,EAAE,WAAW;YACpBY,YAAY,EAAE,MAAM;YACpBC,UAAU,EAAE,WAAW;YACvBH,UAAU,EAAE;UACd,CAAE;UAAApC,QAAA,GAAC,QACK,EAACgB,UAAU,CAAClB,QAAQ,CAAC;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNf,OAAA;MAAKiC,KAAK,EAAE;QAAEQ,QAAQ,EAAE,OAAO;QAAEC,MAAM,EAAE,QAAQ;QAAEL,OAAO,EAAE;MAAY,CAAE;MAAA1B,QAAA,gBACxEX,OAAA;QAAKiC,KAAK,EAAE;UACVE,UAAU,EAAE,OAAO;UACnBc,YAAY,EAAE,MAAM;UACpBE,SAAS,EAAE,uCAAuC;UAClDd,OAAO,EAAE,MAAM;UACfe,YAAY,EAAE;QAChB,CAAE;QAAAzC,QAAA,gBAEAX,OAAA;UAAKiC,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAzC,QAAA,eACnCX,OAAA;YAAMiC,KAAK,EAAE;cACXE,UAAU,EAAE,SAAS;cACrBa,KAAK,EAAE,SAAS;cAChBX,OAAO,EAAE,UAAU;cACnBY,YAAY,EAAE,MAAM;cACpBH,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAApC,QAAA,GAAC,WACQ,EAACR,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGNf,OAAA;UAAKiC,KAAK,EAAE;YAAEmB,YAAY,EAAE;UAAO,CAAE;UAAAzC,QAAA,eACnCX,OAAA;YAAIiC,KAAK,EAAE;cACTa,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,KAAK;cACjBC,KAAK,EAAE,SAAS;cAChBK,UAAU,EAAE,KAAK;cACjBX,MAAM,EAAE;YACV,CAAE;YAAA/B,QAAA,EACCK;UAAY;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,EAGL,CAACb,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAQ,kBACnCvD,OAAA;UAAKiC,KAAK,EAAE;YAAEmB,YAAY,EAAE,MAAM;YAAEI,SAAS,EAAE;UAAS,CAAE;UAAA7C,QAAA,eACxDX,OAAA;YACEyD,GAAG,EAAEvD,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAS;YACzCG,GAAG,EAAC,UAAU;YACdzB,KAAK,EAAE;cACLQ,QAAQ,EAAE,MAAM;cAChBkB,SAAS,EAAE,OAAO;cAClBC,SAAS,EAAE,SAAS;cACpBX,YAAY,EAAE,MAAM;cACpBY,MAAM,EAAE;YACV;UAAE;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAGDf,OAAA;UAAKiC,KAAK,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEmB,aAAa,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAApD,QAAA,EACnEQ,UAAU,KAAK,SAAS,IAAIC,OAAO,CAAC4C,MAAM,GAAG,CAAC,GAC7C5C,OAAO,CAACG,GAAG,CAAC,CAAC0C,MAAM,EAAEC,KAAK,KAAK;YAC7B,MAAMC,MAAM,GAAGjD,MAAM,CAACkD,YAAY,CAAC,EAAE,GAAGF,KAAK,CAAC;YAC9C,MAAMG,UAAU,GAAGhE,cAAc,KAAK6D,KAAK;YAE3C,oBACElE,OAAA;cAEEsE,OAAO,EAAEA,CAAA,KAAMhE,cAAc,CAAC4D,KAAK,CAAE;cACrCjC,KAAK,EAAE;gBACLsC,KAAK,EAAE,MAAM;gBACbf,SAAS,EAAE,MAAM;gBACjBnB,OAAO,EAAE,MAAM;gBACfY,YAAY,EAAE,MAAM;gBACpBY,MAAM,EAAEQ,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;gBAC9DlC,UAAU,EAAEkC,UAAU,GAAG,SAAS,GAAG,OAAO;gBAC5CrB,KAAK,EAAEqB,UAAU,GAAG,SAAS,GAAG,SAAS;gBACzCG,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,UAAU;gBACtB9B,OAAO,EAAE,MAAM;gBACfE,UAAU,EAAE,QAAQ;gBACpBkB,GAAG,EAAE;cACP,CAAE;cAAApD,QAAA,gBAEFX,OAAA;gBAAKiC,KAAK,EAAE;kBACVsC,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdzB,YAAY,EAAE,KAAK;kBACnBd,UAAU,EAAEkC,UAAU,GAAG,SAAS,GAAG,SAAS;kBAC9CrB,KAAK,EAAEqB,UAAU,GAAG,OAAO,GAAG,SAAS;kBACvC1B,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE,QAAQ;kBACxBG,UAAU,EAAE,MAAM;kBAClBD,QAAQ,EAAE;gBACZ,CAAE;gBAAAnC,QAAA,EACCwD;cAAM;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNf,OAAA;gBAAMiC,KAAK,EAAE;kBAAE0C,IAAI,EAAE,CAAC;kBAAE5B,UAAU,EAAE;gBAAM,CAAE;gBAAApC,QAAA,EAAEsD;cAAM;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAC3DsD,UAAU,iBACTrE,OAAA;gBAAKiC,KAAK,EAAE;kBACVsC,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdzB,YAAY,EAAE,KAAK;kBACnBd,UAAU,EAAE,SAAS;kBACrBa,KAAK,EAAE,OAAO;kBACdL,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBD,cAAc,EAAE;gBAClB,CAAE;gBAAAjC,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA,GA7CImD,KAAK;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8CJ,CAAC;UAEb,CAAC,CAAC,gBAEFf,OAAA;YAAAW,QAAA,gBACEX,OAAA;cAAOiC,KAAK,EAAE;gBACZU,OAAO,EAAE,OAAO;gBAChBG,QAAQ,EAAE,MAAM;gBAChBC,UAAU,EAAE,KAAK;gBACjBC,KAAK,EAAE,SAAS;gBAChBI,YAAY,EAAE;cAChB,CAAE;cAAAzC,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRf,OAAA;cACE4E,IAAI,EAAC,MAAM;cACXC,KAAK,EAAExE,cAAc,IAAI,EAAG;cAC5ByE,QAAQ,EAAGC,CAAC,IAAKzE,cAAc,CAACyE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAChDI,WAAW,EAAC,0BAA0B;cACtChD,KAAK,EAAE;gBACLsC,KAAK,EAAE,MAAM;gBACblC,OAAO,EAAE,MAAM;gBACfwB,MAAM,EAAE,mBAAmB;gBAC3BZ,YAAY,EAAE,MAAM;gBACpBH,QAAQ,EAAE,MAAM;gBAChBoC,OAAO,EAAE;cACX;YAAE;cAAAtE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNf,OAAA;QAAKiC,KAAK,EAAE;UAAEU,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAlC,QAAA,gBACrFX,OAAA;UACEsE,OAAO,EAAE9D,UAAW;UACpB2E,QAAQ,EAAEhF,aAAa,KAAK,CAAE;UAC9B8B,KAAK,EAAE;YACLI,OAAO,EAAE,WAAW;YACpBY,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAE,KAAK;YACjBc,MAAM,EAAE,MAAM;YACdW,MAAM,EAAErE,aAAa,KAAK,CAAC,GAAG,aAAa,GAAG,SAAS;YACvDgC,UAAU,EAAEhC,aAAa,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS;YACvD6C,KAAK,EAAE7C,aAAa,KAAK,CAAC,GAAG,SAAS,GAAG;UAC3C,CAAE;UAAAQ,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETf,OAAA;UAAKiC,KAAK,EAAE;YAAEuB,SAAS,EAAE;UAAS,CAAE;UAAA7C,QAAA,gBAClCX,OAAA;YAAKiC,KAAK,EAAE;cAAEa,QAAQ,EAAE,MAAM;cAAEE,KAAK,EAAE,SAAS;cAAEI,YAAY,EAAE;YAAM,CAAE;YAAAzC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvFf,OAAA;YAAKiC,KAAK,EAAE;cACVsC,KAAK,EAAE,OAAO;cACdG,MAAM,EAAE,KAAK;cACbvC,UAAU,EAAE,SAAS;cACrBc,YAAY,EAAE,KAAK;cACnBmC,QAAQ,EAAE;YACZ,CAAE;YAAAzE,QAAA,eACAX,OAAA;cACEiC,KAAK,EAAE;gBACLyC,MAAM,EAAE,MAAM;gBACdvC,UAAU,EAAE,SAAS;gBACrBc,YAAY,EAAE,KAAK;gBACnBsB,KAAK,EAAG,CAACpE,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG,GAAG,GAAG;gBACzDqE,UAAU,EAAE;cACd;YAAE;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENf,OAAA;UACEsE,OAAO,EAAE/D,MAAO;UAChB0B,KAAK,EAAE;YACLI,OAAO,EAAE,WAAW;YACpBY,YAAY,EAAE,MAAM;YACpBF,UAAU,EAAE,KAAK;YACjBc,MAAM,EAAE,MAAM;YACdW,MAAM,EAAE,SAAS;YACjBrC,UAAU,EAAE,SAAS;YACrBa,KAAK,EAAE;UACT,CAAE;UAAArC,QAAA,EAEDR,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,aAAa,GAAG;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAsE,EAAA,GApQMpF,mBAAmB;AAqQzB,MAAMqF,oBAAoB,GAAGA,CAAC;EAC5BC,SAAS;EACTC,eAAe;EACfC,YAAY;EACZC,gBAAgB;EAChBC,OAAO;EACPC,QAAQ;EACRC,wBAAwB;EACxBC,kBAAkB;EAClBC,SAAS;EACTC,SAAS;EACTC,cAAc;EACdC;AACF,CAAC,KAAK;EACJ,IAAI,CAACX,SAAS,IAAI,CAAClE,KAAK,CAACC,OAAO,CAACiE,SAAS,CAAC,EAAE;IAC3C,oBACEvF,OAAA;MAAKmG,SAAS,EAAC,wFAAwF;MAAAxF,QAAA,eACrGX,OAAA;QAAKmG,SAAS,EAAC,+CAA+C;QAAAxF,QAAA,gBAC5DX,OAAA;UAAImG,SAAS,EAAC,qCAAqC;UAAAxF,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrEf,OAAA;UAAGmG,SAAS,EAAC,cAAc;UAAAxF,QAAA,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEf,OAAA;IAAKmG,SAAS,EAAC,6EAA6E;IAAAxF,QAAA,eAC1FX,OAAA;MAAKmG,SAAS,EAAC,wBAAwB;MAAAxF,QAAA,gBAErCX,OAAA;QAAKmG,SAAS,EAAC,kBAAkB;QAAAxF,QAAA,eAC/BX,OAAA;UAAKmG,SAAS,EAAC,kFAAkF;UAAAxF,QAAA,gBAC/FX,OAAA;YAAImG,SAAS,EAAC,uCAAuC;YAAAxF,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxEf,OAAA;YAAGmG,SAAS,EAAC,gBAAgB;YAAAxF,QAAA,EAAC;UAAwC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNf,OAAA;QAAKmG,SAAS,EAAC,gBAAgB;QAAAxF,QAAA,EAC5B4E,SAAS,CAAChE,GAAG,CAAC,CAACrB,QAAQ,EAAEgE,KAAK,KAAK;UAClC;UACA,IAAI,CAAChE,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;YAC7C,OAAO,IAAI;UACb;;UAEA;UACA,MAAMc,YAAY,GAAGE,MAAM,CAAChB,QAAQ,CAACe,IAAI,IAAI,EAAE,CAAC;UAChD,MAAME,UAAU,GAAGD,MAAM,CAAChB,QAAQ,CAACiB,UAAU,IAAI,EAAE,CAAC;UACpD,MAAMiF,aAAa,GAAGlG,QAAQ,CAACkG,aAAa;UAC5C,MAAMC,aAAa,GAAGnG,QAAQ,CAACmG,aAAa;UAC5C,MAAMC,UAAU,GAAGd,eAAe,CAACtB,KAAK,CAAC;;UAEzC;UACA,IAAIqC,SAAS,GAAG,KAAK;UACrB,IAAIC,iBAAiB,GAAG,EAAE;UAC1B,IAAIC,cAAc,GAAG,EAAE;UAEvB,IAAItF,UAAU,KAAK,SAAS,EAAE;YAC5BoF,SAAS,GAAGH,aAAa,KAAKE,UAAU;;YAExC;YACA,IAAIpG,QAAQ,CAACkB,OAAO,IAAIgF,aAAa,KAAKM,SAAS,EAAE;cACnD,MAAMC,WAAW,GAAGzG,QAAQ,CAACkB,OAAO,CAACgF,aAAa,CAAC;cACnDI,iBAAiB,GAAG,OAAOG,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGzF,MAAM,CAACyF,WAAW,IAAIP,aAAa,IAAI,SAAS,CAAC;YACvH,CAAC,MAAM;cACLI,iBAAiB,GAAGtF,MAAM,CAACkF,aAAa,IAAI,SAAS,CAAC;YACxD;;YAEA;YACA,IAAIlG,QAAQ,CAACkB,OAAO,IAAIkF,UAAU,KAAKI,SAAS,EAAE;cAChD,MAAMC,WAAW,GAAGzG,QAAQ,CAACkB,OAAO,CAACkF,UAAU,CAAC;cAChDG,cAAc,GAAG,OAAOE,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGzF,MAAM,CAACyF,WAAW,IAAIL,UAAU,IAAI,cAAc,CAAC;YACtH,CAAC,MAAM;cACLG,cAAc,GAAGvF,MAAM,CAACoF,UAAU,IAAI,cAAc,CAAC;YACvD;UACF,CAAC,MAAM;YACLC,SAAS,GAAGF,aAAa,KAAKC,UAAU;YACxCE,iBAAiB,GAAGtF,MAAM,CAACmF,aAAa,IAAI,SAAS,CAAC;YACtDI,cAAc,GAAGvF,MAAM,CAACoF,UAAU,IAAI,cAAc,CAAC;UACvD;UAEA,oBACEtG,OAAA;YAEEmG,SAAS,EAAG,qCACVI,SAAS,GACL,8BAA8B,GAC9B,0BACL,EAAE;YAAA5F,QAAA,gBAGHX,OAAA;cAAKmG,SAAS,EAAC,MAAM;cAAAxF,QAAA,eACnBX,OAAA;gBAAKmG,SAAS,EAAC,4BAA4B;gBAAAxF,QAAA,gBACzCX,OAAA;kBAAKmG,SAAS,EAAC,iHAAiH;kBAAAxF,QAAA,EAC7HuD,KAAK,GAAG;gBAAC;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACNf,OAAA;kBAAKmG,SAAS,EAAC,QAAQ;kBAAAxF,QAAA,eACrBX,OAAA;oBAAGmG,SAAS,EAAC,4CAA4C;oBAAAxF,QAAA,EAAEK;kBAAY;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNf,OAAA;cAAKmG,SAAS,EAAC,MAAM;cAAAxF,QAAA,gBACnBX,OAAA;gBAAMmG,SAAS,EAAC,sCAAsC;gBAAAxF,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3Ef,OAAA;gBAAMmG,SAAS,EAAG,eAAcI,SAAS,GAAG,gBAAgB,GAAG,cAAe,EAAE;gBAAA5F,QAAA,EAC7E8F;cAAc;gBAAA7F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,EACNwF,SAAS,gBACRvG,OAAA;gBAAMmG,SAAS,EAAC,6BAA6B;gBAAAxF,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAEtDf,OAAA;gBAAMmG,SAAS,EAAC,2BAA2B;gBAAAxF,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGL,CAACwF,SAAS,iBACTvG,OAAA;cAAKmG,SAAS,EAAC,MAAM;cAAAxF,QAAA,gBACnBX,OAAA;gBAAMmG,SAAS,EAAC,sCAAsC;gBAAAxF,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9Ef,OAAA;gBAAMmG,SAAS,EAAC,4BAA4B;gBAAAxF,QAAA,EAAE6F;cAAiB;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvEf,OAAA;gBAAMmG,SAAS,EAAC,6BAA6B;gBAAAxF,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,EAGA,CAACwF,SAAS,iBACTvG,OAAA;cAAKmG,SAAS,EAAC,MAAM;cAAAxF,QAAA,eACnBX,OAAA;gBACEmG,SAAS,EAAC,iKAAiK;gBAC3K7B,OAAO,EAAEA,CAAA,KAAM;kBACboB,gBAAgB,CACd1E,YAAY,EACZwF,iBAAiB,EACjBC,cAAc,EACdvG,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAQ,IAAI,EACzC,CAAC;gBACH,CAAE;gBAAA5C,QAAA,gBAEFX,OAAA;kBAAAW,QAAA,EAAM;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACff,OAAA;kBAAAW,QAAA,EAAM;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGA0E,YAAY,CAACzE,YAAY,CAAC,iBACzBhB,OAAA;cAAKmG,SAAS,EAAC,4FAA4F;cAAAxF,QAAA,gBACzGX,OAAA;gBAAKmG,SAAS,EAAC,wBAAwB;gBAAAxF,QAAA,gBACrCX,OAAA;kBAAMmG,SAAS,EAAC,4BAA4B;kBAAAxF,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtDf,OAAA;kBAAImG,SAAS,EAAC,mCAAmC;kBAAAxF,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,EAGL,CAACb,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAQ,kBACnCvD,OAAA;gBAAKmG,SAAS,EAAC,oDAAoD;gBAAAxF,QAAA,gBACjEX,OAAA;kBAAKmG,SAAS,EAAC,wBAAwB;kBAAAxF,QAAA,eACrCX,OAAA;oBAAMmG,SAAS,EAAC,mCAAmC;oBAAAxF,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,eACNf,OAAA;kBAAKmG,SAAS,EAAC,qBAAqB;kBAAAxF,QAAA,eAClCX,OAAA;oBACEyD,GAAG,EAAEvD,QAAQ,CAACoD,KAAK,IAAIpD,QAAQ,CAACqD,QAAS;oBACzCG,GAAG,EAAC,kBAAkB;oBACtByC,SAAS,EAAC,mEAAmE;oBAC7ElE,KAAK,EAAE;sBAAEQ,QAAQ,EAAE;oBAAQ;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDf,OAAA;gBAAKmG,SAAS,EAAC,8DAA8D;gBAAAxF,QAAA,EAC1EO,MAAM,CAACuE,YAAY,CAACzE,YAAY,CAAC,IAAI,EAAE;cAAC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,GA1FIG,MAAM,CAAChB,QAAQ,CAAC0G,GAAG,IAAI1C,KAAK,CAAC;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2F/B,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNf,OAAA;QAAKmG,SAAS,EAAC,6DAA6D;QAAAxF,QAAA,gBAC1EX,OAAA;UACEmG,SAAS,EAAC,+GAA+G;UACzH7B,OAAO,EAAEA,CAAA,KAAMqB,OAAO,CAAC,QAAQ,CAAE;UAAAhF,QAAA,EAClC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETf,OAAA;UACEmG,SAAS,EAAC,iHAAiH;UAC3H7B,OAAO,EAAEA,CAAA,KAAM;YACb;YACAqB,OAAO,CAAC,cAAc,CAAC;YACvBE,wBAAwB,CAAC,CAAC,CAAC;YAC3BC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACtBC,SAAS,CAAC,CAAC,CAAC,CAAC;YACbC,SAAS,CAAC,KAAK,CAAC;YAChBC,cAAc,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiB,QAAQ,KAAI,CAAC,CAAC;YACvCX,eAAe,CAAC,CAAC,CAAC,CAAC;UACrB,CAAE;UAAAvF,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC+F,GAAA,GA9MIxB,oBAAoB;AAgN1B,SAASyB,SAASA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACnB,MAAM,CAACxB,QAAQ,EAAEyB,WAAW,CAAC,GAAGzI,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC2G,SAAS,EAAE+B,YAAY,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2I,qBAAqB,EAAE1B,wBAAwB,CAAC,GAAGjH,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC4G,eAAe,EAAEM,kBAAkB,CAAC,GAAGlH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC4I,MAAM,EAAEzB,SAAS,CAAC,GAAGnH,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM6I,MAAM,GAAGzI,SAAS,CAAC,CAAC;EAC1B,MAAM0I,QAAQ,GAAG7I,WAAW,CAAC,CAAC;EAC9B,MAAM8I,QAAQ,GAAG5I,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6I,IAAI,EAAEjC,OAAO,CAAC,GAAG/G,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAM,CAACiJ,WAAW,EAAE5B,cAAc,CAAC,GAAGrH,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkJ,MAAM,EAAE9B,SAAS,CAAC,GAAGpH,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACmJ,UAAU,EAAEC,aAAa,CAAC,GAAGpJ,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACqJ,SAAS,EAAEC,YAAY,CAAC,GAAGtJ,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuJ,SAAS,EAAEC,YAAY,CAAC,GAAGxJ,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM;IAAEyJ;EAAK,CAAC,GAAGvJ,WAAW,CAAEwJ,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAM;IAAE9D,KAAK;IAAEG;EAAO,CAAC,GAAGjF,aAAa,CAAC,CAAC;EACzC,MAAM,CAACgG,YAAY,EAAES,eAAe,CAAC,GAAGtH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEpD,MAAM2J,WAAW,GAAG7J,WAAW,CAAC,YAAY;IAC1C,IAAI;MACFwJ,YAAY,CAAC,IAAI,CAAC;MAClBR,QAAQ,CAACtI,WAAW,CAAC,CAAC,CAAC;MACvBoJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEhB,MAAM,CAACiB,EAAE,CAAC;MAEpD,MAAMC,QAAQ,GAAG,MAAM1J,WAAW,CAAC;QAAE2J,MAAM,EAAEnB,MAAM,CAACiB;MAAG,CAAC,CAAC;MACzDF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,QAAQ,CAAC;MAE3CjB,QAAQ,CAACvI,WAAW,CAAC,CAAC,CAAC;MACvB+I,YAAY,CAAC,KAAK,CAAC;MAEnB,IAAIS,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMjD,QAAQ,GAAG+C,QAAQ,CAACG,IAAI;;QAE9B;QACA,IAAIvD,SAAS,GAAG,EAAE;QAClB,IAAIK,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEL,SAAS,IAAIlE,KAAK,CAACC,OAAO,CAACsE,QAAQ,CAACL,SAAS,CAAC,EAAE;UAC5DA,SAAS,GAAGK,QAAQ,CAACL,SAAS;QAChC,CAAC,MAAM,IAAIK,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAE1F,QAAQ,IAAImB,KAAK,CAACC,OAAO,CAACsE,QAAQ,CAAC1F,QAAQ,CAAC,EAAE;UACjEqF,SAAS,GAAGK,QAAQ,CAAC1F,QAAQ;QAC/B,CAAC,MAAM,IAAI0F,QAAQ,IAAIvE,KAAK,CAACC,OAAO,CAACsE,QAAQ,CAAC,EAAE;UAC9CL,SAAS,GAAGK,QAAQ;QACtB;QAEA4C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE7C,QAAQ,CAAC;QACnC4C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAElD,SAAS,CAACvB,MAAM,CAAC;QACjDwE,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEhH,MAAM,CAACsH,IAAI,CAACnD,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC;QAEhE0B,YAAY,CAAC/B,SAAS,CAAC;QACvB8B,WAAW,CAACzB,QAAQ,CAAC;QACrBK,cAAc,CAAC,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiB,QAAQ,KAAI,CAAC,CAAC;QAEvC,IAAItB,SAAS,CAACvB,MAAM,KAAK,CAAC,EAAE;UAC1BwE,OAAO,CAACQ,IAAI,CAAC,iCAAiC,CAAC;UAC/CR,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEE,QAAQ,CAAC;UACrDnK,OAAO,CAACyK,OAAO,CAAC,6DAA6D,CAAC;QAChF;MACF,CAAC,MAAM;QACLT,OAAO,CAACU,KAAK,CAAC,YAAY,EAAEP,QAAQ,CAACnK,OAAO,CAAC;QAC7CgK,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEE,QAAQ,CAAC;QAC7CnK,OAAO,CAAC0K,KAAK,CAACP,QAAQ,CAACnK,OAAO,IAAI,0BAA0B,CAAC;MAC/D;IACF,CAAC,CAAC,OAAO0K,KAAK,EAAE;MACdxB,QAAQ,CAACvI,WAAW,CAAC,CAAC,CAAC;MACvB+I,YAAY,CAAC,KAAK,CAAC;MACnBM,OAAO,CAACU,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD1K,OAAO,CAAC0K,KAAK,CAACA,KAAK,CAAC1K,OAAO,IAAI,wCAAwC,CAAC;IAC1E;EACF,CAAC,EAAE,CAACiJ,MAAM,CAACiB,EAAE,EAAEhB,QAAQ,CAAC,CAAC;EAEzB,MAAMyB,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACpF,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAE8E;IAAK,CAAC,GAAG,MAAMlJ,uBAAuB,CAACwJ,OAAO,CAAC;IACvD,OAAON,IAAI;EACb,CAAC;EAED,MAAMO,eAAe,GAAG3K,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF;MACA,IAAI,CAAC2J,IAAI,IAAI,CAACA,IAAI,CAACzB,GAAG,EAAE;QACtBpI,OAAO,CAAC0K,KAAK,CAAC,sCAAsC,CAAC;QACrDvB,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAD,QAAQ,CAACtI,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMkK,eAAe,GAAG,EAAE;MAC1B,MAAMC,QAAQ,GAAG,EAAE;MAEnBhE,SAAS,CAACiE,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,IAAID,CAAC,CAACtI,UAAU,KAAK,WAAW,IAAIsI,CAAC,CAACtI,UAAU,KAAK,mBAAmB,EAAE;UACxEoI,QAAQ,CAACI,IAAI,CAACD,GAAG,CAAC;UAClBJ,eAAe,CAACK,IAAI,CAAC;YACnBzJ,QAAQ,EAAEuJ,CAAC,CAACxI,IAAI;YAChB2I,cAAc,EAAEH,CAAC,CAACpD,aAAa,IAAIoD,CAAC,CAACrD,aAAa;YAClDE,UAAU,EAAEd,eAAe,CAACkE,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMG,UAAU,GAAG,MAAMV,oBAAoB,CAACG,eAAe,CAAC;MAC9D,MAAMQ,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACL,OAAO,CAAEO,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACvC,MAAM,IAAI,OAAOuC,CAAC,CAACvC,MAAM,CAACjB,SAAS,KAAK,SAAS,EAAE;UACvDuD,MAAM,CAACC,CAAC,CAAC7J,QAAQ,CAAC,GAAG6J,CAAC,CAACvC,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOuC,CAAC,CAACxD,SAAS,KAAK,SAAS,EAAE;UAC3CuD,MAAM,CAACC,CAAC,CAAC7J,QAAQ,CAAC,GAAG;YAAEqG,SAAS,EAAEwD,CAAC,CAACxD,SAAS;YAAEyD,MAAM,EAAED,CAAC,CAACC,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MACvB,MAAMC,YAAY,GAAG,EAAE;MAEvB5E,SAAS,CAACiE,OAAO,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAK;QAC5B,MAAMU,aAAa,GAAG5E,eAAe,CAACkE,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAID,CAAC,CAACtI,UAAU,KAAK,WAAW,IAAIsI,CAAC,CAACtI,UAAU,KAAK,mBAAmB,EAAE;UACxE,MAAM;YAAEoF,SAAS,GAAG,KAAK;YAAEyD,MAAM,GAAG;UAAG,CAAC,GAAGF,MAAM,CAACL,CAAC,CAACxI,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAMoJ,QAAQ,GAAG;YAAE,GAAGZ,CAAC;YAAEnD,UAAU,EAAE8D,aAAa;YAAEJ;UAAO,CAAC;UAE5D,IAAIzD,SAAS,EAAE;YACb0D,cAAc,CAACN,IAAI,CAACU,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACP,IAAI,CAACU,QAAQ,CAAC;YAC3BF,YAAY,CAACR,IAAI,CAAC;cAChBzJ,QAAQ,EAAEuJ,CAAC,CAACxI,IAAI;cAChB2I,cAAc,EAAEH,CAAC,CAACpD,aAAa,IAAIoD,CAAC,CAACrD,aAAa;cAClDE,UAAU,EAAE8D;YACd,CAAC,CAAC;UACJ;QACF,CAAC,MAAM,IAAIX,CAAC,CAACtI,UAAU,KAAK,SAAS,EAAE;UACrC,MAAMmJ,UAAU,GAAGb,CAAC,CAACrD,aAAa;UAClC,MAAMmE,YAAY,GAAId,CAAC,CAACrI,OAAO,IAAIqI,CAAC,CAACrI,OAAO,CAACkJ,UAAU,CAAC,IAAKA,UAAU;UACvE,MAAME,SAAS,GAAIf,CAAC,CAACrI,OAAO,IAAIqI,CAAC,CAACrI,OAAO,CAACgJ,aAAa,CAAC,IAAKA,aAAa,IAAI,EAAE;UAEhF,MAAM7D,SAAS,GAAG+D,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGZ,CAAC;YAAEnD,UAAU,EAAE8D;UAAc,CAAC;UAEpD,IAAI7D,SAAS,EAAE;YACb0D,cAAc,CAACN,IAAI,CAACU,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLH,YAAY,CAACP,IAAI,CAACU,QAAQ,CAAC;YAC3BF,YAAY,CAACR,IAAI,CAAC;cAChBzJ,QAAQ,EAAEuJ,CAAC,CAACxI,IAAI;cAChB2I,cAAc,EAAEW,YAAY;cAC5BjE,UAAU,EAAEkE;YACd,CAAC,CAAC;UACJ;QACF;MACF,CAAC,CAAC;;MAEF;MACA,MAAMC,SAAS,GAAGtC,SAAS,GAAGrG,IAAI,CAACC,KAAK,CAAC,CAAC2I,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGxC,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC;MAC7E,MAAMyC,gBAAgB,GAAG,CAAC,CAAAhF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiB,QAAQ,KAAI,CAAC,IAAI,EAAE,CAAC,CAAC;;MAEzD;MACA,MAAMzG,cAAc,GAAGmF,SAAS,CAACvB,MAAM;MACvC,MAAM6G,YAAY,GAAGZ,cAAc,CAACjG,MAAM;MAC1C,MAAM8G,eAAe,GAAGhJ,IAAI,CAACiJ,KAAK,CAAEF,YAAY,GAAGzK,cAAc,GAAI,GAAG,CAAC;MACzE,MAAM4K,MAAM,GAAGH,YAAY,GAAG,EAAE,CAAC,CAAC;;MAElC;MACA,MAAMI,iBAAiB,GAAGrF,QAAQ,CAACsF,YAAY,IAAI,EAAE,CAAC,CAAC;MACvD,MAAMC,OAAO,GAAGL,eAAe,IAAIG,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEtE,MAAMG,UAAU,GAAG;QACjBnB,cAAc,EAAEA,cAAc,IAAI,EAAE;QACpCC,YAAY,EAAEA,YAAY,IAAI,EAAE;QAChCiB,OAAO,EAAEA,OAAO,IAAI,MAAM;QAC1BE,KAAK,EAAEP,eAAe;QACtBE,MAAM,EAAEA,MAAM;QACd5K,cAAc,EAAEA,cAAc;QAC9BqK,SAAS,EAAEA,SAAS;QACpBG,gBAAgB,EAAEA;MACpB,CAAC;MAED7E,SAAS,CAACqF,UAAU,CAAC;MAErB,MAAMzC,QAAQ,GAAG,MAAMzJ,SAAS,CAAC;QAC/BoM,IAAI,EAAE7D,MAAM,CAACiB,EAAE;QACflB,MAAM,EAAE4D,UAAU;QAClB/C,IAAI,EAAEA,IAAI,CAACzB;MACb,CAAC,CAAC;MAEF,IAAI+B,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAM0C,YAAY,GAAG;UACnB,GAAGH,UAAU;UACbI,MAAM,EAAE7C,QAAQ,CAAC6C;QACnB,CAAC;QACDzF,SAAS,CAACwF,YAAY,CAAC;QAEvB5F,OAAO,CAAC,QAAQ,CAAC;QACjB8F,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACrB,IAAIC,KAAK,CAACR,OAAO,KAAK,MAAM,GAAGzL,SAAS,GAAGC,SAAS,CAAC,CAACiM,IAAI,CAAC,CAAC;MAC9D,CAAC,MAAM;QACLpN,OAAO,CAAC0K,KAAK,CAACP,QAAQ,CAACnK,OAAO,CAAC;MACjC;MACAkJ,QAAQ,CAACvI,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAO+J,KAAK,EAAE;MACdxB,QAAQ,CAACvI,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAAC0K,KAAK,CAACA,KAAK,CAAC1K,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC+G,SAAS,EAAEC,eAAe,EAAEI,QAAQ,EAAE6B,MAAM,CAACiB,EAAE,EAAEL,IAAI,EAAEV,QAAQ,EAAED,QAAQ,CAAC,CAAC;EAE/E,MAAMhC,gBAAgB,GAAG,MAAAA,CAAOxF,QAAQ,EAAE0J,cAAc,EAAEtD,UAAU,EAAE/C,QAAQ,KAAK;IACjF,IAAI;MACFmE,QAAQ,CAACtI,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMuJ,QAAQ,GAAG,MAAM9I,2BAA2B,CAAC;QAAEK,QAAQ;QAAE0J,cAAc;QAAEtD,UAAU;QAAE/C;MAAS,CAAC,CAAC;MACtGmE,QAAQ,CAACvI,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAIwJ,QAAQ,CAACE,OAAO,EAAE;QACpB3C,eAAe,CAAE2F,IAAI,KAAM;UAAE,GAAGA,IAAI;UAAE,CAAC3L,QAAQ,GAAGyI,QAAQ,CAACmD;QAAY,CAAC,CAAC,CAAC;MAC5E,CAAC,MAAM;QACLtN,OAAO,CAAC0K,KAAK,CAACP,QAAQ,CAACO,KAAK,IAAI,8BAA8B,CAAC;MACjE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdxB,QAAQ,CAACvI,WAAW,CAAC,CAAC,CAAC;MACvBX,OAAO,CAAC0K,KAAK,CAACA,KAAK,CAAC1K,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,MAAMuN,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMC,YAAY,GAAG,CAAApG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiB,QAAQ,KAAI,CAAC;IAC5CZ,cAAc,CAAC+F,YAAY,CAAC;IAC5B5D,YAAY,CAACsC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE1B,MAAMsB,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtCjG,cAAc,CAAEkG,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACLnG,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRgC,aAAa,CAACiE,aAAa,CAAC;EAC9B,CAAC;EAEDtN,SAAS,CAAC,MAAM;IACd,IAAImJ,MAAM,IAAIF,IAAI,KAAK,WAAW,EAAE;MAClCwE,aAAa,CAACrE,UAAU,CAAC;MACzBsB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACvB,MAAM,EAAEF,IAAI,EAAEG,UAAU,EAAEsB,eAAe,CAAC,CAAC;EAE/C1K,SAAS,CAAC,MAAM;IACd6J,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEhB,MAAM,CAACiB,EAAE,CAAC;IAC1D,IAAIjB,MAAM,CAACiB,EAAE,EAAE;MACbH,WAAW,CAAC,CAAC;IACf,CAAC,MAAM;MACLC,OAAO,CAACU,KAAK,CAAC,uCAAuC,CAAC;MACtD1K,OAAO,CAAC0K,KAAK,CAAC,sDAAsD,CAAC;MACrEvB,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACF,MAAM,CAACiB,EAAE,EAAEH,WAAW,EAAEZ,QAAQ,CAAC,CAAC;EAEtChJ,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAIoJ,UAAU,EAAE;QACdqE,aAAa,CAACrE,UAAU,CAAC;MAC3B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACApJ,SAAS,CAAC,MAAM;IACd,IAAIiJ,IAAI,KAAK,cAAc,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACxEyE,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChD,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD;;IAEA;IACA,OAAO,MAAM;MACXJ,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,CAAC7E,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM8E,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFhF,QAAQ,CAACtI,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMuJ,QAAQ,GAAG,MAAMgE,KAAK,CAAC,kCAAkC,EAAE;QAC/DC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE;QAC3D,CAAC;QACDT,IAAI,EAAEU,IAAI,CAACC,SAAS,CAAC;UAAErE,MAAM,EAAEnB,MAAM,CAACiB;QAAG,CAAC;MAC5C,CAAC,CAAC;MAEF,MAAMI,IAAI,GAAG,MAAMH,QAAQ,CAACuE,IAAI,CAAC,CAAC;MAClC,IAAIpE,IAAI,CAACD,OAAO,EAAE;QAChBrK,OAAO,CAACqK,OAAO,CAACC,IAAI,CAACtK,OAAO,CAAC;QAC7B;QACA+J,WAAW,CAAC,CAAC;MACf,CAAC,MAAM;QACL/J,OAAO,CAAC0K,KAAK,CAACJ,IAAI,CAACtK,OAAO,CAAC;MAC7B;IACF,CAAC,CAAC,OAAO0K,KAAK,EAAE;MACd1K,OAAO,CAAC0K,KAAK,CAAC,iCAAiC,CAAC;IAClD,CAAC,SAAS;MACRxB,QAAQ,CAACvI,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;;EAED;EACA,IAAI,CAACkJ,IAAI,EAAE;IACT,oBACErI,OAAA;MAAKmG,SAAS,EAAC,qGAAqG;MAAAxF,QAAA,eAClHX,OAAA;QAAKmG,SAAS,EAAC,2GAA2G;QAAAxF,QAAA,gBACxHX,OAAA;UAAKmG,SAAS,EAAC,kFAAkF;UAAAxF,QAAA,eAC/FX,OAAA;YAAKmG,SAAS,EAAC,sBAAsB;YAACgH,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAzM,QAAA,eAC3EX,OAAA;cAAMqN,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,8JAA8J;cAACC,QAAQ,EAAC;YAAS;cAAA3M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5M;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAImG,SAAS,EAAC,uCAAuC;UAAAxF,QAAA,EAAC;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClFf,OAAA;UAAGmG,SAAS,EAAC,oBAAoB;UAAAxF,QAAA,EAAC;QAAiE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvGf,OAAA;UACEmG,SAAS,EAAC,mNAAmN;UAC7N7B,OAAO,EAAEA,CAAA,KAAMqD,QAAQ,CAAC,QAAQ,CAAE;UAAAhH,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,OAAO6E,QAAQ,gBACb5F,OAAA;IAAKmG,SAAS,EAAC,oEAAoE;IAAAxF,QAAA,GAEhFiH,IAAI,KAAK,cAAc,iBACtB5H,OAAA,CAACX,YAAY;MACXuG,QAAQ,EAAEA,QAAS;MACnBD,OAAO,EAAEA,OAAQ;MACjBoG,UAAU,EAAEA,UAAW;MACvBxG,SAAS,EAAEA;IAAU;MAAA3E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CACF,EAEA6G,IAAI,KAAK,WAAW,KACnBK,SAAS,gBACPjI,OAAA;MAAKmG,SAAS,EAAC,qGAAqG;MAAAxF,QAAA,eAClHX,OAAA;QAAKmG,SAAS,EAAC,2GAA2G;QAAAxF,QAAA,gBACxHX,OAAA;UAAKmG,SAAS,EAAC,2IAA2I;UAAAxF,QAAA,eACxJX,OAAA;YAAKmG,SAAS,EAAC,mCAAmC;YAACgH,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAAAzM,QAAA,gBAChFX,OAAA;cAAQmG,SAAS,EAAC,YAAY;cAACqH,EAAE,EAAC,IAAI;cAACC,EAAE,EAAC,IAAI;cAAC1D,CAAC,EAAC,IAAI;cAAC2D,MAAM,EAAC,cAAc;cAACC,WAAW,EAAC;YAAG;cAAA/M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACrGf,OAAA;cAAMmG,SAAS,EAAC,YAAY;cAACgH,IAAI,EAAC,cAAc;cAACG,CAAC,EAAC;YAAiH;cAAA1M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAImG,SAAS,EAAC,uCAAuC;UAAAxF,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1Ef,OAAA;UAAGmG,SAAS,EAAC,uBAAuB;UAAAxF,QAAA,EAAC;QAErC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJwE,SAAS,CAACvB,MAAM,KAAK,CAAC,gBACxBhE,OAAA;MAAKmG,SAAS,EAAC,sGAAsG;MAAAxF,QAAA,eACnHX,OAAA;QAAKmG,SAAS,EAAC,4GAA4G;QAAAxF,QAAA,gBACzHX,OAAA;UAAKmG,SAAS,EAAC,8HAA8H;UAAAxF,QAAA,eAC3IX,OAAA;YAAKmG,SAAS,EAAC,sBAAsB;YAACgH,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAzM,QAAA,eAC3EX,OAAA;cAAMqN,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,mNAAmN;cAACC,QAAQ,EAAC;YAAS;cAAA3M,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNf,OAAA;UAAImG,SAAS,EAAC,wCAAwC;UAAAxF,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9Ef,OAAA;UAAGmG,SAAS,EAAC,6CAA6C;UAAAxF,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJf,OAAA;UAAImG,SAAS,EAAC,yCAAyC;UAAAxF,QAAA,gBACrDX,OAAA;YAAAW,QAAA,EAAI;UAA4C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrDf,OAAA;YAAAW,QAAA,EAAI;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrCf,OAAA;YAAAW,QAAA,EAAI;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACLf,OAAA;UAAKmG,SAAS,EAAC,WAAW;UAAAxF,QAAA,gBACxBX,OAAA;YACEsE,OAAO,EAAEoI,mBAAoB;YAC7BvG,SAAS,EAAC,iNAAiN;YAAAxF,QAAA,EAC5N;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTf,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAM;cACbkE,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;cAC1CF,WAAW,CAAC,CAAC;YACf,CAAE;YACFpC,SAAS,EAAC,2MAA2M;YAAAxF,QAAA,EACtN;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTf,OAAA;YACEsE,OAAO,EAAEA,CAAA,KAAMqD,QAAQ,CAAC,YAAY,CAAE;YACtCxB,SAAS,EAAC,2MAA2M;YAAAxF,QAAA,EACtN;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENf,OAAA,CAAC4N,kBAAkB;MACjB1N,QAAQ,EAAEqF,SAAS,CAACgC,qBAAqB,CAAE;MAC3CpH,aAAa,EAAEoH,qBAAsB;MACrCnH,cAAc,EAAEmF,SAAS,CAACvB,MAAO;MACjC3D,cAAc,EAAEmF,eAAe,CAAC+B,qBAAqB,CAAE;MACvDjH,cAAc,EAAGuN,MAAM,IAAK/H,kBAAkB,CAAC;QAAC,GAAGN,eAAe;QAAE,CAAC+B,qBAAqB,GAAGsG;MAAM,CAAC,CAAE;MACtGtN,MAAM,EAAEA,CAAA,KAAM;QACZ,IAAIgH,qBAAqB,KAAKhC,SAAS,CAACvB,MAAM,GAAG,CAAC,EAAE;UAClDqF,eAAe,CAAC,CAAC;QACnB,CAAC,MAAM;UACLxD,wBAAwB,CAAC0B,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACF/G,UAAU,EAAEA,CAAA,KAAM;QAChB,IAAI+G,qBAAqB,GAAG,CAAC,EAAE;UAC7B1B,wBAAwB,CAAC0B,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACF9G,QAAQ,EAAEoH,WAAY;MACtBnH,SAAS,EAAE,CAAAkF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE3E,IAAI,KAAI;IAAO;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CACF,CACF,EAEA6G,IAAI,KAAK,QAAQ,iBAChB5H,OAAA;MAAKmG,SAAS,EAAC,6EAA6E;MAAAxF,QAAA,GACzF6G,MAAM,CAAC2D,OAAO,KAAK,MAAM,iBAAInL,OAAA,CAACR,QAAQ;QAAC+E,KAAK,EAAEA,KAAM;QAACG,MAAM,EAAEA;MAAO;QAAA9D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAExEf,OAAA;QAAKmG,SAAS,EAAC,wBAAwB;QAAAxF,QAAA,eACrCX,OAAA;UAAKmG,SAAS,EAAC,+FAA+F;UAAAxF,QAAA,gBAE5GX,OAAA;YAAKmG,SAAS,EAAG,mCACfqB,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACrB,sEAAsE,GACtE,oEACL,EAAE;YAAAxK,QAAA,eACDX,OAAA;cAAKmG,SAAS,EAAC,UAAU;cAAAxF,QAAA,gBACvBX,OAAA;gBAAKmG,SAAS,EAAG,iFACfqB,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACrB,iDAAiD,GACjD,gDACL,EAAE;gBAAAxK,QAAA,eACDX,OAAA;kBACEyD,GAAG,EAAE+D,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG7L,IAAI,GAAGC,IAAK;kBAC7CmE,GAAG,EAAE8D,MAAM,CAAC2D,OAAQ;kBACpBhF,SAAS,EAAC;gBAA0B;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNf,OAAA;gBAAImG,SAAS,EAAG,2CACdqB,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;gBAAAxK,QAAA,EACA6G,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,iBAAiB,GAAG;cAAe;gBAAAvK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACLf,OAAA;gBAAGmG,SAAS,EAAC,qEAAqE;gBAAAxF,QAAA,EAC/E6G,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACtB,+CAA+C,GAC/C;cAAgD;gBAAAvK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNf,OAAA;YAAKmG,SAAS,EAAC,KAAK;YAAAxF,QAAA,gBAClBX,OAAA;cAAKmG,SAAS,EAAC,4CAA4C;cAAAxF,QAAA,gBAEzDX,OAAA;gBAAKmG,SAAS,EAAC,yKAAyK;gBAAAxF,QAAA,gBACtLX,OAAA;kBAAKmG,SAAS,EAAC;gBAAqI;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3Jf,OAAA;kBAAKmG,SAAS,EAAC,sBAAsB;kBAAAxF,QAAA,gBACnCX,OAAA;oBAAKmG,SAAS,EAAC,uDAAuD;oBAAAxF,QAAA,GACnEmB,IAAI,CAACiJ,KAAK,CAAE,CAAC,EAAA9D,qBAAA,GAAAO,MAAM,CAACyC,cAAc,cAAAhD,qBAAA,uBAArBA,qBAAA,CAAuBjD,MAAM,KAAI,CAAC,IAAIuB,SAAS,CAACvB,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;kBAAA;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNf,OAAA;oBAAKmG,SAAS,EAAC,6DAA6D;oBAAAxF,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNf,OAAA;gBAAKmG,SAAS,EAAC,8KAA8K;gBAAAxF,QAAA,gBAC3LX,OAAA;kBAAKmG,SAAS,EAAC;gBAAwI;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9Jf,OAAA;kBAAKmG,SAAS,EAAC,sBAAsB;kBAAAxF,QAAA,gBACnCX,OAAA;oBAAKmG,SAAS,EAAC,0DAA0D;oBAAAxF,QAAA,GACtE,EAAAuG,sBAAA,GAAAM,MAAM,CAACyC,cAAc,cAAA/C,sBAAA,uBAArBA,sBAAA,CAAuBlD,MAAM,KAAI,CAAC,EAAC,GAAC,EAACuB,SAAS,CAACvB,MAAM;kBAAA;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACNf,OAAA;oBAAKmG,SAAS,EAAC,gEAAgE;oBAAAxF,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNf,OAAA;gBAAKmG,SAAS,EAAG,qGACfqB,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACrB,4EAA4E,GAC5E,yEACL,EAAE;gBAAAxK,QAAA,gBACDX,OAAA;kBAAKmG,SAAS,EAAG,wGACfqB,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,oBAAoB,GAAG,kBACpD;gBAAiB;kBAAAvK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBf,OAAA;kBAAKmG,SAAS,EAAC,sBAAsB;kBAAAxF,QAAA,gBACnCX,OAAA;oBAAKmG,SAAS,EAAG,2CACfqB,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;oBAAAxK,QAAA,EACA6G,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG;kBAAO;oBAAAvK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC,eACNf,OAAA;oBAAKmG,SAAS,EAAG,8CACfqB,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,qBAAqB,GAAG,mBACrD,EAAE;oBAAAxK,QAAA,EACA6G,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,UAAU,GAAI,QAAOvF,QAAQ,CAACsF,YAAa;kBAAC;oBAAAtK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNf,OAAA;cAAKmG,SAAS,EAAC,MAAM;cAAAxF,QAAA,eACnBX,OAAA;gBAAKmG,SAAS,EAAC,uCAAuC;gBAAAxF,QAAA,gBACpDX,OAAA;kBAAKmG,SAAS,EAAC,kBAAkB;kBAAAxF,QAAA,gBAC/BX,OAAA;oBAAImG,SAAS,EAAC,uCAAuC;oBAAAxF,QAAA,EAAC;kBAAoB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/Ef,OAAA;oBAAGmG,SAAS,EAAC,wBAAwB;oBAAAxF,QAAA,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACNf,OAAA;kBAAKmG,SAAS,EAAC,UAAU;kBAAAxF,QAAA,gBACvBX,OAAA;oBAAKmG,SAAS,EAAC,mEAAmE;oBAAAxF,QAAA,eAChFX,OAAA;sBACEmG,SAAS,EAAG,uFACVqB,MAAM,CAAC2D,OAAO,KAAK,MAAM,GACrB,6DAA6D,GAC7D,2DACL,EAAE;sBACHlJ,KAAK,EAAE;wBAAEsC,KAAK,EAAG,GAAG,CAAC,EAAA4C,sBAAA,GAAAK,MAAM,CAACyC,cAAc,cAAA9C,sBAAA,uBAArBA,sBAAA,CAAuBnD,MAAM,KAAI,CAAC,IAAIuB,SAAS,CAACvB,MAAM,GAAI,GAAI;sBAAG,CAAE;sBAAArD,QAAA,eAExFX,OAAA;wBAAKmG,SAAS,EAAC;sBAAgE;wBAAAvF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNf,OAAA;oBAAKmG,SAAS,EAAC,wCAAwC;oBAAAxF,QAAA,gBACrDX,OAAA;sBAAMmG,SAAS,EAAC,oCAAoC;sBAAAxF,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9Df,OAAA;sBAAMmG,SAAS,EAAG,qCAChBqB,MAAM,CAAC2D,OAAO,KAAK,MAAM,GAAG,kBAAkB,GAAG,gBAClD,EAAE;sBAAAxK,QAAA,GACAmB,IAAI,CAACiJ,KAAK,CAAE,CAAC,EAAA3D,sBAAA,GAAAI,MAAM,CAACyC,cAAc,cAAA7C,sBAAA,uBAArBA,sBAAA,CAAuBpD,MAAM,KAAI,CAAC,IAAIuB,SAAS,CAACvB,MAAM,GAAI,GAAG,CAAC,EAAC,GAC/E;oBAAA;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACPf,OAAA;sBAAMmG,SAAS,EAAC,oCAAoC;sBAAAxF,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLyG,MAAM,CAACgE,MAAM,iBACZxL,OAAA;cAAKmG,SAAS,EAAC,MAAM;cAAAxF,QAAA,eACnBX,OAAA,CAACF,eAAe;gBAAC0L,MAAM,EAAEhE,MAAM,CAACgE;cAAO;gBAAA5K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACN,eAGDf,OAAA;cAAKmG,SAAS,EAAC,gDAAgD;cAAAxF,QAAA,eAC7DX,OAAA;gBACEmG,SAAS,EAAC,sPAAsP;gBAChQ7B,OAAO,EAAEA,CAAA,KAAMqB,OAAO,CAAC,QAAQ,CAAE;gBAAAhF,QAAA,gBAEjCX,OAAA;kBAAKmG,SAAS,EAAC;gBAAkI;kBAAAvF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxJf,OAAA;kBAAMmG,SAAS,EAAC,UAAU;kBAAAxF,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAEA6G,IAAI,KAAK,QAAQ,iBAChB5H,OAAA,CAACsF,oBAAoB;MACnBC,SAAS,EAAEA,SAAU;MACrBC,eAAe,EAAEA,eAAgB;MACjCC,YAAY,EAAEA,YAAa;MAC3BC,gBAAgB,EAAEA,gBAAiB;MACnCC,OAAO,EAAEA,OAAQ;MACjBC,QAAQ,EAAEA,QAAS;MACnBC,wBAAwB,EAAEA,wBAAyB;MACnDC,kBAAkB,EAAEA,kBAAmB;MACvCC,SAAS,EAAEA,SAAU;MACrBC,SAAS,EAAEA,SAAU;MACrBC,cAAc,EAAEA,cAAe;MAC/BC,eAAe,EAAEA;IAAgB;MAAAtF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC,GACJ,IAAI;AACV;AAACiG,EAAA,CAjlBQD,SAAS;EAAA,QAMD/H,SAAS,EACPH,WAAW,EACXE,WAAW,EAOXD,WAAW,EAEFW,aAAa;AAAA;AAAAqO,GAAA,GAjBhC/G,SAAS;AAmlBlB,eAAeA,SAAS;AAAC,IAAA1B,EAAA,EAAAyB,GAAA,EAAAgH,GAAA;AAAAC,YAAA,CAAA1I,EAAA;AAAA0I,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}