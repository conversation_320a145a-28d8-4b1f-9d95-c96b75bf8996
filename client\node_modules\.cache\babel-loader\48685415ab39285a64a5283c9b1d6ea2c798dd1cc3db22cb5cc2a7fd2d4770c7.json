{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { Tb<PERSON>ser, TbUsers, TbTrophy, TbPlayerPlay, TbPlayerPause, TbClock, TbSearch, TbFilter } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingList = ({\n  users = [],\n  currentUserId = null,\n  layout = 'horizontal',\n  // 'horizontal', 'vertical', 'grid'\n  size = 'medium',\n  showStats = true,\n  className = '',\n  currentUserRef = null,\n  showFindMe = false,\n  lastUpdated = null,\n  autoRefresh = false,\n  onAutoRefreshToggle = null\n}) => {\n  _s();\n  // State for search and filtering\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'\n  const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'\n  const [viewMode, setViewMode] = useState('card'); // 'card', 'compact', 'detailed'\n  const [showOnlyMyClass, setShowOnlyMyClass] = useState(false);\n  const [localShowFindMe, setLocalShowFindMe] = useState(false);\n  const localCurrentUserRef = useRef(null);\n\n  // Use passed refs or local ones\n  const userRef = currentUserRef || localCurrentUserRef;\n  const findMeActive = showFindMe || localShowFindMe;\n\n  // Get current user's class for filtering\n  const currentUser = users.find(user => user.userId === currentUserId || user._id === currentUserId);\n  const currentUserClass = currentUser === null || currentUser === void 0 ? void 0 : currentUser.class;\n\n  // Filter and search users\n  const filteredUsers = users.filter(user => {\n    var _user$name, _user$email, _user$class, _user$normalizedSubsc, _user$subscriptionSta;\n    // Search filter\n    const matchesSearch = ((_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.toLowerCase().includes(searchTerm.toLowerCase())) || ((_user$class = user.class) === null || _user$class === void 0 ? void 0 : _user$class.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Class filter\n    const matchesClass = !showOnlyMyClass || user.class === currentUserClass;\n\n    // Subscription filter\n    const userStatus = ((_user$normalizedSubsc = user.normalizedSubscriptionStatus) === null || _user$normalizedSubsc === void 0 ? void 0 : _user$normalizedSubsc.toLowerCase()) || ((_user$subscriptionSta = user.subscriptionStatus) === null || _user$subscriptionSta === void 0 ? void 0 : _user$subscriptionSta.toLowerCase()) || 'free';\n    let matchesFilter = true;\n    switch (filterType) {\n      case 'premium':\n        matchesFilter = userStatus === 'premium' || userStatus === 'active';\n        break;\n      case 'expired':\n        matchesFilter = userStatus === 'expired';\n        break;\n      case 'free':\n        matchesFilter = userStatus === 'free';\n        break;\n      default:\n        matchesFilter = true;\n    }\n    return matchesSearch && matchesFilter && matchesClass;\n  }).sort((a, b) => {\n    switch (sortBy) {\n      case 'xp':\n        return (b.totalXP || 0) - (a.totalXP || 0);\n      case 'name':\n        return (a.name || '').localeCompare(b.name || '');\n      case 'score':\n        return (b.rankingScore || b.score || 0) - (a.rankingScore || a.score || 0);\n      case 'class':\n        return (a.class || '').localeCompare(b.class || '');\n      default:\n        return (a.rank || 0) - (b.rank || 0);\n    }\n  });\n\n  // Calculate class ranks for filtered users\n  const usersWithClassRank = filteredUsers.map(user => {\n    // Group users by class and calculate class rank\n    const sameClassUsers = filteredUsers.filter(u => u.class === user.class);\n    const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n    return {\n      ...user,\n      classRank\n    };\n  });\n\n  // Find current user in filtered list\n  const currentUserInList = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n\n  // Scroll to current user\n  const scrollToCurrentUser = () => {\n    if (userRef.current) {\n      userRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setLocalShowFindMe(true);\n      // Hide the highlight after 3 seconds\n      setTimeout(() => setLocalShowFindMe(false), 3000);\n    }\n  };\n\n  // Get layout classes based on view mode and layout\n  const getLayoutClasses = () => {\n    if (viewMode === 'compact') {\n      return 'space-y-2';\n    }\n    switch (layout) {\n      case 'vertical':\n        return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n      case 'grid':\n        return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';\n      case 'horizontal':\n      default:\n        return 'space-y-4';\n    }\n  };\n\n  // Stats summary with enhanced calculations\n  const totalUsers = users.length;\n  const premiumUsers = users.filter(u => u.subscriptionStatus === 'active' || u.subscriptionStatus === 'premium' || u.normalizedSubscriptionStatus === 'premium').length;\n\n  // Use ranking score or XP as the primary metric\n  const topScore = users.length > 0 ? Math.max(...users.map(u => u.rankingScore || u.totalXP || u.totalPoints || 0)) : 0;\n\n  // Calculate additional stats\n  const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n  const averageXP = users.length > 0 ? Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200 animate-fadeInUp\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6 bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(TbSearch, {\n                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                id: \"ranking-search\",\n                name: \"ranking-search\",\n                type: \"text\",\n                placeholder: \"Search by name, email, or class...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                autoComplete: \"off\",\n                className: \"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 37\n              }, this), searchTerm && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSearchTerm(''),\n                className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                children: \"\\u2715\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex gap-2\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowOnlyMyClass(!showOnlyMyClass),\n                className: `px-4 py-2 rounded-lg font-medium transition-all duration-200 ${showOnlyMyClass ? 'bg-blue-500 text-white shadow-md' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n                children: \"My Class Only\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(TbFilter, {\n                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterType,\n                onChange: e => setFilterType(e.target.value),\n                className: \"pl-10 pr-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"all\",\n                  children: \"All Subscriptions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"premium\",\n                  children: \"\\uD83D\\uDC51 Premium Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"free\",\n                  children: \"\\uD83C\\uDD93 Free Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"expired\",\n                  children: \"\\u23F0 Expired Users\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: sortBy,\n              onChange: e => setSortBy(e.target.value),\n              className: \"px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"rank\",\n                children: \"\\uD83C\\uDFC6 Sort by Rank\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"xp\",\n                children: \"\\u26A1 Sort by XP\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"score\",\n                children: \"\\uD83D\\uDCCA Sort by Score\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"name\",\n                children: \"\\uD83D\\uDCDD Sort by Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"class\",\n                children: \"\\uD83C\\uDF93 Sort by Class\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex bg-gray-100 rounded-lg p-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('card'),\n                className: `px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${viewMode === 'card' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-800'}`,\n                children: \"Cards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setViewMode('compact'),\n                className: `px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${viewMode === 'compact' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-800'}`,\n                children: \"Compact\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-gray-800\",\n              children: usersWithClassRank.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 33\n            }, this), \" of \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold text-gray-800\",\n              children: users.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 44\n            }, this), \" users\", searchTerm && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-1\",\n              children: [\"matching \", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium text-blue-600\",\n                children: [\"\\\"\", searchTerm, \"\\\"\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 50\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 37\n            }, this), filterType !== 'all' && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium\",\n              children: filterType\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 37\n            }, this), showOnlyMyClass && currentUserClass && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium\",\n              children: [\"Class \", currentUserClass]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex gap-4 text-xs text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\uD83C\\uDFC6 Top: \", Math.max(...usersWithClassRank.map(u => u.rankingScore || u.totalXP || 0)).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\uD83D\\uDCCA Avg: \", Math.round(usersWithClassRank.reduce((sum, u) => sum + (u.rankingScore || u.totalXP || 0), 0) / usersWithClassRank.length || 0).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-7 h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent\",\n                children: \"Leaderboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: \"Top performers across all levels\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 29\n          }, this), lastUpdated && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200\",\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4 text-blue-600\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-blue-700 font-medium\",\n              children: [\"Updated \", new Date(lastUpdated).toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [onAutoRefreshToggle && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onAutoRefreshToggle,\n            className: `px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 ${autoRefresh ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`,\n            children: [autoRefresh ? /*#__PURE__*/_jsxDEV(TbPlayerPause, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 52\n            }, this) : /*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 92\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: autoRefresh ? 'Auto' : 'Manual'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 33\n          }, this), currentUserId && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: scrollToCurrentUser,\n            className: \"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95\",\n            children: [/*#__PURE__*/_jsxDEV(TbUser, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Find Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 sm:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-blue-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbUsers, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-blue-700\",\n              children: \"Total Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-blue-900 mb-1\",\n            children: totalUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-blue-600 font-medium\",\n            children: [activeUsers, \" active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-yellow-700\",\n              children: \"Premium Users\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-yellow-900 mb-1\",\n            children: premiumUsers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-yellow-600 font-medium\",\n            children: [totalUsers > 0 ? Math.round(premiumUsers / totalUsers * 100) : 0, \"% premium\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-green-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbUser, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-green-700\",\n              children: \"Top Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-green-900 mb-1\",\n            children: topScore.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-green-600 font-medium\",\n            children: \"ranking points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-2 bg-purple-500 rounded-lg\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-semibold text-purple-700\",\n              children: \"Avg XP\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-3xl font-black text-purple-900 mb-1\",\n            children: averageXP.toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 mt-1\",\n            children: \"experience points\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `animate-fadeInUp ${getLayoutClasses()}`,\n      children: usersWithClassRank.map((user, index) => {\n        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n        const rank = user.rank || index + 1;\n\n        // Render compact view for better performance with large lists\n        if (viewMode === 'compact') {\n          var _ref, _ref2, _ref3, _ref4, _ref5, _ref6;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: isCurrentUser ? userRef : null,\n            className: `animate-slideInLeft transition-all duration-200 p-3 rounded-lg border ${isCurrentUser && findMeActive ? 'find-me-highlight ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200' : isCurrentUser ? 'ring-2 ring-blue-400 bg-blue-50 border-blue-200' : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-md'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${rank <= 3 ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white' : rank <= 10 ? 'bg-gradient-to-r from-blue-400 to-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`,\n                  children: rank\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: user.profilePicture || '/default-avatar.png',\n                    alt: user.name,\n                    className: \"w-8 h-8 rounded-full object-cover border-2 border-gray-200\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-semibold text-gray-900 text-sm\",\n                      children: user.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 420,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: [\"Class \", user.class]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-right\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"font-bold text-gray-900 text-sm\",\n                    children: (user.rankingScore || user.totalXP || 0).toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-gray-500\",\n                    children: user.rankingScore ? 'pts' : 'XP'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-2 py-1 rounded-full text-xs font-medium ${((_ref = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref === void 0 ? void 0 : _ref.toLowerCase()) === 'premium' || ((_ref2 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref2 === void 0 ? void 0 : _ref2.toLowerCase()) === 'active' ? 'bg-yellow-100 text-yellow-800' : ((_ref3 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref3 === void 0 ? void 0 : _ref3.toLowerCase()) === 'expired' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'}`,\n                  children: ((_ref4 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref4 === void 0 ? void 0 : _ref4.toLowerCase()) === 'premium' || ((_ref5 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref5 === void 0 ? void 0 : _ref5.toLowerCase()) === 'active' ? '👑' : ((_ref6 = user.normalizedSubscriptionStatus || user.subscriptionStatus) === null || _ref6 === void 0 ? void 0 : _ref6.toLowerCase()) === 'expired' ? '⏰' : '🆓'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 33\n            }, this)\n          }, user.userId || user._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 29\n          }, this);\n        }\n\n        // Regular card view\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: isCurrentUser ? userRef : null,\n          className: `animate-slideInLeft transition-all duration-300 ${isCurrentUser && findMeActive ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl' : isCurrentUser ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(UserRankingCard, {\n            user: user,\n            rank: rank,\n            classRank: user.classRank,\n            isCurrentUser: isCurrentUser,\n            layout: layout,\n            size: size,\n            showStats: showStats\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 33\n          }, this)\n        }, user.userId || user._id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 25\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 382,\n      columnNumber: 13\n    }, this), usersWithClassRank.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12 bg-white rounded-xl border border-gray-200 animate-fadeInUp\",\n      children: [/*#__PURE__*/_jsxDEV(TbUsers, {\n        className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-gray-900 mb-2\",\n        children: \"No users found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500\",\n        children: users.length === 0 ? 'No ranking data available.' : 'Try adjusting your search or filter criteria.'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 17\n    }, this), currentUserId && usersWithClassRank.length > 10 && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: scrollToCurrentUser,\n      className: \"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 hover:scale-110 active:scale-95 animate-bounce\",\n      title: \"Find me in ranking\",\n      children: /*#__PURE__*/_jsxDEV(TbUser, {\n        className: \"w-6 h-6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 9\n  }, this);\n};\n_s(UserRankingList, \"s9yOzICRFbNABlQU30hbh3VQGA4=\");\n_c = UserRankingList;\nexport default UserRankingList;\nvar _c;\n$RefreshReg$(_c, \"UserRankingList\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "TbUser", "TbUsers", "TbTrophy", "TbPlayerPlay", "TbPlayerPause", "TbClock", "TbSearch", "Tb<PERSON><PERSON>er", "UserRankingCard", "jsxDEV", "_jsxDEV", "UserRankingList", "users", "currentUserId", "layout", "size", "showStats", "className", "currentUserRef", "showFindMe", "lastUpdated", "autoRefresh", "onAutoRefreshToggle", "_s", "searchTerm", "setSearchTerm", "filterType", "setFilterType", "sortBy", "setSortBy", "viewMode", "setViewMode", "showOnlyMyClass", "setShowOnlyMyClass", "localShowFindMe", "setLocalShowFindMe", "localCurrentUserRef", "userRef", "findMeActive", "currentUser", "find", "user", "userId", "_id", "currentUserClass", "class", "filteredUsers", "filter", "_user$name", "_user$email", "_user$class", "_user$normalizedSubsc", "_user$subscriptionSta", "matchesSearch", "name", "toLowerCase", "includes", "email", "matchesClass", "userStatus", "normalizedSubscriptionStatus", "subscriptionStatus", "matchesFilter", "sort", "a", "b", "totalXP", "localeCompare", "rankingScore", "score", "rank", "usersWithClassRank", "map", "sameClassUsers", "u", "classRank", "findIndex", "currentUserInList", "scrollToCurrentUser", "current", "scrollIntoView", "behavior", "block", "setTimeout", "getLayoutClasses", "totalUsers", "length", "premiumUsers", "topScore", "Math", "max", "totalPoints", "activeUsers", "totalQuizzesTaken", "averageXP", "round", "reduce", "sum", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "type", "placeholder", "value", "onChange", "e", "target", "autoComplete", "onClick", "toLocaleString", "Date", "toLocaleTimeString", "index", "isCurrentUser", "_ref", "_ref2", "_ref3", "_ref4", "_ref5", "_ref6", "ref", "src", "profilePicture", "alt", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingList.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { TbUser, TbUsers, Tb<PERSON>rophy, TbPlayerPlay, TbPlayerPause, Tb<PERSON>lock, TbSearch, TbFilter } from 'react-icons/tb';\nimport UserRankingCard from './UserRankingCard';\n\nconst UserRankingList = ({\n    users = [],\n    currentUserId = null,\n    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'\n    size = 'medium',\n    showStats = true,\n    className = '',\n    currentUserRef = null,\n    showFindMe = false,\n    lastUpdated = null,\n    autoRefresh = false,\n    onAutoRefreshToggle = null\n}) => {\n    // State for search and filtering\n    const [searchTerm, setSearchTerm] = useState('');\n    const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'\n    const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'\n    const [viewMode, setViewMode] = useState('card'); // 'card', 'compact', 'detailed'\n    const [showOnlyMyClass, setShowOnlyMyClass] = useState(false);\n    const [localShowFindMe, setLocalShowFindMe] = useState(false);\n    const localCurrentUserRef = useRef(null);\n\n    // Use passed refs or local ones\n    const userRef = currentUserRef || localCurrentUserRef;\n    const findMeActive = showFindMe || localShowFindMe;\n\n    // Get current user's class for filtering\n    const currentUser = users.find(user => user.userId === currentUserId || user._id === currentUserId);\n    const currentUserClass = currentUser?.class;\n\n    // Filter and search users\n    const filteredUsers = users.filter(user => {\n        // Search filter\n        const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                            user.class?.toLowerCase().includes(searchTerm.toLowerCase());\n\n        // Class filter\n        const matchesClass = !showOnlyMyClass || user.class === currentUserClass;\n\n        // Subscription filter\n        const userStatus = user.normalizedSubscriptionStatus?.toLowerCase() || user.subscriptionStatus?.toLowerCase() || 'free';\n        let matchesFilter = true;\n\n        switch (filterType) {\n            case 'premium':\n                matchesFilter = userStatus === 'premium' || userStatus === 'active';\n                break;\n            case 'expired':\n                matchesFilter = userStatus === 'expired';\n                break;\n            case 'free':\n                matchesFilter = userStatus === 'free';\n                break;\n            default:\n                matchesFilter = true;\n        }\n\n        return matchesSearch && matchesFilter && matchesClass;\n    }).sort((a, b) => {\n        switch (sortBy) {\n            case 'xp':\n                return (b.totalXP || 0) - (a.totalXP || 0);\n            case 'name':\n                return (a.name || '').localeCompare(b.name || '');\n            case 'score':\n                return (b.rankingScore || b.score || 0) - (a.rankingScore || a.score || 0);\n            case 'class':\n                return (a.class || '').localeCompare(b.class || '');\n            default:\n                return (a.rank || 0) - (b.rank || 0);\n        }\n    });\n\n    // Calculate class ranks for filtered users\n    const usersWithClassRank = filteredUsers.map(user => {\n        // Group users by class and calculate class rank\n        const sameClassUsers = filteredUsers.filter(u => u.class === user.class);\n        const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;\n        return { ...user, classRank };\n    });\n\n    // Find current user in filtered list\n    const currentUserInList = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);\n\n    // Scroll to current user\n    const scrollToCurrentUser = () => {\n        if (userRef.current) {\n            userRef.current.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center'\n            });\n            setLocalShowFindMe(true);\n            // Hide the highlight after 3 seconds\n            setTimeout(() => setLocalShowFindMe(false), 3000);\n        }\n    };\n\n    // Get layout classes based on view mode and layout\n    const getLayoutClasses = () => {\n        if (viewMode === 'compact') {\n            return 'space-y-2';\n        }\n\n        switch (layout) {\n            case 'vertical':\n                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';\n            case 'grid':\n                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';\n            case 'horizontal':\n            default:\n                return 'space-y-4';\n        }\n    };\n\n\n\n    // Stats summary with enhanced calculations\n    const totalUsers = users.length;\n    const premiumUsers = users.filter(u =>\n        u.subscriptionStatus === 'active' ||\n        u.subscriptionStatus === 'premium' ||\n        u.normalizedSubscriptionStatus === 'premium'\n    ).length;\n\n    // Use ranking score or XP as the primary metric\n    const topScore = users.length > 0 ? Math.max(...users.map(u =>\n        u.rankingScore || u.totalXP || u.totalPoints || 0\n    )) : 0;\n\n    // Calculate additional stats\n    const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;\n    const averageXP = users.length > 0 ?\n        Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;\n\n    return (\n        <div className={`space-y-6 ${className}`}>\n            {/* Header with Stats */}\n            {showStats && (\n                <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200 animate-fadeInUp\">\n                    {/* Enhanced Search and Filter Section */}\n                    <div className=\"mb-6 bg-white/90 backdrop-blur-sm rounded-xl p-6 border border-white/50 shadow-lg\">\n                        <div className=\"flex flex-col gap-4\">\n                            {/* Top Row - Search and Quick Filters */}\n                            <div className=\"flex flex-col sm:flex-row gap-4\">\n                                {/* Search Input */}\n                                <div className=\"flex-1 relative\">\n                                    <TbSearch className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                                    <input\n                                        id=\"ranking-search\"\n                                        name=\"ranking-search\"\n                                        type=\"text\"\n                                        placeholder=\"Search by name, email, or class...\"\n                                        value={searchTerm}\n                                        onChange={(e) => setSearchTerm(e.target.value)}\n                                        autoComplete=\"off\"\n                                        className=\"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n                                    />\n                                    {searchTerm && (\n                                        <button\n                                            onClick={() => setSearchTerm('')}\n                                            className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                                        >\n                                            ✕\n                                        </button>\n                                    )}\n                                </div>\n\n                                {/* Quick Filter Buttons */}\n                                <div className=\"flex gap-2\">\n                                    <button\n                                        onClick={() => setShowOnlyMyClass(!showOnlyMyClass)}\n                                        className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${\n                                            showOnlyMyClass\n                                                ? 'bg-blue-500 text-white shadow-md'\n                                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                                        }`}\n                                    >\n                                        My Class Only\n                                    </button>\n                                </div>\n                            </div>\n\n                            {/* Bottom Row - Dropdowns and View Mode */}\n                            <div className=\"flex flex-col sm:flex-row gap-4\">\n                                {/* Filter Dropdown */}\n                                <div className=\"relative\">\n                                    <TbFilter className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n                                    <select\n                                        value={filterType}\n                                        onChange={(e) => setFilterType(e.target.value)}\n                                        className=\"pl-10 pr-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n                                    >\n                                        <option value=\"all\">All Subscriptions</option>\n                                        <option value=\"premium\">👑 Premium Users</option>\n                                        <option value=\"free\">🆓 Free Users</option>\n                                        <option value=\"expired\">⏰ Expired Users</option>\n                                    </select>\n                                </div>\n\n                                {/* Sort Dropdown */}\n                                <select\n                                    value={sortBy}\n                                    onChange={(e) => setSortBy(e.target.value)}\n                                    className=\"px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm\"\n                                >\n                                    <option value=\"rank\">🏆 Sort by Rank</option>\n                                    <option value=\"xp\">⚡ Sort by XP</option>\n                                    <option value=\"score\">📊 Sort by Score</option>\n                                    <option value=\"name\">📝 Sort by Name</option>\n                                    <option value=\"class\">🎓 Sort by Class</option>\n                                </select>\n\n                                {/* View Mode Toggle */}\n                                <div className=\"flex bg-gray-100 rounded-lg p-1\">\n                                    <button\n                                        onClick={() => setViewMode('card')}\n                                        className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${\n                                            viewMode === 'card'\n                                                ? 'bg-white text-blue-600 shadow-sm'\n                                                : 'text-gray-600 hover:text-gray-800'\n                                        }`}\n                                    >\n                                        Cards\n                                    </button>\n                                    <button\n                                        onClick={() => setViewMode('compact')}\n                                        className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${\n                                            viewMode === 'compact'\n                                                ? 'bg-white text-blue-600 shadow-sm'\n                                                : 'text-gray-600 hover:text-gray-800'\n                                        }`}\n                                    >\n                                        Compact\n                                    </button>\n                                </div>\n                            </div>\n                        </div>\n\n                        {/* Enhanced Results Count and Stats */}\n                        <div className=\"mt-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2\">\n                            <div className=\"text-sm text-gray-600\">\n                                <span className=\"font-semibold text-gray-800\">\n                                    {usersWithClassRank.length}\n                                </span> of <span className=\"font-semibold text-gray-800\">{users.length}</span> users\n                                {searchTerm && (\n                                    <span className=\"ml-1\">\n                                        matching <span className=\"font-medium text-blue-600\">\"{searchTerm}\"</span>\n                                    </span>\n                                )}\n                                {filterType !== 'all' && (\n                                    <span className=\"ml-1 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium\">\n                                        {filterType}\n                                    </span>\n                                )}\n                                {showOnlyMyClass && currentUserClass && (\n                                    <span className=\"ml-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium\">\n                                        Class {currentUserClass}\n                                    </span>\n                                )}\n                            </div>\n\n                            {/* Quick Stats */}\n                            <div className=\"flex gap-4 text-xs text-gray-500\">\n                                <span>🏆 Top: {Math.max(...usersWithClassRank.map(u => u.rankingScore || u.totalXP || 0)).toLocaleString()}</span>\n                                <span>📊 Avg: {Math.round(usersWithClassRank.reduce((sum, u) => sum + (u.rankingScore || u.totalXP || 0), 0) / usersWithClassRank.length || 0).toLocaleString()}</span>\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"flex items-center justify-between mb-6\">\n                        <div className=\"flex items-center space-x-4\">\n                            <div className=\"flex items-center space-x-3\">\n                                <div className=\"p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg\">\n                                    <TbTrophy className=\"w-7 h-7 text-white\" />\n                                </div>\n                                <div>\n                                    <h2 className=\"text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent\">\n                                        Leaderboard\n                                    </h2>\n                                    <p className=\"text-sm text-gray-600 font-medium\">Top performers across all levels</p>\n                                </div>\n                            </div>\n\n                            {lastUpdated && (\n                                <div className=\"flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200\">\n                                    <TbClock className=\"w-4 h-4 text-blue-600\" />\n                                    <span className=\"text-sm text-blue-700 font-medium\">\n                                        Updated {new Date(lastUpdated).toLocaleTimeString()}\n                                    </span>\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"flex items-center space-x-2\">\n                            {/* Auto-refresh toggle */}\n                            {onAutoRefreshToggle && (\n                                <button\n                                    onClick={onAutoRefreshToggle}\n                                    className={`px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95 ${\n                                        autoRefresh\n                                            ? 'bg-green-500 hover:bg-green-600 text-white'\n                                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'\n                                    }`}\n                                >\n                                    {autoRefresh ? <TbPlayerPause className=\"w-4 h-4\" /> : <TbPlayerPlay className=\"w-4 h-4\" />}\n                                    <span className=\"hidden sm:inline\">\n                                        {autoRefresh ? 'Auto' : 'Manual'}\n                                    </span>\n                                </button>\n                            )}\n\n\n\n                            {/* Find Me button */}\n                            {currentUserId && (\n                                <button\n                                    onClick={scrollToCurrentUser}\n                                    className=\"bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 hover:scale-105 active:scale-95\"\n                                >\n                                    <TbUser className=\"w-4 h-4\" />\n                                    <span className=\"hidden sm:inline\">Find Me</span>\n                                </button>\n                            )}\n                        </div>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-4\">\n                        <div className=\"bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-blue-500 rounded-lg\">\n                                    <TbUsers className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-blue-700\">Total Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-blue-900 mb-1\">{totalUsers}</div>\n                            <div className=\"text-xs text-blue-600 font-medium\">{activeUsers} active</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-yellow-700\">Premium Users</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-yellow-900 mb-1\">{premiumUsers}</div>\n                            <div className=\"text-xs text-yellow-600 font-medium\">\n                                {totalUsers > 0 ? Math.round((premiumUsers / totalUsers) * 100) : 0}% premium\n                            </div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-green-500 rounded-lg\">\n                                    <TbUser className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-green-700\">Top Score</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-green-900 mb-1\">{topScore.toLocaleString()}</div>\n                            <div className=\"text-xs text-green-600 font-medium\">ranking points</div>\n                        </div>\n\n                        <div className=\"bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                            <div className=\"flex items-center space-x-3 mb-3\">\n                                <div className=\"p-2 bg-purple-500 rounded-lg\">\n                                    <TbTrophy className=\"w-5 h-5 text-white\" />\n                                </div>\n                                <span className=\"text-sm font-semibold text-purple-700\">Avg XP</span>\n                            </div>\n                            <div className=\"text-3xl font-black text-purple-900 mb-1\">{averageXP.toLocaleString()}</div>\n                            <div className=\"text-xs text-gray-500 mt-1\">experience points</div>\n                        </div>\n                    </div>\n                </div>\n            )}\n\n            {/* User List */}\n            <div className={`animate-fadeInUp ${getLayoutClasses()}`}>\n                {usersWithClassRank.map((user, index) => {\n                    const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;\n                    const rank = user.rank || index + 1;\n\n                    // Render compact view for better performance with large lists\n                    if (viewMode === 'compact') {\n                        return (\n                            <div\n                                key={user.userId || user._id}\n                                ref={isCurrentUser ? userRef : null}\n                                className={`animate-slideInLeft transition-all duration-200 p-3 rounded-lg border ${\n                                    isCurrentUser && findMeActive\n                                        ? 'find-me-highlight ring-2 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200'\n                                        : isCurrentUser\n                                        ? 'ring-2 ring-blue-400 bg-blue-50 border-blue-200'\n                                        : 'bg-white border-gray-200 hover:border-gray-300 hover:shadow-md'\n                                }`}\n                            >\n                                <div className=\"flex items-center justify-between\">\n                                    <div className=\"flex items-center space-x-3\">\n                                        {/* Rank Badge */}\n                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${\n                                            rank <= 3 ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white' :\n                                            rank <= 10 ? 'bg-gradient-to-r from-blue-400 to-blue-500 text-white' :\n                                            'bg-gray-100 text-gray-600'\n                                        }`}>\n                                            {rank}\n                                        </div>\n\n                                        {/* User Info */}\n                                        <div className=\"flex items-center space-x-2\">\n                                            <img\n                                                src={user.profilePicture || '/default-avatar.png'}\n                                                alt={user.name}\n                                                className=\"w-8 h-8 rounded-full object-cover border-2 border-gray-200\"\n                                            />\n                                            <div>\n                                                <div className=\"font-semibold text-gray-900 text-sm\">{user.name}</div>\n                                                <div className=\"text-xs text-gray-500\">Class {user.class}</div>\n                                            </div>\n                                        </div>\n                                    </div>\n\n                                    {/* Score and Badge */}\n                                    <div className=\"flex items-center space-x-3\">\n                                        <div className=\"text-right\">\n                                            <div className=\"font-bold text-gray-900 text-sm\">\n                                                {(user.rankingScore || user.totalXP || 0).toLocaleString()}\n                                            </div>\n                                            <div className=\"text-xs text-gray-500\">\n                                                {user.rankingScore ? 'pts' : 'XP'}\n                                            </div>\n                                        </div>\n\n                                        {/* Subscription Badge */}\n                                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${\n                                            (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'premium' ||\n                                            (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'active'\n                                                ? 'bg-yellow-100 text-yellow-800'\n                                                : (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'expired'\n                                                ? 'bg-red-100 text-red-800'\n                                                : 'bg-blue-100 text-blue-800'\n                                        }`}>\n                                            {(user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'premium' ||\n                                             (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'active' ? '👑' :\n                                             (user.normalizedSubscriptionStatus || user.subscriptionStatus)?.toLowerCase() === 'expired' ? '⏰' : '🆓'}\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        );\n                    }\n\n                    // Regular card view\n                    return (\n                        <div\n                            key={user.userId || user._id}\n                            ref={isCurrentUser ? userRef : null}\n                            className={`animate-slideInLeft transition-all duration-300 ${\n                                isCurrentUser && findMeActive\n                                    ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl'\n                                    : isCurrentUser\n                                    ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg'\n                                        : ''\n                                }`}\n                            >\n                                <UserRankingCard\n                                    user={user}\n                                    rank={rank}\n                                    classRank={user.classRank}\n                                    isCurrentUser={isCurrentUser}\n                                    layout={layout}\n                                    size={size}\n                                    showStats={showStats}\n                                />\n                            </div>\n                        );\n                    })}\n            </div>\n\n            {/* Empty State */}\n            {usersWithClassRank.length === 0 && (\n                <div className=\"text-center py-12 bg-white rounded-xl border border-gray-200 animate-fadeInUp\">\n                    <TbUsers className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No users found</h3>\n                    <p className=\"text-gray-500\">\n                        {users.length === 0 ? 'No ranking data available.' : 'Try adjusting your search or filter criteria.'}\n                    </p>\n                </div>\n            )}\n\n            {/* Floating Action Button for Current User */}\n            {currentUserId && usersWithClassRank.length > 10 && (\n                <button\n                    onClick={scrollToCurrentUser}\n                    className=\"fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 hover:scale-110 active:scale-95 animate-bounce\"\n                    title=\"Find me in ranking\"\n                >\n                    <TbUser className=\"w-6 h-6\" />\n                </button>\n            )}\n        </div>\n    );\n};\n\nexport default UserRankingList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AACpH,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,eAAe,GAAGA,CAAC;EACrBC,KAAK,GAAG,EAAE;EACVC,aAAa,GAAG,IAAI;EACpBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EACfC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG,EAAE;EACdC,cAAc,GAAG,IAAI;EACrBC,UAAU,GAAG,KAAK;EAClBC,WAAW,GAAG,IAAI;EAClBC,WAAW,GAAG,KAAK;EACnBC,mBAAmB,GAAG;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;EAClD,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMsC,mBAAmB,GAAGrC,MAAM,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMsC,OAAO,GAAGnB,cAAc,IAAIkB,mBAAmB;EACrD,MAAME,YAAY,GAAGnB,UAAU,IAAIe,eAAe;;EAElD;EACA,MAAMK,WAAW,GAAG3B,KAAK,CAAC4B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK7B,aAAa,IAAI4B,IAAI,CAACE,GAAG,KAAK9B,aAAa,CAAC;EACnG,MAAM+B,gBAAgB,GAAGL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,KAAK;;EAE3C;EACA,MAAMC,aAAa,GAAGlC,KAAK,CAACmC,MAAM,CAACN,IAAI,IAAI;IAAA,IAAAO,UAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACvC;IACA,MAAMC,aAAa,GAAG,EAAAL,UAAA,GAAAP,IAAI,CAACa,IAAI,cAAAN,UAAA,uBAATA,UAAA,CAAWO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC,OAAAN,WAAA,GAC7DR,IAAI,CAACgB,KAAK,cAAAR,WAAA,uBAAVA,WAAA,CAAYM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC,OAAAL,WAAA,GAC5DT,IAAI,CAACI,KAAK,cAAAK,WAAA,uBAAVA,WAAA,CAAYK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChC,UAAU,CAAC+B,WAAW,CAAC,CAAC,CAAC;;IAEhF;IACA,MAAMG,YAAY,GAAG,CAAC1B,eAAe,IAAIS,IAAI,CAACI,KAAK,KAAKD,gBAAgB;;IAExE;IACA,MAAMe,UAAU,GAAG,EAAAR,qBAAA,GAAAV,IAAI,CAACmB,4BAA4B,cAAAT,qBAAA,uBAAjCA,qBAAA,CAAmCI,WAAW,CAAC,CAAC,OAAAH,qBAAA,GAAIX,IAAI,CAACoB,kBAAkB,cAAAT,qBAAA,uBAAvBA,qBAAA,CAAyBG,WAAW,CAAC,CAAC,KAAI,MAAM;IACvH,IAAIO,aAAa,GAAG,IAAI;IAExB,QAAQpC,UAAU;MACd,KAAK,SAAS;QACVoC,aAAa,GAAGH,UAAU,KAAK,SAAS,IAAIA,UAAU,KAAK,QAAQ;QACnE;MACJ,KAAK,SAAS;QACVG,aAAa,GAAGH,UAAU,KAAK,SAAS;QACxC;MACJ,KAAK,MAAM;QACPG,aAAa,GAAGH,UAAU,KAAK,MAAM;QACrC;MACJ;QACIG,aAAa,GAAG,IAAI;IAC5B;IAEA,OAAOT,aAAa,IAAIS,aAAa,IAAIJ,YAAY;EACzD,CAAC,CAAC,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACd,QAAQrC,MAAM;MACV,KAAK,IAAI;QACL,OAAO,CAACqC,CAAC,CAACC,OAAO,IAAI,CAAC,KAAKF,CAAC,CAACE,OAAO,IAAI,CAAC,CAAC;MAC9C,KAAK,MAAM;QACP,OAAO,CAACF,CAAC,CAACV,IAAI,IAAI,EAAE,EAAEa,aAAa,CAACF,CAAC,CAACX,IAAI,IAAI,EAAE,CAAC;MACrD,KAAK,OAAO;QACR,OAAO,CAACW,CAAC,CAACG,YAAY,IAAIH,CAAC,CAACI,KAAK,IAAI,CAAC,KAAKL,CAAC,CAACI,YAAY,IAAIJ,CAAC,CAACK,KAAK,IAAI,CAAC,CAAC;MAC9E,KAAK,OAAO;QACR,OAAO,CAACL,CAAC,CAACnB,KAAK,IAAI,EAAE,EAAEsB,aAAa,CAACF,CAAC,CAACpB,KAAK,IAAI,EAAE,CAAC;MACvD;QACI,OAAO,CAACmB,CAAC,CAACM,IAAI,IAAI,CAAC,KAAKL,CAAC,CAACK,IAAI,IAAI,CAAC,CAAC;IAC5C;EACJ,CAAC,CAAC;;EAEF;EACA,MAAMC,kBAAkB,GAAGzB,aAAa,CAAC0B,GAAG,CAAC/B,IAAI,IAAI;IACjD;IACA,MAAMgC,cAAc,GAAG3B,aAAa,CAACC,MAAM,CAAC2B,CAAC,IAAIA,CAAC,CAAC7B,KAAK,KAAKJ,IAAI,CAACI,KAAK,CAAC;IACxE,MAAM8B,SAAS,GAAGF,cAAc,CAACG,SAAS,CAACF,CAAC,IAAIA,CAAC,CAAC/B,GAAG,KAAKF,IAAI,CAACE,GAAG,IAAI+B,CAAC,CAAChC,MAAM,KAAKD,IAAI,CAACC,MAAM,CAAC,GAAG,CAAC;IACnG,OAAO;MAAE,GAAGD,IAAI;MAAEkC;IAAU,CAAC;EACjC,CAAC,CAAC;;EAEF;EACA,MAAME,iBAAiB,GAAGN,kBAAkB,CAAC/B,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACE,GAAG,KAAK9B,aAAa,IAAI4B,IAAI,CAACC,MAAM,KAAK7B,aAAa,CAAC;;EAEtH;EACA,MAAMiE,mBAAmB,GAAGA,CAAA,KAAM;IAC9B,IAAIzC,OAAO,CAAC0C,OAAO,EAAE;MACjB1C,OAAO,CAAC0C,OAAO,CAACC,cAAc,CAAC;QAC3BC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACF/C,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACAgD,UAAU,CAAC,MAAMhD,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACrD;EACJ,CAAC;;EAED;EACA,MAAMiD,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAItD,QAAQ,KAAK,SAAS,EAAE;MACxB,OAAO,WAAW;IACtB;IAEA,QAAQhB,MAAM;MACV,KAAK,UAAU;QACX,OAAO,qEAAqE;MAChF,KAAK,MAAM;QACP,OAAO,sDAAsD;MACjE,KAAK,YAAY;MACjB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;;EAID;EACA,MAAMuE,UAAU,GAAGzE,KAAK,CAAC0E,MAAM;EAC/B,MAAMC,YAAY,GAAG3E,KAAK,CAACmC,MAAM,CAAC2B,CAAC,IAC/BA,CAAC,CAACb,kBAAkB,KAAK,QAAQ,IACjCa,CAAC,CAACb,kBAAkB,KAAK,SAAS,IAClCa,CAAC,CAACd,4BAA4B,KAAK,SACvC,CAAC,CAAC0B,MAAM;;EAER;EACA,MAAME,QAAQ,GAAG5E,KAAK,CAAC0E,MAAM,GAAG,CAAC,GAAGG,IAAI,CAACC,GAAG,CAAC,GAAG9E,KAAK,CAAC4D,GAAG,CAACE,CAAC,IACvDA,CAAC,CAACN,YAAY,IAAIM,CAAC,CAACR,OAAO,IAAIQ,CAAC,CAACiB,WAAW,IAAI,CACpD,CAAC,CAAC,GAAG,CAAC;;EAEN;EACA,MAAMC,WAAW,GAAGhF,KAAK,CAACmC,MAAM,CAAC2B,CAAC,IAAI,CAACA,CAAC,CAACmB,iBAAiB,IAAI,CAAC,IAAI,CAAC,CAAC,CAACP,MAAM;EAC5E,MAAMQ,SAAS,GAAGlF,KAAK,CAAC0E,MAAM,GAAG,CAAC,GAC9BG,IAAI,CAACM,KAAK,CAACnF,KAAK,CAACoF,MAAM,CAAC,CAACC,GAAG,EAAEvB,CAAC,KAAKuB,GAAG,IAAIvB,CAAC,CAACR,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGtD,KAAK,CAAC0E,MAAM,CAAC,GAAG,CAAC;EAEtF,oBACI5E,OAAA;IAAKO,SAAS,EAAG,aAAYA,SAAU,EAAE;IAAAiF,QAAA,GAEpClF,SAAS,iBACNN,OAAA;MAAKO,SAAS,EAAC,mGAAmG;MAAAiF,QAAA,gBAE9GxF,OAAA;QAAKO,SAAS,EAAC,mFAAmF;QAAAiF,QAAA,gBAC9FxF,OAAA;UAAKO,SAAS,EAAC,qBAAqB;UAAAiF,QAAA,gBAEhCxF,OAAA;YAAKO,SAAS,EAAC,iCAAiC;YAAAiF,QAAA,gBAE5CxF,OAAA;cAAKO,SAAS,EAAC,iBAAiB;cAAAiF,QAAA,gBAC5BxF,OAAA,CAACJ,QAAQ;gBAACW,SAAS,EAAC;cAA0E;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjG5F,OAAA;gBACI6F,EAAE,EAAC,gBAAgB;gBACnBjD,IAAI,EAAC,gBAAgB;gBACrBkD,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,oCAAoC;gBAChDC,KAAK,EAAElF,UAAW;gBAClBmF,QAAQ,EAAGC,CAAC,IAAKnF,aAAa,CAACmF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CI,YAAY,EAAC,KAAK;gBAClB7F,SAAS,EAAC;cAAmK;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChL,CAAC,EACD9E,UAAU,iBACPd,OAAA;gBACIqG,OAAO,EAAEA,CAAA,KAAMtF,aAAa,CAAC,EAAE,CAAE;gBACjCR,SAAS,EAAC,uFAAuF;gBAAAiF,QAAA,EACpG;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGN5F,OAAA;cAAKO,SAAS,EAAC,YAAY;cAAAiF,QAAA,eACvBxF,OAAA;gBACIqG,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAAC,CAACD,eAAe,CAAE;gBACpDf,SAAS,EAAG,gEACRe,eAAe,GACT,kCAAkC,GAClC,6CACT,EAAE;gBAAAkE,QAAA,EACN;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN5F,OAAA;YAAKO,SAAS,EAAC,iCAAiC;YAAAiF,QAAA,gBAE5CxF,OAAA;cAAKO,SAAS,EAAC,UAAU;cAAAiF,QAAA,gBACrBxF,OAAA,CAACH,QAAQ;gBAACU,SAAS,EAAC;cAA0E;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjG5F,OAAA;gBACIgG,KAAK,EAAEhF,UAAW;gBAClBiF,QAAQ,EAAGC,CAAC,IAAKjF,aAAa,CAACiF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;gBAC/CzF,SAAS,EAAC,4JAA4J;gBAAAiF,QAAA,gBAEtKxF,OAAA;kBAAQgG,KAAK,EAAC,KAAK;kBAAAR,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9C5F,OAAA;kBAAQgG,KAAK,EAAC,SAAS;kBAAAR,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjD5F,OAAA;kBAAQgG,KAAK,EAAC,MAAM;kBAAAR,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3C5F,OAAA;kBAAQgG,KAAK,EAAC,SAAS;kBAAAR,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGN5F,OAAA;cACIgG,KAAK,EAAE9E,MAAO;cACd+E,QAAQ,EAAGC,CAAC,IAAK/E,SAAS,CAAC+E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC3CzF,SAAS,EAAC,sJAAsJ;cAAAiF,QAAA,gBAEhKxF,OAAA;gBAAQgG,KAAK,EAAC,MAAM;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C5F,OAAA;gBAAQgG,KAAK,EAAC,IAAI;gBAAAR,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxC5F,OAAA;gBAAQgG,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC/C5F,OAAA;gBAAQgG,KAAK,EAAC,MAAM;gBAAAR,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7C5F,OAAA;gBAAQgG,KAAK,EAAC,OAAO;gBAAAR,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eAGT5F,OAAA;cAAKO,SAAS,EAAC,iCAAiC;cAAAiF,QAAA,gBAC5CxF,OAAA;gBACIqG,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,MAAM,CAAE;gBACnCd,SAAS,EAAG,wEACRa,QAAQ,KAAK,MAAM,GACb,kCAAkC,GAClC,mCACT,EAAE;gBAAAoE,QAAA,EACN;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT5F,OAAA;gBACIqG,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,SAAS,CAAE;gBACtCd,SAAS,EAAG,wEACRa,QAAQ,KAAK,SAAS,GAChB,kCAAkC,GAClC,mCACT,EAAE;gBAAAoE,QAAA,EACN;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN5F,OAAA;UAAKO,SAAS,EAAC,kFAAkF;UAAAiF,QAAA,gBAC7FxF,OAAA;YAAKO,SAAS,EAAC,uBAAuB;YAAAiF,QAAA,gBAClCxF,OAAA;cAAMO,SAAS,EAAC,6BAA6B;cAAAiF,QAAA,EACxC3B,kBAAkB,CAACe;YAAM;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,QAAI,eAAA5F,OAAA;cAAMO,SAAS,EAAC,6BAA6B;cAAAiF,QAAA,EAAEtF,KAAK,CAAC0E;YAAM;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,UAC9E,EAAC9E,UAAU,iBACPd,OAAA;cAAMO,SAAS,EAAC,MAAM;cAAAiF,QAAA,GAAC,WACV,eAAAxF,OAAA;gBAAMO,SAAS,EAAC,2BAA2B;gBAAAiF,QAAA,GAAC,IAAC,EAAC1E,UAAU,EAAC,IAAC;cAAA;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CACT,EACA5E,UAAU,KAAK,KAAK,iBACjBhB,OAAA;cAAMO,SAAS,EAAC,2EAA2E;cAAAiF,QAAA,EACtFxE;YAAU;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CACT,EACAtE,eAAe,IAAIY,gBAAgB,iBAChClC,OAAA;cAAMO,SAAS,EAAC,6EAA6E;cAAAiF,QAAA,GAAC,QACpF,EAACtD,gBAAgB;YAAA;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGN5F,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAiF,QAAA,gBAC7CxF,OAAA;cAAAwF,QAAA,GAAM,oBAAQ,EAACT,IAAI,CAACC,GAAG,CAAC,GAAGnB,kBAAkB,CAACC,GAAG,CAACE,CAAC,IAAIA,CAAC,CAACN,YAAY,IAAIM,CAAC,CAACR,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC8C,cAAc,CAAC,CAAC;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAClH5F,OAAA;cAAAwF,QAAA,GAAM,oBAAQ,EAACT,IAAI,CAACM,KAAK,CAACxB,kBAAkB,CAACyB,MAAM,CAAC,CAACC,GAAG,EAAEvB,CAAC,KAAKuB,GAAG,IAAIvB,CAAC,CAACN,YAAY,IAAIM,CAAC,CAACR,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGK,kBAAkB,CAACe,MAAM,IAAI,CAAC,CAAC,CAAC0B,cAAc,CAAC,CAAC;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACN5F,OAAA;QAAKO,SAAS,EAAC,wCAAwC;QAAAiF,QAAA,gBACnDxF,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAAiF,QAAA,gBACxCxF,OAAA;YAAKO,SAAS,EAAC,6BAA6B;YAAAiF,QAAA,gBACxCxF,OAAA;cAAKO,SAAS,EAAC,yFAAyF;cAAAiF,QAAA,eACpGxF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN5F,OAAA;cAAAwF,QAAA,gBACIxF,OAAA;gBAAIO,SAAS,EAAC,2HAA2H;gBAAAiF,QAAA,EAAC;cAE1I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5F,OAAA;gBAAGO,SAAS,EAAC,mCAAmC;gBAAAiF,QAAA,EAAC;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAELlF,WAAW,iBACRV,OAAA;YAAKO,SAAS,EAAC,oFAAoF;YAAAiF,QAAA,gBAC/FxF,OAAA,CAACL,OAAO;cAACY,SAAS,EAAC;YAAuB;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7C5F,OAAA;cAAMO,SAAS,EAAC,mCAAmC;cAAAiF,QAAA,GAAC,UACxC,EAAC,IAAIe,IAAI,CAAC7F,WAAW,CAAC,CAAC8F,kBAAkB,CAAC,CAAC;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEN5F,OAAA;UAAKO,SAAS,EAAC,6BAA6B;UAAAiF,QAAA,GAEvC5E,mBAAmB,iBAChBZ,OAAA;YACIqG,OAAO,EAAEzF,mBAAoB;YAC7BL,SAAS,EAAG,4HACRI,WAAW,GACL,4CAA4C,GAC5C,6CACT,EAAE;YAAA6E,QAAA,GAEF7E,WAAW,gBAAGX,OAAA,CAACN,aAAa;cAACa,SAAS,EAAC;YAAS;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5F,OAAA,CAACP,YAAY;cAACc,SAAS,EAAC;YAAS;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3F5F,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAAiF,QAAA,EAC7B7E,WAAW,GAAG,MAAM,GAAG;YAAQ;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACX,EAKAzF,aAAa,iBACVH,OAAA;YACIqG,OAAO,EAAEjC,mBAAoB;YAC7B7D,SAAS,EAAC,uKAAuK;YAAAiF,QAAA,gBAEjLxF,OAAA,CAACV,MAAM;cAACiB,SAAS,EAAC;YAAS;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9B5F,OAAA;cAAMO,SAAS,EAAC,kBAAkB;cAAAiF,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN5F,OAAA;QAAKO,SAAS,EAAC,uCAAuC;QAAAiF,QAAA,gBAClDxF,OAAA;UAAKO,SAAS,EAAC,yJAAyJ;UAAAiF,QAAA,gBACpKxF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAiF,QAAA,gBAC7CxF,OAAA;cAAKO,SAAS,EAAC,4BAA4B;cAAAiF,QAAA,eACvCxF,OAAA,CAACT,OAAO;gBAACgB,SAAS,EAAC;cAAoB;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACN5F,OAAA;cAAMO,SAAS,EAAC,qCAAqC;cAAAiF,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACN5F,OAAA;YAAKO,SAAS,EAAC,wCAAwC;YAAAiF,QAAA,EAAEb;UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1E5F,OAAA;YAAKO,SAAS,EAAC,mCAAmC;YAAAiF,QAAA,GAAEN,WAAW,EAAC,SAAO;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAEN5F,OAAA;UAAKO,SAAS,EAAC,6JAA6J;UAAAiF,QAAA,gBACxKxF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAiF,QAAA,gBAC7CxF,OAAA;cAAKO,SAAS,EAAC,+DAA+D;cAAAiF,QAAA,eAC1ExF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN5F,OAAA;cAAMO,SAAS,EAAC,uCAAuC;cAAAiF,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACN5F,OAAA;YAAKO,SAAS,EAAC,0CAA0C;YAAAiF,QAAA,EAAEX;UAAY;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9E5F,OAAA;YAAKO,SAAS,EAAC,qCAAqC;YAAAiF,QAAA,GAC/Cb,UAAU,GAAG,CAAC,GAAGI,IAAI,CAACM,KAAK,CAAER,YAAY,GAAGF,UAAU,GAAI,GAAG,CAAC,GAAG,CAAC,EAAC,WACxE;UAAA;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN5F,OAAA;UAAKO,SAAS,EAAC,4JAA4J;UAAAiF,QAAA,gBACvKxF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAiF,QAAA,gBAC7CxF,OAAA;cAAKO,SAAS,EAAC,6BAA6B;cAAAiF,QAAA,eACxCxF,OAAA,CAACV,MAAM;gBAACiB,SAAS,EAAC;cAAoB;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN5F,OAAA;cAAMO,SAAS,EAAC,sCAAsC;cAAAiF,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eACN5F,OAAA;YAAKO,SAAS,EAAC,yCAAyC;YAAAiF,QAAA,EAAEV,QAAQ,CAACwB,cAAc,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1F5F,OAAA;YAAKO,SAAS,EAAC,oCAAoC;YAAAiF,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAEN5F,OAAA;UAAKO,SAAS,EAAC,2JAA2J;UAAAiF,QAAA,gBACtKxF,OAAA;YAAKO,SAAS,EAAC,kCAAkC;YAAAiF,QAAA,gBAC7CxF,OAAA;cAAKO,SAAS,EAAC,8BAA8B;cAAAiF,QAAA,eACzCxF,OAAA,CAACR,QAAQ;gBAACe,SAAS,EAAC;cAAoB;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN5F,OAAA;cAAMO,SAAS,EAAC,uCAAuC;cAAAiF,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC,eACN5F,OAAA;YAAKO,SAAS,EAAC,0CAA0C;YAAAiF,QAAA,EAAEJ,SAAS,CAACkB,cAAc,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5F5F,OAAA;YAAKO,SAAS,EAAC,4BAA4B;YAAAiF,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,eAGD5F,OAAA;MAAKO,SAAS,EAAG,oBAAmBmE,gBAAgB,CAAC,CAAE,EAAE;MAAAc,QAAA,EACpD3B,kBAAkB,CAACC,GAAG,CAAC,CAAC/B,IAAI,EAAE0E,KAAK,KAAK;QACrC,MAAMC,aAAa,GAAG3E,IAAI,CAACC,MAAM,KAAK7B,aAAa,IAAI4B,IAAI,CAACE,GAAG,KAAK9B,aAAa;QACjF,MAAMyD,IAAI,GAAG7B,IAAI,CAAC6B,IAAI,IAAI6C,KAAK,GAAG,CAAC;;QAEnC;QACA,IAAIrF,QAAQ,KAAK,SAAS,EAAE;UAAA,IAAAuF,IAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA,EAAAC,KAAA;UACxB,oBACIhH,OAAA;YAEIiH,GAAG,EAAEP,aAAa,GAAG/E,OAAO,GAAG,IAAK;YACpCpB,SAAS,EAAG,yEACRmG,aAAa,IAAI9E,YAAY,GACvB,yGAAyG,GACzG8E,aAAa,GACb,iDAAiD,GACjD,gEACT,EAAE;YAAAlB,QAAA,eAEHxF,OAAA;cAAKO,SAAS,EAAC,mCAAmC;cAAAiF,QAAA,gBAC9CxF,OAAA;gBAAKO,SAAS,EAAC,6BAA6B;gBAAAiF,QAAA,gBAExCxF,OAAA;kBAAKO,SAAS,EAAG,2EACbqD,IAAI,IAAI,CAAC,GAAG,2DAA2D,GACvEA,IAAI,IAAI,EAAE,GAAG,uDAAuD,GACpE,2BACH,EAAE;kBAAA4B,QAAA,EACE5B;gBAAI;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eAGN5F,OAAA;kBAAKO,SAAS,EAAC,6BAA6B;kBAAAiF,QAAA,gBACxCxF,OAAA;oBACIkH,GAAG,EAAEnF,IAAI,CAACoF,cAAc,IAAI,qBAAsB;oBAClDC,GAAG,EAAErF,IAAI,CAACa,IAAK;oBACfrC,SAAS,EAAC;kBAA4D;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACF5F,OAAA;oBAAAwF,QAAA,gBACIxF,OAAA;sBAAKO,SAAS,EAAC,qCAAqC;sBAAAiF,QAAA,EAAEzD,IAAI,CAACa;oBAAI;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACtE5F,OAAA;sBAAKO,SAAS,EAAC,uBAAuB;sBAAAiF,QAAA,GAAC,QAAM,EAACzD,IAAI,CAACI,KAAK;oBAAA;sBAAAsD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGN5F,OAAA;gBAAKO,SAAS,EAAC,6BAA6B;gBAAAiF,QAAA,gBACxCxF,OAAA;kBAAKO,SAAS,EAAC,YAAY;kBAAAiF,QAAA,gBACvBxF,OAAA;oBAAKO,SAAS,EAAC,iCAAiC;oBAAAiF,QAAA,EAC3C,CAACzD,IAAI,CAAC2B,YAAY,IAAI3B,IAAI,CAACyB,OAAO,IAAI,CAAC,EAAE8C,cAAc,CAAC;kBAAC;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,eACN5F,OAAA;oBAAKO,SAAS,EAAC,uBAAuB;oBAAAiF,QAAA,EACjCzD,IAAI,CAAC2B,YAAY,GAAG,KAAK,GAAG;kBAAI;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eAGN5F,OAAA;kBAAKO,SAAS,EAAG,8CACb,EAAAoG,IAAA,GAAC5E,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAAwD,IAAA,uBAA7DA,IAAA,CAAgE9D,WAAW,CAAC,CAAC,MAAK,SAAS,IAC3F,EAAA+D,KAAA,GAAC7E,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAAyD,KAAA,uBAA7DA,KAAA,CAAgE/D,WAAW,CAAC,CAAC,MAAK,QAAQ,GACpF,+BAA+B,GAC/B,EAAAgE,KAAA,GAAC9E,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAA0D,KAAA,uBAA7DA,KAAA,CAAgEhE,WAAW,CAAC,CAAC,MAAK,SAAS,GAC3F,yBAAyB,GACzB,2BACT,EAAE;kBAAA2C,QAAA,EACE,EAAAsB,KAAA,GAAC/E,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAA2D,KAAA,uBAA7DA,KAAA,CAAgEjE,WAAW,CAAC,CAAC,MAAK,SAAS,IAC3F,EAAAkE,KAAA,GAAChF,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAA4D,KAAA,uBAA7DA,KAAA,CAAgElE,WAAW,CAAC,CAAC,MAAK,QAAQ,GAAG,IAAI,GACjG,EAAAmE,KAAA,GAACjF,IAAI,CAACmB,4BAA4B,IAAInB,IAAI,CAACoB,kBAAkB,cAAA6D,KAAA,uBAA7DA,KAAA,CAAgEnE,WAAW,CAAC,CAAC,MAAK,SAAS,GAAG,GAAG,GAAG;gBAAI;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC,GA5DD7D,IAAI,CAACC,MAAM,IAAID,IAAI,CAACE,GAAG;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA6D3B,CAAC;QAEd;;QAEA;QACA,oBACI5F,OAAA;UAEIiH,GAAG,EAAEP,aAAa,GAAG/E,OAAO,GAAG,IAAK;UACpCpB,SAAS,EAAG,mDACRmG,aAAa,IAAI9E,YAAY,GACvB,6GAA6G,GAC7G8E,aAAa,GACb,+CAA+C,GAC3C,EACT,EAAE;UAAAlB,QAAA,eAEHxF,OAAA,CAACF,eAAe;YACZiC,IAAI,EAAEA,IAAK;YACX6B,IAAI,EAAEA,IAAK;YACXK,SAAS,EAAElC,IAAI,CAACkC,SAAU;YAC1ByC,aAAa,EAAEA,aAAc;YAC7BtG,MAAM,EAAEA,MAAO;YACfC,IAAI,EAAEA,IAAK;YACXC,SAAS,EAAEA;UAAU;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC,GAlBD7D,IAAI,CAACC,MAAM,IAAID,IAAI,CAACE,GAAG;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBvB,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL/B,kBAAkB,CAACe,MAAM,KAAK,CAAC,iBAC5B5E,OAAA;MAAKO,SAAS,EAAC,+EAA+E;MAAAiF,QAAA,gBAC1FxF,OAAA,CAACT,OAAO;QAACgB,SAAS,EAAC;MAAsC;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5D5F,OAAA;QAAIO,SAAS,EAAC,0CAA0C;QAAAiF,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5E5F,OAAA;QAAGO,SAAS,EAAC,eAAe;QAAAiF,QAAA,EACvBtF,KAAK,CAAC0E,MAAM,KAAK,CAAC,GAAG,4BAA4B,GAAG;MAA+C;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACR,EAGAzF,aAAa,IAAI0D,kBAAkB,CAACe,MAAM,GAAG,EAAE,iBAC5C5E,OAAA;MACIqG,OAAO,EAAEjC,mBAAoB;MAC7B7D,SAAS,EAAC,4LAA4L;MACtM8G,KAAK,EAAC,oBAAoB;MAAA7B,QAAA,eAE1BxF,OAAA,CAACV,MAAM;QAACiB,SAAS,EAAC;MAAS;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CACX;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC/E,EAAA,CArfIZ,eAAe;AAAAqH,EAAA,GAAfrH,eAAe;AAufrB,eAAeA,eAAe;AAAC,IAAAqH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}