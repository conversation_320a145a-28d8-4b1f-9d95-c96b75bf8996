import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';
import '../pages/user/Quiz/responsive.css';

const QuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  // Early return for invalid question
  if (!question || !question.name) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">Question Not Available</h3>
          <p className="text-gray-600">This question could not be loaded. Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  const renderMCQ = () => {
    if (!question || !question.options || Object.keys(question.options).length === 0) {
      return (
        <div className="text-center bg-red-50 rounded-xl p-6 border border-red-200">
          <div className="text-red-500 text-4xl mb-2">⚠️</div>
          <p className="text-red-700">No options available for this question.</p>
        </div>
      );
    }

    const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];

    return (
      <div className="space-y-3">
        {Object.entries(question.options).map(([key, value], index) => {
          const optionKey = String(key).trim();
          const optionValue = String(value || '').trim();
          const label = optionLabels[index] || optionKey;
          const isSelected = currentAnswer === optionKey;

          // Skip empty options
          if (!optionValue) return null;

          return (
            <motion.button
              key={optionKey}
              onClick={() => handleAnswerSelect(optionKey)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 ${
                isSelected
                  ? 'bg-blue-600 text-white border-blue-600 shadow-lg'
                  : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800'
              }`}
            >
              <div className="flex items-center gap-4">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm transition-all ${
                  isSelected
                    ? 'bg-white text-blue-600'
                    : 'bg-blue-100 text-blue-700'
                }`}>
                  {label}
                </div>
                <span className={`flex-1 font-medium ${
                  isSelected ? 'text-white' : 'text-gray-800'
                }`}>
                  {optionValue}
                </span>
                {isSelected && (
                  <TbCheck className="w-6 h-6 text-white" />
                )}
              </div>
            </motion.button>
          );
        })}
      </div>
    );
  };

  const renderFillBlank = () => (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700 mb-2">
        <div className="flex items-center gap-2">
          <span>✏️</span>
          <span>Your Answer:</span>
        </div>
      </label>
      <div className="relative">
        <input
          type="text"
          value={currentAnswer}
          onChange={(e) => handleAnswerSelect(e.target.value)}
          placeholder="Type your answer here..."
          className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all text-lg font-medium bg-white"
        />
        <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
          {currentAnswer ? (
            <TbCheck className="w-6 h-6 text-green-500" />
          ) : (
            <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Progress Bar */}
      <div className="fixed top-0 left-0 right-0 h-1 bg-gray-200 z-50">
        <motion.div 
          className="h-full bg-gradient-to-r from-blue-500 to-indigo-600"
          initial={{ width: 0 }}
          animate={{ width: `${progressPercentage}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>

      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-100 pt-1">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="grid grid-cols-3 items-center">
            {/* Quiz Title */}
            <div>
              <h1 className="text-xl font-bold text-gray-900">{examTitle}</h1>
              <p className="text-sm text-gray-600">Challenge your brain, beat the rest</p>
            </div>

            {/* Centered Timer */}
            <div className="flex justify-center">
              <div className={`flex items-center gap-2 px-4 py-2 rounded-xl font-mono text-lg font-bold transition-all ${
                isTimeWarning 
                  ? 'bg-red-100 text-red-700 border-2 border-red-300 animate-pulse' 
                  : 'bg-blue-100 text-blue-700 border-2 border-blue-300'
              }`}>
                <TbClock className="w-5 h-5" />
                <span>TIME</span>
                <span>{formatTime(timeLeft)}</span>
              </div>
            </div>

            {/* Question Counter */}
            <div className="flex justify-end">
              <div className="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold">
                {questionIndex + 1} of {totalQuestions}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <motion.div 
          key={questionIndex}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-white rounded-2xl shadow-lg p-8 mb-8"
        >
          {/* Question Number Badge */}
          <div className="mb-6">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-semibold">
              <span>Question {questionIndex + 1}</span>
            </div>
          </div>

          {/* Question Text */}
          <div className="text-xl font-semibold text-gray-900 mb-8 leading-relaxed">
            {question.name}
          </div>

          {/* Question Image */}
          {question.image && (
            <div className="mb-8 text-center">
              <div className="inline-block bg-gray-50 rounded-xl p-4 border border-gray-200">
                <img 
                  src={question.image} 
                  alt="Question" 
                  className="max-w-full max-h-96 rounded-lg shadow-md"
                />
              </div>
            </div>
          )}

          {/* Question Content */}
          {question.options ? renderMCQ() : renderFillBlank()}
        </motion.div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={onPrevious}
              disabled={questionIndex === 0}
              className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all ${
                questionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
              }`}
            >
              <TbArrowLeft className="w-5 h-5" />
              Previous
            </button>

            <div className="flex items-center gap-4">
              {!isAnswered && (
                <div className="flex items-center gap-2 text-amber-600 bg-amber-50 px-3 py-2 rounded-lg text-sm">
                  <span>⚠️</span>
                  <span>Select an answer</span>
                </div>
              )}
            </div>

            <button
              onClick={onNext}
              disabled={!isAnswered}
              className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all ${
                !isAnswered
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : questionIndex === totalQuestions - 1
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {questionIndex === totalQuestions - 1 ? (
                <>
                  <TbCheck className="w-5 h-5" />
                  Submit Quiz
                </>
              ) : (
                <>
                  Next
                  <TbArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Bottom padding to prevent content from being hidden behind fixed navigation */}
      <div className="h-24"></div>
    </div>
  );
};

export default QuizRenderer;
