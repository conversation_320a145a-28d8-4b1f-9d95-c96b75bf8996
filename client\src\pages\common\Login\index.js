import { Form, message } from "antd";
import React from "react";
import './index.css';
import Logo from '../../../assets/logo.png';
import { useDispatch } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { loginUser } from "../../../apicalls/users";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";

function Login() {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const onFinish = async (values) => {
    try {
      dispatch(ShowLoading());
      const response = await loginUser(values);
      dispatch(HideLoading());

      console.log('Login response:', response);

      if (response.success) {
        message.success(response.message);
        localStorage.setItem("token", response.data);

        if (response.data?.isAdmin) {
          navigate("/admin/users");
        } else {
          navigate("/user/hub");
        }
      } else {
        message.error(response.message);
      }
    } catch (error) {
      dispatch(HideLoading());
      console.error('Login error:', error);
      message.error("Login failed. Please try again.");
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <img src={Logo} alt="BrainWave Logo" className="login-logo" />
          <h1 className="login-title">Welcome Back</h1>
          <p className="login-subtitle">Sign in to your account to continue learning</p>
        </div>

        <Form layout="vertical" onFinish={onFinish} className="login-form">
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: "Please input your email!" },
              { type: "email", message: "Please enter a valid email!" }
            ]}
          >
            <input
              type="email"
              className="form-input"
              placeholder="Enter your email"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="Password"
            rules={[{ required: true, message: "Please input your password!" }]}
          >
            <input
              type="password"
              className="form-input"
              placeholder="Enter your password"
            />
          </Form.Item>

          <Form.Item>
            <button type="submit" className="login-btn">
              Sign In
            </button>
          </Form.Item>
        </Form>

        <div className="login-footer">
          <p>
            Don't have an account?{' '}
            <Link to="/register" className="login-link">
              Create Account
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

export default Login;
