{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { TbSearch, TbFilter, TbQuestionMark, TbTrophy, TbPlayerPlay, TbBrain, TbTarget, TbCheck, TbX } from 'react-icons/tb';\nimport { getAllExams } from '../../../apicalls/exams';\nimport { getAllReportsByUser } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport './responsive.css';\nimport './style.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Quiz = () => {\n  _s();\n  const [exams, setExams] = useState([]);\n  const [filteredExams, setFilteredExams] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedClass, setSelectedClass] = useState('');\n  const [userResults, setUserResults] = useState({});\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getUserResults = async () => {\n    try {\n      if (!(user !== null && user !== void 0 && user._id)) return;\n      const response = await getAllReportsByUser({\n        userId: user._id\n      });\n      if (response.success) {\n        const resultsMap = {};\n        response.data.forEach(report => {\n          var _report$exam;\n          const examId = (_report$exam = report.exam) === null || _report$exam === void 0 ? void 0 : _report$exam._id;\n          if (!examId) return;\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\n            resultsMap[examId] = {\n              verdict: report.verdict,\n              percentage: report.percentage,\n              correctAnswers: report.correctAnswers,\n              wrongAnswers: report.wrongAnswers,\n              totalQuestions: report.totalQuestions,\n              obtainedMarks: report.obtainedMarks,\n              totalMarks: report.totalMarks,\n              timeTaken: report.timeTaken,\n              completedAt: report.createdAt\n            };\n          }\n        });\n        setUserResults(resultsMap);\n      }\n    } catch (error) {\n      console.error('Error fetching user results:', error);\n    }\n  };\n  useEffect(() => {\n    const getExams = async () => {\n      try {\n        dispatch(ShowLoading());\n        const response = await getAllExams();\n        dispatch(HideLoading());\n        if (response.success) {\n          // Filter exams by user's level\n          const userLevelExams = response.data.filter(exam => {\n            if (!exam.level || !user.level) return false;\n            return exam.level.toLowerCase() === user.level.toLowerCase();\n          });\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n          setExams(sortedExams);\n\n          // Set default class filter to user's class\n          if (user !== null && user !== void 0 && user.class) {\n            setSelectedClass(String(user.class));\n          }\n        } else {\n          message.error(response.message);\n        }\n      } catch (error) {\n        dispatch(HideLoading());\n        message.error(error.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    getExams();\n    getUserResults();\n  }, [dispatch, user === null || user === void 0 ? void 0 : user.level, user === null || user === void 0 ? void 0 : user.class, user === null || user === void 0 ? void 0 : user._id]);\n  useEffect(() => {\n    let filtered = exams;\n    if (searchTerm) {\n      filtered = filtered.filter(exam => {\n        var _exam$name, _exam$subject;\n        return ((_exam$name = exam.name) === null || _exam$name === void 0 ? void 0 : _exam$name.toLowerCase().includes(searchTerm.toLowerCase())) || ((_exam$subject = exam.subject) === null || _exam$subject === void 0 ? void 0 : _exam$subject.toLowerCase().includes(searchTerm.toLowerCase()));\n      });\n    }\n    if (selectedClass) {\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\n    }\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\n    setFilteredExams(filtered);\n  }, [exams, searchTerm, selectedClass]);\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\n  const handleQuizStart = quiz => {\n    navigate(`/quiz/${quiz._id}/play`);\n  };\n\n  // Custom Quiz Card Component\n  const QuizCard = ({\n    quiz,\n    userResult,\n    onStart\n  }) => {\n    var _quiz$questions;\n    const getStatusInfo = () => {\n      if (!userResult) {\n        return {\n          status: 'not-attempted',\n          color: 'bg-blue-100 text-blue-800',\n          icon: TbTarget,\n          text: 'Not Attempted'\n        };\n      }\n      if (userResult.verdict === 'Pass') {\n        return {\n          status: 'passed',\n          color: 'bg-green-100 text-green-800',\n          icon: TbCheck,\n          text: `Passed (${userResult.percentage}%)`\n        };\n      } else {\n        return {\n          status: 'failed',\n          color: 'bg-red-100 text-red-800',\n          icon: TbX,\n          text: `Failed (${userResult.percentage}%)`\n        };\n      }\n    };\n    const statusInfo = getStatusInfo();\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        opacity: 0,\n        y: 20\n      },\n      animate: {\n        opacity: 1,\n        y: 0\n      },\n      whileHover: {\n        y: -8,\n        scale: 1.02\n      },\n      transition: {\n        duration: 0.3\n      },\n      className: \"bg-white rounded-2xl shadow-lg hover:shadow-2xl border border-gray-100 overflow-hidden group\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white relative overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start justify-between mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white bg-opacity-20 rounded-lg p-2\",\n              children: /*#__PURE__*/_jsxDEV(TbBrain, {\n                className: \"w-6 h-6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `px-3 py-1 rounded-full text-xs font-semibold ${statusInfo.color} bg-white`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-1\",\n                children: [/*#__PURE__*/_jsxDEV(StatusIcon, {\n                  className: \"w-3 h-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), statusInfo.text]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold mb-2 line-clamp-2\",\n            children: quiz.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-4 text-sm opacity-90\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-white bg-opacity-20 px-2 py-1 rounded\",\n              children: quiz.subject\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Class \", quiz.class]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-3 gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 rounded-lg p-3 mb-2\",\n              children: /*#__PURE__*/_jsxDEV(TbQuestionMark, {\n                className: \"w-5 h-5 text-blue-600 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-gray-900\",\n              children: ((_quiz$questions = quiz.questions) === null || _quiz$questions === void 0 ? void 0 : _quiz$questions.length) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Questions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 rounded-lg p-3 mb-2\",\n              children: /*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-5 h-5 text-green-600 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-gray-900\",\n              children: quiz.duration || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Minutes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-50 rounded-lg p-3 mb-2\",\n              children: /*#__PURE__*/_jsxDEV(TbTrophy, {\n                className: \"w-5 h-5 text-purple-600 mx-auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-lg font-bold text-gray-900\",\n              children: quiz.totalMarks || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Points\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), userResult && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-sm mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-600\",\n              children: \"Your Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-semibold\",\n              children: [userResult.percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `h-2 rounded-full transition-all duration-500 ${userResult.verdict === 'Pass' ? 'bg-green-500' : 'bg-red-500'}`,\n              style: {\n                width: `${userResult.percentage}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [userResult.correctAnswers, \" correct\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [userResult.wrongAnswers, \" wrong\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => onStart(quiz),\n          className: \"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 group\",\n          children: [/*#__PURE__*/_jsxDEV(TbPlayerPlay, {\n            className: \"w-5 h-5 group-hover:scale-110 transition-transform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), userResult ? 'Retake Quiz' : 'Start Quiz']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Loading quizzes...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-6 shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(TbBrain, {\n            className: \"w-10 h-10 text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-4\",\n          children: \"Challenge Your Brain, Beat the Rest\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-2xl mx-auto mb-6\",\n          children: [\"Test your knowledge with our comprehensive quizzes designed for Class \", (user === null || user === void 0 ? void 0 : user.class) || 'All Classes']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-8 text-sm text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-green-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [filteredExams.length, \" Available Quizzes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-3 h-3 bg-blue-500 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"Level: \", (user === null || user === void 0 ? void 0 : user.level) || 'All Levels']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.1\n        },\n        className: \"max-w-4xl mx-auto mb-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-2xl shadow-lg p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col md:flex-row gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                children: /*#__PURE__*/_jsxDEV(TbSearch, {\n                  className: \"h-5 w-5 text-gray-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search quizzes by name or subject...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                className: \"block w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-64\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                  children: /*#__PURE__*/_jsxDEV(TbFilter, {\n                    className: \"h-5 w-5 text-gray-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: selectedClass,\n                  onChange: e => setSelectedClass(e.target.value),\n                  className: \"block w-full pl-12 pr-8 py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Classes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 21\n                  }, this), availableClasses.map(className => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: className,\n                    children: [\"Class \", className]\n                  }, className, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.2\n        },\n        children: filteredExams.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-16\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n              className: \"w-16 h-16 text-gray-400 mx-auto mb-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900 mb-2\",\n              children: \"No Quizzes Found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: searchTerm || selectedClass ? \"Try adjusting your search or filter criteria.\" : \"No quizzes are available for your level at the moment.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n          children: filteredExams.map((quiz, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(QuizCard, {\n              quiz: quiz,\n              userResult: userResults[quiz._id],\n              onStart: handleQuizStart\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 19\n            }, this)\n          }, quiz._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 260,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n_s(Quiz, \"hbwqS2WFIhfBIzVZ0sPUhh0bcl4=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = Quiz;\nexport default Quiz;\nvar _c;\n$RefreshReg$(_c, \"Quiz\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useDispatch", "useSelector", "motion", "message", "TbSearch", "Tb<PERSON><PERSON>er", "TbQuestionMark", "TbTrophy", "TbPlayerPlay", "TbBrain", "TbTarget", "TbCheck", "TbX", "getAllExams", "getAllReportsByUser", "HideLoading", "ShowLoading", "jsxDEV", "_jsxDEV", "Quiz", "_s", "exams", "setExams", "filteredExams", "setFilteredExams", "searchTerm", "setSearchTerm", "selectedClass", "setSelectedClass", "userResults", "setUserResults", "loading", "setLoading", "navigate", "dispatch", "user", "state", "getUserResults", "_id", "response", "userId", "success", "resultsMap", "data", "for<PERSON>ach", "report", "_report$exam", "examId", "exam", "Date", "createdAt", "verdict", "percentage", "correctAnswers", "wrongAnswers", "totalQuestions", "obtainedMarks", "totalMarks", "timeTaken", "completedAt", "error", "console", "getExams", "userLevelExams", "filter", "level", "toLowerCase", "sortedExams", "sort", "a", "b", "class", "String", "filtered", "_exam$name", "_exam$subject", "name", "includes", "subject", "availableClasses", "Set", "map", "e", "Boolean", "handleQuizStart", "quiz", "QuizCard", "userResult", "onStart", "_quiz$questions", "getStatusInfo", "status", "color", "icon", "text", "statusInfo", "StatusIcon", "div", "initial", "opacity", "y", "animate", "whileHover", "scale", "transition", "duration", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "questions", "length", "TbClock", "style", "width", "onClick", "delay", "type", "placeholder", "value", "onChange", "target", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { motion } from 'framer-motion';\r\nimport { message } from 'antd';\r\nimport {\r\n  Tb<PERSON><PERSON>ch,\r\n  Tb<PERSON><PERSON><PERSON>,\r\n  TbQuestionMark,\r\n  TbTrophy,\r\n  TbPlayerPlay,\r\n  TbBrain,\r\n  TbTarget,\r\n  TbCheck,\r\n  TbX\r\n} from 'react-icons/tb';\r\nimport { getAllExams } from '../../../apicalls/exams';\r\nimport { getAllReportsByUser } from '../../../apicalls/reports';\r\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\r\nimport './responsive.css';\r\nimport './style.css';\r\n\r\nconst Quiz = () => {\r\n  const [exams, setExams] = useState([]);\r\n  const [filteredExams, setFilteredExams] = useState([]);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [selectedClass, setSelectedClass] = useState('');\r\n  const [userResults, setUserResults] = useState({});\r\n  const [loading, setLoading] = useState(true);\r\n  const navigate = useNavigate();\r\n  const dispatch = useDispatch();\r\n  const { user } = useSelector((state) => state.user);\r\n\r\n  const getUserResults = async () => {\r\n    try {\r\n      if (!user?._id) return;\r\n\r\n      const response = await getAllReportsByUser({ userId: user._id });\r\n      if (response.success) {\r\n        const resultsMap = {};\r\n        response.data.forEach(report => {\r\n          const examId = report.exam?._id;\r\n          if (!examId) return;\r\n          if (!resultsMap[examId] || new Date(report.createdAt) > new Date(resultsMap[examId].createdAt)) {\r\n            resultsMap[examId] = {\r\n              verdict: report.verdict,\r\n              percentage: report.percentage,\r\n              correctAnswers: report.correctAnswers,\r\n              wrongAnswers: report.wrongAnswers,\r\n              totalQuestions: report.totalQuestions,\r\n              obtainedMarks: report.obtainedMarks,\r\n              totalMarks: report.totalMarks,\r\n              timeTaken: report.timeTaken,\r\n              completedAt: report.createdAt,\r\n            };\r\n          }\r\n        });\r\n        setUserResults(resultsMap);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching user results:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const getExams = async () => {\r\n      try {\r\n        dispatch(ShowLoading());\r\n        const response = await getAllExams();\r\n        dispatch(HideLoading());\r\n\r\n        if (response.success) {\r\n          // Filter exams by user's level\r\n          const userLevelExams = response.data.filter(exam => {\r\n            if (!exam.level || !user.level) return false;\r\n            return exam.level.toLowerCase() === user.level.toLowerCase();\r\n          });\r\n\r\n          const sortedExams = userLevelExams.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n          setExams(sortedExams);\r\n\r\n          // Set default class filter to user's class\r\n          if (user?.class) {\r\n            setSelectedClass(String(user.class));\r\n          }\r\n        } else {\r\n          message.error(response.message);\r\n        }\r\n      } catch (error) {\r\n        dispatch(HideLoading());\r\n        message.error(error.message);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    getExams();\r\n    getUserResults();\r\n  }, [dispatch, user?.level, user?.class, user?._id]);\r\n\r\n  useEffect(() => {\r\n    let filtered = exams;\r\n    if (searchTerm) {\r\n      filtered = filtered.filter(exam =>\r\n        exam.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n        exam.subject?.toLowerCase().includes(searchTerm.toLowerCase())\r\n      );\r\n    }\r\n    if (selectedClass) {\r\n      filtered = filtered.filter(exam => String(exam.class) === String(selectedClass));\r\n    }\r\n    filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));\r\n    setFilteredExams(filtered);\r\n  }, [exams, searchTerm, selectedClass]);\r\n\r\n  const availableClasses = [...new Set(exams.map(e => e.class).filter(Boolean))].sort();\r\n\r\n  const handleQuizStart = (quiz) => {\r\n    navigate(`/quiz/${quiz._id}/play`);\r\n  };\r\n\r\n  // Custom Quiz Card Component\r\n  const QuizCard = ({ quiz, userResult, onStart }) => {\r\n    const getStatusInfo = () => {\r\n      if (!userResult) {\r\n        return {\r\n          status: 'not-attempted',\r\n          color: 'bg-blue-100 text-blue-800',\r\n          icon: TbTarget,\r\n          text: 'Not Attempted'\r\n        };\r\n      }\r\n\r\n      if (userResult.verdict === 'Pass') {\r\n        return {\r\n          status: 'passed',\r\n          color: 'bg-green-100 text-green-800',\r\n          icon: TbCheck,\r\n          text: `Passed (${userResult.percentage}%)`\r\n        };\r\n      } else {\r\n        return {\r\n          status: 'failed',\r\n          color: 'bg-red-100 text-red-800',\r\n          icon: TbX,\r\n          text: `Failed (${userResult.percentage}%)`\r\n        };\r\n      }\r\n    };\r\n\r\n    const statusInfo = getStatusInfo();\r\n    const StatusIcon = statusInfo.icon;\r\n\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        whileHover={{ y: -8, scale: 1.02 }}\r\n        transition={{ duration: 0.3 }}\r\n        className=\"bg-white rounded-2xl shadow-lg hover:shadow-2xl border border-gray-100 overflow-hidden group\"\r\n      >\r\n        {/* Header with gradient */}\r\n        <div className=\"bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white relative overflow-hidden\">\r\n          <div className=\"absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -mr-16 -mt-16\"></div>\r\n          <div className=\"relative z-10\">\r\n            <div className=\"flex items-start justify-between mb-4\">\r\n              <div className=\"bg-white bg-opacity-20 rounded-lg p-2\">\r\n                <TbBrain className=\"w-6 h-6\" />\r\n              </div>\r\n              <div className={`px-3 py-1 rounded-full text-xs font-semibold ${statusInfo.color} bg-white`}>\r\n                <div className=\"flex items-center gap-1\">\r\n                  <StatusIcon className=\"w-3 h-3\" />\r\n                  {statusInfo.text}\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <h3 className=\"text-xl font-bold mb-2 line-clamp-2\">{quiz.name}</h3>\r\n            <div className=\"flex items-center gap-4 text-sm opacity-90\">\r\n              <span className=\"bg-white bg-opacity-20 px-2 py-1 rounded\">{quiz.subject}</span>\r\n              <span>Class {quiz.class}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"p-6\">\r\n          {/* Quiz Stats */}\r\n          <div className=\"grid grid-cols-3 gap-4 mb-6\">\r\n            <div className=\"text-center\">\r\n              <div className=\"bg-blue-50 rounded-lg p-3 mb-2\">\r\n                <TbQuestionMark className=\"w-5 h-5 text-blue-600 mx-auto\" />\r\n              </div>\r\n              <div className=\"text-lg font-bold text-gray-900\">{quiz.questions?.length || 0}</div>\r\n              <div className=\"text-xs text-gray-500\">Questions</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"bg-green-50 rounded-lg p-3 mb-2\">\r\n                <TbClock className=\"w-5 h-5 text-green-600 mx-auto\" />\r\n              </div>\r\n              <div className=\"text-lg font-bold text-gray-900\">{quiz.duration || 0}</div>\r\n              <div className=\"text-xs text-gray-500\">Minutes</div>\r\n            </div>\r\n            <div className=\"text-center\">\r\n              <div className=\"bg-purple-50 rounded-lg p-3 mb-2\">\r\n                <TbTrophy className=\"w-5 h-5 text-purple-600 mx-auto\" />\r\n              </div>\r\n              <div className=\"text-lg font-bold text-gray-900\">{quiz.totalMarks || 0}</div>\r\n              <div className=\"text-xs text-gray-500\">Points</div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Progress Bar (if attempted) */}\r\n          {userResult && (\r\n            <div className=\"mb-6\">\r\n              <div className=\"flex justify-between text-sm mb-2\">\r\n                <span className=\"text-gray-600\">Your Progress</span>\r\n                <span className=\"font-semibold\">{userResult.percentage}%</span>\r\n              </div>\r\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n                <div\r\n                  className={`h-2 rounded-full transition-all duration-500 ${\r\n                    userResult.verdict === 'Pass' ? 'bg-green-500' : 'bg-red-500'\r\n                  }`}\r\n                  style={{ width: `${userResult.percentage}%` }}\r\n                ></div>\r\n              </div>\r\n              <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\r\n                <span>{userResult.correctAnswers} correct</span>\r\n                <span>{userResult.wrongAnswers} wrong</span>\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Action Button */}\r\n          <button\r\n            onClick={() => onStart(quiz)}\r\n            className=\"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2 group\"\r\n          >\r\n            <TbPlayerPlay className=\"w-5 h-5 group-hover:scale-110 transition-transform\" />\r\n            {userResult ? 'Retake Quiz' : 'Start Quiz'}\r\n          </button>\r\n        </div>\r\n      </motion.div>\r\n    );\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\r\n          <p className=\"text-gray-600\">Loading quizzes...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\r\n      <div className=\"container mx-auto px-4 py-8\">\r\n        {/* Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          className=\"text-center mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full mb-6 shadow-lg\">\r\n            <TbBrain className=\"w-10 h-10 text-white\" />\r\n          </div>\r\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4\">\r\n            Challenge Your Brain, Beat the Rest\r\n          </h1>\r\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto mb-6\">\r\n            Test your knowledge with our comprehensive quizzes designed for Class {user?.class || 'All Classes'}\r\n          </p>\r\n          <div className=\"flex items-center justify-center gap-8 text-sm text-gray-500\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\r\n              <span>{filteredExams.length} Available Quizzes</span>\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\r\n              <span>Level: {user?.level || 'All Levels'}</span>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Search and Filter */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.1 }}\r\n          className=\"max-w-4xl mx-auto mb-12\"\r\n        >\r\n          <div className=\"bg-white rounded-2xl shadow-lg p-6\">\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                  <TbSearch className=\"h-5 w-5 text-gray-400\" />\r\n                </div>\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Search quizzes by name or subject...\"\r\n                  value={searchTerm}\r\n                  onChange={(e) => setSearchTerm(e.target.value)}\r\n                  className=\"block w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all\"\r\n                />\r\n              </div>\r\n              <div className=\"md:w-64\">\r\n                <div className=\"relative\">\r\n                  <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\r\n                    <TbFilter className=\"h-5 w-5 text-gray-400\" />\r\n                  </div>\r\n                  <select\r\n                    value={selectedClass}\r\n                    onChange={(e) => setSelectedClass(e.target.value)}\r\n                    className=\"block w-full pl-12 pr-8 py-3 border border-gray-300 rounded-xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all appearance-none\"\r\n                  >\r\n                    <option value=\"\">All Classes</option>\r\n                    {availableClasses.map((className) => (\r\n                      <option key={className} value={className}>Class {className}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Quiz Grid */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ delay: 0.2 }}\r\n        >\r\n          {filteredExams.length === 0 ? (\r\n            <div className=\"text-center py-16\">\r\n              <div className=\"bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto\">\r\n                <TbTarget className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No Quizzes Found</h3>\r\n                <p className=\"text-gray-600\">\r\n                  {searchTerm || selectedClass\r\n                    ? \"Try adjusting your search or filter criteria.\"\r\n                    : \"No quizzes are available for your level at the moment.\"\r\n                  }\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\r\n              {filteredExams.map((quiz, index) => (\r\n                <motion.div\r\n                  key={quiz._id}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  animate={{ opacity: 1, y: 0 }}\r\n                  transition={{ delay: index * 0.1 }}\r\n                >\r\n                  <QuizCard\r\n                    quiz={quiz}\r\n                    userResult={userResults[quiz._id]}\r\n                    onStart={handleQuizStart}\r\n                  />\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Quiz;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EACRC,cAAc,EACdC,QAAQ,EACRC,YAAY,EACZC,OAAO,EACPC,QAAQ,EACRC,OAAO,EACPC,GAAG,QACE,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAO,kBAAkB;AACzB,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMoC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAMmC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEmC;EAAK,CAAC,GAAGlC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,IAAI,EAACF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,GAAG,GAAE;MAEhB,MAAMC,QAAQ,GAAG,MAAMzB,mBAAmB,CAAC;QAAE0B,MAAM,EAAEL,IAAI,CAACG;MAAI,CAAC,CAAC;MAChE,IAAIC,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMC,UAAU,GAAG,CAAC,CAAC;QACrBH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAACC,MAAM,IAAI;UAAA,IAAAC,YAAA;UAC9B,MAAMC,MAAM,IAAAD,YAAA,GAAGD,MAAM,CAACG,IAAI,cAAAF,YAAA,uBAAXA,YAAA,CAAaR,GAAG;UAC/B,IAAI,CAACS,MAAM,EAAE;UACb,IAAI,CAACL,UAAU,CAACK,MAAM,CAAC,IAAI,IAAIE,IAAI,CAACJ,MAAM,CAACK,SAAS,CAAC,GAAG,IAAID,IAAI,CAACP,UAAU,CAACK,MAAM,CAAC,CAACG,SAAS,CAAC,EAAE;YAC9FR,UAAU,CAACK,MAAM,CAAC,GAAG;cACnBI,OAAO,EAAEN,MAAM,CAACM,OAAO;cACvBC,UAAU,EAAEP,MAAM,CAACO,UAAU;cAC7BC,cAAc,EAAER,MAAM,CAACQ,cAAc;cACrCC,YAAY,EAAET,MAAM,CAACS,YAAY;cACjCC,cAAc,EAAEV,MAAM,CAACU,cAAc;cACrCC,aAAa,EAAEX,MAAM,CAACW,aAAa;cACnCC,UAAU,EAAEZ,MAAM,CAACY,UAAU;cAC7BC,SAAS,EAAEb,MAAM,CAACa,SAAS;cAC3BC,WAAW,EAAEd,MAAM,CAACK;YACtB,CAAC;UACH;QACF,CAAC,CAAC;QACFpB,cAAc,CAACY,UAAU,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED9D,SAAS,CAAC,MAAM;IACd,MAAMgE,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI;QACF5B,QAAQ,CAAClB,WAAW,CAAC,CAAC,CAAC;QACvB,MAAMuB,QAAQ,GAAG,MAAM1B,WAAW,CAAC,CAAC;QACpCqB,QAAQ,CAACnB,WAAW,CAAC,CAAC,CAAC;QAEvB,IAAIwB,QAAQ,CAACE,OAAO,EAAE;UACpB;UACA,MAAMsB,cAAc,GAAGxB,QAAQ,CAACI,IAAI,CAACqB,MAAM,CAAChB,IAAI,IAAI;YAClD,IAAI,CAACA,IAAI,CAACiB,KAAK,IAAI,CAAC9B,IAAI,CAAC8B,KAAK,EAAE,OAAO,KAAK;YAC5C,OAAOjB,IAAI,CAACiB,KAAK,CAACC,WAAW,CAAC,CAAC,KAAK/B,IAAI,CAAC8B,KAAK,CAACC,WAAW,CAAC,CAAC;UAC9D,CAAC,CAAC;UAEF,MAAMC,WAAW,GAAGJ,cAAc,CAACK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrB,IAAI,CAACqB,CAAC,CAACpB,SAAS,CAAC,GAAG,IAAID,IAAI,CAACoB,CAAC,CAACnB,SAAS,CAAC,CAAC;UAChG5B,QAAQ,CAAC6C,WAAW,CAAC;;UAErB;UACA,IAAIhC,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEoC,KAAK,EAAE;YACf3C,gBAAgB,CAAC4C,MAAM,CAACrC,IAAI,CAACoC,KAAK,CAAC,CAAC;UACtC;QACF,CAAC,MAAM;UACLpE,OAAO,CAACyD,KAAK,CAACrB,QAAQ,CAACpC,OAAO,CAAC;QACjC;MACF,CAAC,CAAC,OAAOyD,KAAK,EAAE;QACd1B,QAAQ,CAACnB,WAAW,CAAC,CAAC,CAAC;QACvBZ,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;MAC9B,CAAC,SAAS;QACR6B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED8B,QAAQ,CAAC,CAAC;IACVzB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACH,QAAQ,EAAEC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK,EAAE9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,KAAK,EAAEpC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,GAAG,CAAC,CAAC;EAEnDxC,SAAS,CAAC,MAAM;IACd,IAAI2E,QAAQ,GAAGpD,KAAK;IACpB,IAAII,UAAU,EAAE;MACdgD,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAAChB,IAAI;QAAA,IAAA0B,UAAA,EAAAC,aAAA;QAAA,OAC7B,EAAAD,UAAA,GAAA1B,IAAI,CAAC4B,IAAI,cAAAF,UAAA,uBAATA,UAAA,CAAWR,WAAW,CAAC,CAAC,CAACW,QAAQ,CAACpD,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAC,OAAAS,aAAA,GAC3D3B,IAAI,CAAC8B,OAAO,cAAAH,aAAA,uBAAZA,aAAA,CAAcT,WAAW,CAAC,CAAC,CAACW,QAAQ,CAACpD,UAAU,CAACyC,WAAW,CAAC,CAAC,CAAC;MAAA,CAChE,CAAC;IACH;IACA,IAAIvC,aAAa,EAAE;MACjB8C,QAAQ,GAAGA,QAAQ,CAACT,MAAM,CAAChB,IAAI,IAAIwB,MAAM,CAACxB,IAAI,CAACuB,KAAK,CAAC,KAAKC,MAAM,CAAC7C,aAAa,CAAC,CAAC;IAClF;IACA8C,QAAQ,CAACL,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIrB,IAAI,CAACqB,CAAC,CAACpB,SAAS,CAAC,GAAG,IAAID,IAAI,CAACoB,CAAC,CAACnB,SAAS,CAAC,CAAC;IACtE1B,gBAAgB,CAACiD,QAAQ,CAAC;EAC5B,CAAC,EAAE,CAACpD,KAAK,EAAEI,UAAU,EAAEE,aAAa,CAAC,CAAC;EAEtC,MAAMoD,gBAAgB,GAAG,CAAC,GAAG,IAAIC,GAAG,CAAC3D,KAAK,CAAC4D,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACX,KAAK,CAAC,CAACP,MAAM,CAACmB,OAAO,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,CAAC;EAErF,MAAMgB,eAAe,GAAIC,IAAI,IAAK;IAChCpD,QAAQ,CAAE,SAAQoD,IAAI,CAAC/C,GAAI,OAAM,CAAC;EACpC,CAAC;;EAED;EACA,MAAMgD,QAAQ,GAAGA,CAAC;IAAED,IAAI;IAAEE,UAAU;IAAEC;EAAQ,CAAC,KAAK;IAAA,IAAAC,eAAA;IAClD,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAI,CAACH,UAAU,EAAE;QACf,OAAO;UACLI,MAAM,EAAE,eAAe;UACvBC,KAAK,EAAE,2BAA2B;UAClCC,IAAI,EAAEnF,QAAQ;UACdoF,IAAI,EAAE;QACR,CAAC;MACH;MAEA,IAAIP,UAAU,CAACpC,OAAO,KAAK,MAAM,EAAE;QACjC,OAAO;UACLwC,MAAM,EAAE,QAAQ;UAChBC,KAAK,EAAE,6BAA6B;UACpCC,IAAI,EAAElF,OAAO;UACbmF,IAAI,EAAG,WAAUP,UAAU,CAACnC,UAAW;QACzC,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLuC,MAAM,EAAE,QAAQ;UAChBC,KAAK,EAAE,yBAAyB;UAChCC,IAAI,EAAEjF,GAAG;UACTkF,IAAI,EAAG,WAAUP,UAAU,CAACnC,UAAW;QACzC,CAAC;MACH;IACF,CAAC;IAED,MAAM2C,UAAU,GAAGL,aAAa,CAAC,CAAC;IAClC,MAAMM,UAAU,GAAGD,UAAU,CAACF,IAAI;IAElC,oBACE3E,OAAA,CAAChB,MAAM,CAAC+F,GAAG;MACTC,OAAO,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAG,CAAE;MAC/BC,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEF,CAAC,EAAE,CAAC,CAAC;QAAEG,KAAK,EAAE;MAAK,CAAE;MACnCC,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BC,SAAS,EAAC,8FAA8F;MAAAC,QAAA,gBAGxGzF,OAAA;QAAKwF,SAAS,EAAC,sFAAsF;QAAAC,QAAA,gBACnGzF,OAAA;UAAKwF,SAAS,EAAC;QAAiF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvG7F,OAAA;UAAKwF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BzF,OAAA;YAAKwF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,gBACpDzF,OAAA;cAAKwF,SAAS,EAAC,uCAAuC;cAAAC,QAAA,eACpDzF,OAAA,CAACT,OAAO;gBAACiG,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAG,gDAA+CX,UAAU,CAACH,KAAM,WAAW;cAAAe,QAAA,eAC1FzF,OAAA;gBAAKwF,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtCzF,OAAA,CAAC8E,UAAU;kBAACU,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACjChB,UAAU,CAACD,IAAI;cAAA;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN7F,OAAA;YAAIwF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,EAAEtB,IAAI,CAACT;UAAI;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpE7F,OAAA;YAAKwF,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDzF,OAAA;cAAMwF,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAEtB,IAAI,CAACP;YAAO;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChF7F,OAAA;cAAAyF,QAAA,GAAM,QAAM,EAACtB,IAAI,CAACd,KAAK;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7F,OAAA;QAAKwF,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAElBzF,OAAA;UAAKwF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CzF,OAAA;YAAKwF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BzF,OAAA;cAAKwF,SAAS,EAAC,gCAAgC;cAAAC,QAAA,eAC7CzF,OAAA,CAACZ,cAAc;gBAACoG,SAAS,EAAC;cAA+B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAE,EAAAlB,eAAA,GAAAJ,IAAI,CAAC2B,SAAS,cAAAvB,eAAA,uBAAdA,eAAA,CAAgBwB,MAAM,KAAI;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpF7F,OAAA;cAAKwF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACN7F,OAAA;YAAKwF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BzF,OAAA;cAAKwF,SAAS,EAAC,iCAAiC;cAAAC,QAAA,eAC9CzF,OAAA,CAACgG,OAAO;gBAACR,SAAS,EAAC;cAAgC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAEtB,IAAI,CAACoB,QAAQ,IAAI;YAAC;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3E7F,OAAA;cAAKwF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACN7F,OAAA;YAAKwF,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BzF,OAAA;cAAKwF,SAAS,EAAC,kCAAkC;cAAAC,QAAA,eAC/CzF,OAAA,CAACX,QAAQ;gBAACmG,SAAS,EAAC;cAAiC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAEtB,IAAI,CAAC5B,UAAU,IAAI;YAAC;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7E7F,OAAA;cAAKwF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLxB,UAAU,iBACTrE,OAAA;UAAKwF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzF,OAAA;YAAKwF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzF,OAAA;cAAMwF,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpD7F,OAAA;cAAMwF,SAAS,EAAC,eAAe;cAAAC,QAAA,GAAEpB,UAAU,CAACnC,UAAU,EAAC,GAAC;YAAA;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACN7F,OAAA;YAAKwF,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDzF,OAAA;cACEwF,SAAS,EAAG,gDACVnB,UAAU,CAACpC,OAAO,KAAK,MAAM,GAAG,cAAc,GAAG,YAClD,EAAE;cACHgE,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAE7B,UAAU,CAACnC,UAAW;cAAG;YAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN7F,OAAA;YAAKwF,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DzF,OAAA;cAAAyF,QAAA,GAAOpB,UAAU,CAAClC,cAAc,EAAC,UAAQ;YAAA;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChD7F,OAAA;cAAAyF,QAAA,GAAOpB,UAAU,CAACjC,YAAY,EAAC,QAAM;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD7F,OAAA;UACEmG,OAAO,EAAEA,CAAA,KAAM7B,OAAO,CAACH,IAAI,CAAE;UAC7BqB,SAAS,EAAC,8OAA8O;UAAAC,QAAA,gBAExPzF,OAAA,CAACV,YAAY;YAACkG,SAAS,EAAC;UAAoD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAC9ExB,UAAU,GAAG,aAAa,GAAG,YAAY;QAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEjB,CAAC;EAED,IAAIhF,OAAO,EAAE;IACX,oBACEb,OAAA;MAAKwF,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGzF,OAAA;QAAKwF,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzF,OAAA;UAAKwF,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG7F,OAAA;UAAGwF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7F,OAAA;IAAKwF,SAAS,EAAC,2DAA2D;IAAAC,QAAA,eACxEzF,OAAA;MAAKwF,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAE1CzF,OAAA,CAAChB,MAAM,CAAC+F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BM,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BzF,OAAA;UAAKwF,SAAS,EAAC,6HAA6H;UAAAC,QAAA,eAC1IzF,OAAA,CAACT,OAAO;YAACiG,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACN7F,OAAA;UAAIwF,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL7F,OAAA;UAAGwF,SAAS,EAAC,8CAA8C;UAAAC,QAAA,GAAC,wEACY,EAAC,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoC,KAAK,KAAI,aAAa;QAAA;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClG,CAAC,eACJ7F,OAAA;UAAKwF,SAAS,EAAC,8DAA8D;UAAAC,QAAA,gBAC3EzF,OAAA;YAAKwF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCzF,OAAA;cAAKwF,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzD7F,OAAA;cAAAyF,QAAA,GAAOpF,aAAa,CAAC0F,MAAM,EAAC,oBAAkB;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACN7F,OAAA;YAAKwF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCzF,OAAA;cAAKwF,SAAS,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxD7F,OAAA;cAAAyF,QAAA,GAAM,SAAO,EAAC,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK,KAAI,YAAY;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb7F,OAAA,CAAChB,MAAM,CAAC+F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BI,UAAU,EAAE;UAAEc,KAAK,EAAE;QAAI,CAAE;QAC3BZ,SAAS,EAAC,yBAAyB;QAAAC,QAAA,eAEnCzF,OAAA;UAAKwF,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDzF,OAAA;YAAKwF,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CzF,OAAA;cAAKwF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BzF,OAAA;gBAAKwF,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,eACnFzF,OAAA,CAACd,QAAQ;kBAACsG,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACN7F,OAAA;gBACEqG,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,sCAAsC;gBAClDC,KAAK,EAAEhG,UAAW;gBAClBiG,QAAQ,EAAGxC,CAAC,IAAKxD,aAAa,CAACwD,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;gBAC/Cf,SAAS,EAAC;cAAmL;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9L,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAC,SAAS;cAAAC,QAAA,eACtBzF,OAAA;gBAAKwF,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBzF,OAAA;kBAAKwF,SAAS,EAAC,sEAAsE;kBAAAC,QAAA,eACnFzF,OAAA,CAACb,QAAQ;oBAACqG,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACN7F,OAAA;kBACEuG,KAAK,EAAE9F,aAAc;kBACrB+F,QAAQ,EAAGxC,CAAC,IAAKtD,gBAAgB,CAACsD,CAAC,CAACyC,MAAM,CAACF,KAAK,CAAE;kBAClDf,SAAS,EAAC,mMAAmM;kBAAAC,QAAA,gBAE7MzF,OAAA;oBAAQuG,KAAK,EAAC,EAAE;oBAAAd,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACpChC,gBAAgB,CAACE,GAAG,CAAEyB,SAAS,iBAC9BxF,OAAA;oBAAwBuG,KAAK,EAAEf,SAAU;oBAAAC,QAAA,GAAC,QAAM,EAACD,SAAS;kBAAA,GAA7CA,SAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAA6C,CACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb7F,OAAA,CAAChB,MAAM,CAAC+F,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BI,UAAU,EAAE;UAAEc,KAAK,EAAE;QAAI,CAAE;QAAAX,QAAA,EAE1BpF,aAAa,CAAC0F,MAAM,KAAK,CAAC,gBACzB/F,OAAA;UAAKwF,SAAS,EAAC,mBAAmB;UAAAC,QAAA,eAChCzF,OAAA;YAAKwF,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEzF,OAAA,CAACR,QAAQ;cAACgG,SAAS,EAAC;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7D7F,OAAA;cAAIwF,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9E7F,OAAA;cAAGwF,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzBlF,UAAU,IAAIE,aAAa,GACxB,+CAA+C,GAC/C;YAAwD;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN7F,OAAA;UAAKwF,SAAS,EAAC,qEAAqE;UAAAC,QAAA,EACjFpF,aAAa,CAAC0D,GAAG,CAAC,CAACI,IAAI,EAAEuC,KAAK,kBAC7B1G,OAAA,CAAChB,MAAM,CAAC+F,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BI,UAAU,EAAE;cAAEc,KAAK,EAAEM,KAAK,GAAG;YAAI,CAAE;YAAAjB,QAAA,eAEnCzF,OAAA,CAACoE,QAAQ;cACPD,IAAI,EAAEA,IAAK;cACXE,UAAU,EAAE1D,WAAW,CAACwD,IAAI,CAAC/C,GAAG,CAAE;cAClCkD,OAAO,EAAEJ;YAAgB;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC,GATG1B,IAAI,CAAC/C,GAAG;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUH,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3F,EAAA,CA5VID,IAAI;EAAA,QAOSpB,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAA4H,EAAA,GATxB1G,IAAI;AA8VV,eAAeA,IAAI;AAAC,IAAA0G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}