{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef, useCallback } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { TbTrophy, TbMedal, TbRefresh, TbAlertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from \"react-icons/tb\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\nimport { message } from \"antd\";\nimport axiosInstance from \"../../../apicalls\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const {\n    user\n  } = userState;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [refreshing, setRefreshing] = useState(false);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [autoRefresh, setAutoRefresh] = useState(false);\n  const currentUserRef = useRef(null);\n  const refreshIntervalRef = useRef(null);\n  const fetchRankingData = useCallback(async (showRefreshMessage = false) => {\n    try {\n      // Check if user is available\n      if (!user || !user._id) {\n        console.log('🚫 User not available, skipping ranking fetch');\n        return;\n      }\n      console.log('🔄 Starting ranking data fetch for user:', user._id);\n      setLoading(true);\n      if (showRefreshMessage) {\n        setRefreshing(true);\n        message.loading(\"Refreshing rankings...\", 1);\n      }\n\n      // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking\n      let response;\n\n      // Add cache-busting timestamp to ensure fresh data\n      const timestamp = new Date().getTime();\n      try {\n        // Try new XP-based leaderboard first\n        console.log('🎯 Attempting XP leaderboard API call...');\n        const xpResponse = await axiosInstance.get(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {\n          headers: {\n            'Cache-Control': 'no-cache'\n          }\n        });\n        console.log('📊 XP leaderboard response:', xpResponse.data);\n        if (xpResponse.data.success) {\n          var _response$data;\n          response = xpResponse.data;\n          console.log('✅ Using XP-based leaderboard with', ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.length) || 0, 'users');\n        } else {\n          throw new Error('XP leaderboard failed, trying enhanced leaderboard');\n        }\n      } catch (xpError) {\n        console.log('❌ XP leaderboard failed, trying enhanced leaderboard:', xpError);\n        try {\n          const enhancedResponse = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {\n            headers: {\n              'Cache-Control': 'no-cache'\n            }\n          });\n          console.log('📊 Enhanced leaderboard response:', enhancedResponse.data);\n          if (enhancedResponse.data.success) {\n            var _response$data2;\n            response = enhancedResponse.data;\n            console.log('✅ Using enhanced leaderboard with', ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.length) || 0, 'users');\n          } else {\n            throw new Error('Enhanced leaderboard failed');\n          }\n        } catch (enhancedError) {\n          console.log('❌ Falling back to regular ranking:', enhancedError);\n          response = await getAllReportsForRanking();\n          console.log('📊 Regular ranking response:', response);\n        }\n      }\n      if (response.success) {\n        var _response$data3;\n        console.log('🔄 Transforming ranking data...', ((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.length) || 0, 'users');\n\n        // Transform data to match UserRankingCard expectations with enhanced XP system support\n        const transformedData = response.data.map((userData, index) => ({\n          // User identification - handle both old and new API formats\n          userId: userData.userId || userData._id,\n          _id: userData.userId || userData._id,\n          name: userData.userName || userData.name || 'Unknown User',\n          profilePicture: userData.userPhoto || userData.profileImage,\n          profileImage: userData.profileImage || userData.userPhoto,\n          school: userData.userSchool || userData.school || 'Unknown School',\n          class: userData.userClass || userData.class || 'Unknown',\n          level: userData.userLevel || userData.level || 'Primary',\n          email: userData.email,\n          // Legacy points system\n          totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,\n          totalPointsEarned: userData.totalPointsEarned || userData.totalPoints || 0,\n          quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\n          totalQuizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\n          passedExamsCount: userData.passedExamsCount || 0,\n          retryCount: userData.retryCount || 0,\n          scoreRatio: userData.scoreRatio || 0,\n          // XP System data (new)\n          totalXP: userData.totalXP || 0,\n          currentLevel: userData.currentLevel || 1,\n          xpToNextLevel: userData.xpToNextLevel || 0,\n          seasonXP: userData.seasonXP || 0,\n          lifetimeXP: userData.lifetimeXP || 0,\n          // Statistics\n          averageScore: userData.averageScore || 0,\n          bestStreak: userData.bestStreak || 0,\n          currentStreak: userData.currentStreak || 0,\n          achievements: userData.achievements || [],\n          achievementCount: userData.achievementCount || (userData.achievements ? userData.achievements.length : 0),\n          // Ranking data (prioritize ranking score from enhanced system)\n          rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,\n          rank: userData.rank || index + 1,\n          score: userData.rankingScore || userData.score || userData.totalXP || userData.totalPoints || 0,\n          // Subscription status (handle normalized status)\n          subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\n          normalizedSubscriptionStatus: userData.normalizedSubscriptionStatus || userData.subscriptionStatus || 'free',\n          subscriptionPlan: userData.subscriptionPlan,\n          subscriptionEndDate: userData.subscriptionEndDate,\n          // XP breakdown (if available from new system)\n          breakdown: userData.breakdown || null,\n          // Additional metadata\n          createdAt: userData.createdAt,\n          updatedAt: userData.updatedAt\n        }));\n        setRankingData(transformedData);\n        setError(null);\n        setLastUpdated(new Date());\n\n        // Find current user's rank\n        const userRank = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id) || item.userId === (user === null || user === void 0 ? void 0 : user._id));\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n        if (showRefreshMessage) {\n          message.success(\"Rankings updated successfully!\");\n        }\n      } else {\n        setError(response.message || \"Failed to fetch ranking data\");\n        message.error(\"Failed to load rankings\");\n      }\n    } catch (err) {\n      console.error('Ranking fetch error:', err);\n\n      // Handle different types of errors\n      let errorMessage = \"An error occurred while fetching rankings\";\n      if (err.response) {\n        var _err$response$data;\n        // Server responded with error status\n        errorMessage = ((_err$response$data = err.response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.message) || `Server error: ${err.response.status}`;\n      } else if (err.request) {\n        // Request was made but no response received\n        errorMessage = \"Network error: Unable to connect to server\";\n      } else if (err.message) {\n        // Something else happened\n        errorMessage = err.message;\n      }\n      setError(errorMessage);\n      message.error(errorMessage);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [user]); // Add user as dependency\n\n  useEffect(() => {\n    fetchRankingData();\n\n    // Cleanup function to reset states when component unmounts\n    return () => {\n      setLoading(false);\n      setRefreshing(false);\n      setError(null);\n    };\n  }, [fetchRankingData]);\n\n  // Auto-refresh functionality\n  useEffect(() => {\n    if (autoRefresh) {\n      refreshIntervalRef.current = setInterval(() => {\n        fetchRankingData(false); // Silent refresh\n      }, 30000); // Refresh every 30 seconds\n    } else {\n      if (refreshIntervalRef.current) {\n        clearInterval(refreshIntervalRef.current);\n        refreshIntervalRef.current = null;\n      }\n    }\n\n    // Cleanup on unmount\n    return () => {\n      if (refreshIntervalRef.current) {\n        clearInterval(refreshIntervalRef.current);\n      }\n    };\n  }, [autoRefresh, fetchRankingData]);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRef.current) {\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setShowFindMe(true);\n      setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds\n    }\n  };\n\n  const handleRefresh = () => {\n    fetchRankingData(true);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-xl p-8 shadow-lg animate-fadeInUp\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4 animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading rankings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full animate-fadeInUp\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-2\",\n          children: \"Error Loading Rankings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRefresh,\n          className: \"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 mx-auto hover:scale-105 active:scale-95\",\n          children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-200 to-pink-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-0 left-1/2 w-96 h-96 bg-gradient-to-br from-yellow-200 to-orange-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-4000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-grid-pattern opacity-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 border-b border-white/20 animate-slideInLeft\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.history.back(),\n              className: \"bg-white/80 backdrop-blur-lg hover:bg-white/90 text-gray-700 px-4 py-2 rounded-xl font-semibold transition-all duration-200 flex items-center space-x-2 border border-gray-200 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95\",\n              children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl font-black text-gray-900 gradient-text-blue\",\n                children: \"\\uD83C\\uDFC6 Student Rankings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: \"Compete with students across all levels\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [currentUserRank && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white/80 backdrop-blur-lg rounded-xl px-4 py-2 border border-gray-200 shadow-lg\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500 font-medium\",\n                  children: \"Your Rank\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-lg font-black text-blue-600\",\n                  children: [\"#\", currentUserRank.rank]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 33\n            }, this), currentUserRank && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleFindMe,\n              className: \"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-3 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg hover:scale-105 active:scale-95\",\n              children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Find Me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8 px-4 animate-fadeInUp\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative animate-wiggle\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"text-6xl text-yellow-400 drop-shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2\",\n              children: \"\\uD83C\\uDFC6 LEADERBOARD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2 text-white/80\",\n              children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-medium\",\n                children: \"Battle for Glory\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto animate-scaleIn\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                className: \"text-orange-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 350,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbAward, {\n                className: \"text-purple-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: [rankingData.length, \" Competitors\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbMedal, {\n                className: \"text-yellow-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: \"Live Rankings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\",\n      children: rankingData.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-16 bg-white/80 backdrop-blur-lg rounded-2xl border border-white/50 shadow-2xl animate-fadeInUp\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-wiggle\",\n          children: /*#__PURE__*/_jsxDEV(TbMedal, {\n            className: \"w-16 h-16 text-yellow-500 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-900 mb-2\",\n          children: \"No Rankings Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6 text-lg\",\n          children: \"Complete some quizzes to join the leaderboard!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleRefresh,\n          disabled: refreshing,\n          className: `bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg hover:scale-105 active:scale-95 ${refreshing ? 'opacity-50 cursor-not-allowed' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n            className: `w-5 h-5 inline mr-2 ${refreshing ? 'animate-spin' : ''}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 29\n          }, this), refreshing ? 'Refreshing...' : 'Refresh Rankings']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white/80 backdrop-blur-xl rounded-2xl border border-white/50 p-8 shadow-2xl animate-fadeInUp\",\n        children: [currentUserRank && currentUserRank.rank <= 10 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-xl p-4 text-white animate-bounce\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-bold text-lg\",\n              children: \"\\uD83C\\uDF89 Congratulations! You're in the Top 10! \\uD83C\\uDF89\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"w-6 h-6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(UserRankingList, {\n          users: rankingData,\n          currentUserId: (user === null || user === void 0 ? void 0 : user._id) || null,\n          layout: \"horizontal\",\n          size: \"medium\",\n          showStats: true,\n          className: \"space-y-4\",\n          currentUserRef: currentUserRef,\n          showFindMe: showFindMe,\n          lastUpdated: lastUpdated,\n          autoRefresh: autoRefresh,\n          onAutoRefreshToggle: () => setAutoRefresh(!autoRefresh)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"SOdk1vKQ5vCf/hwqWlvLcfmfl24=\", false, function () {\n  return [useSelector];\n});\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useCallback", "useSelector", "TbTrophy", "TbMedal", "TbRefresh", "TbAlertCircle", "TbArrowLeft", "TbTarget", "TbStar", "TbFlame", "TbAward", "getAllReportsForRanking", "UserRankingList", "message", "axiosInstance", "jsxDEV", "_jsxDEV", "Ranking", "_s", "userState", "state", "users", "user", "rankingData", "setRankingData", "loading", "setLoading", "error", "setError", "refreshing", "setRefreshing", "currentUserRank", "setCurrentUserRank", "showFindMe", "setShowFindMe", "lastUpdated", "setLastUpdated", "autoRefresh", "setAutoRefresh", "currentUserRef", "refreshIntervalRef", "fetchRankingData", "showRefreshMessage", "_id", "console", "log", "response", "timestamp", "Date", "getTime", "xpResponse", "get", "headers", "data", "success", "_response$data", "length", "Error", "xpError", "enhancedResponse", "_response$data2", "enhancedError", "_response$data3", "transformedData", "map", "userData", "index", "userId", "name", "userName", "profilePicture", "userPhoto", "profileImage", "school", "userSchool", "class", "userClass", "level", "userLevel", "email", "totalPoints", "totalPointsEarned", "quizzesTaken", "totalQuizzesTaken", "passedExamsCount", "retryCount", "scoreRatio", "totalXP", "currentLevel", "xpToNextLevel", "seasonXP", "lifetimeXP", "averageScore", "bestStreak", "currentStreak", "achievements", "achievementCount", "rankingScore", "enhancedRankingScore", "rank", "score", "subscriptionStatus", "normalizedSubscriptionStatus", "subscriptionPlan", "subscriptionEndDate", "breakdown", "createdAt", "updatedAt", "userRank", "findIndex", "item", "err", "errorMessage", "_err$response$data", "status", "request", "current", "setInterval", "clearInterval", "handleFindMe", "scrollIntoView", "behavior", "block", "setTimeout", "handleRefresh", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "history", "back", "disabled", "currentUserId", "layout", "size", "showStats", "onAutoRefreshToggle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState, useRef, useCallback } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { Tb<PERSON><PERSON><PERSON>, TbMedal, TbRefresh, Tb<PERSON>lertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from \"react-icons/tb\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\r\nimport { message } from \"antd\";\r\nimport axiosInstance from \"../../../apicalls\";\r\n\r\nconst Ranking = () => {\r\n    const userState = useSelector((state) => state.users || {});\r\n    const { user } = userState;\r\n    const [rankingData, setRankingData] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [refreshing, setRefreshing] = useState(false);\r\n    const [currentUserRank, setCurrentUserRank] = useState(null);\r\n    const [showFindMe, setShowFindMe] = useState(false);\r\n    const [lastUpdated, setLastUpdated] = useState(null);\r\n    const [autoRefresh, setAutoRefresh] = useState(false);\r\n    const currentUserRef = useRef(null);\r\n    const refreshIntervalRef = useRef(null);\r\n\r\n    const fetchRankingData = useCallback(async (showRefreshMessage = false) => {\r\n        try {\r\n            // Check if user is available\r\n            if (!user || !user._id) {\r\n                console.log('🚫 User not available, skipping ranking fetch');\r\n                return;\r\n            }\r\n\r\n            console.log('🔄 Starting ranking data fetch for user:', user._id);\r\n            setLoading(true);\r\n\r\n            if (showRefreshMessage) {\r\n                setRefreshing(true);\r\n                message.loading(\"Refreshing rankings...\", 1);\r\n            }\r\n\r\n            // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking\r\n            let response;\r\n\r\n            // Add cache-busting timestamp to ensure fresh data\r\n            const timestamp = new Date().getTime();\r\n\r\n            try {\r\n                // Try new XP-based leaderboard first\r\n                console.log('🎯 Attempting XP leaderboard API call...');\r\n                const xpResponse = await axiosInstance.get(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {\r\n                    headers: {\r\n                        'Cache-Control': 'no-cache'\r\n                    }\r\n                });\r\n\r\n                console.log('📊 XP leaderboard response:', xpResponse.data);\r\n\r\n                if (xpResponse.data.success) {\r\n                    response = xpResponse.data;\r\n                    console.log('✅ Using XP-based leaderboard with', response.data?.length || 0, 'users');\r\n                } else {\r\n                    throw new Error('XP leaderboard failed, trying enhanced leaderboard');\r\n                }\r\n            } catch (xpError) {\r\n                console.log('❌ XP leaderboard failed, trying enhanced leaderboard:', xpError);\r\n                try {\r\n                    const enhancedResponse = await axiosInstance.get(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {\r\n                        headers: {\r\n                            'Cache-Control': 'no-cache'\r\n                        }\r\n                    });\r\n\r\n                    console.log('📊 Enhanced leaderboard response:', enhancedResponse.data);\r\n\r\n                    if (enhancedResponse.data.success) {\r\n                        response = enhancedResponse.data;\r\n                        console.log('✅ Using enhanced leaderboard with', response.data?.length || 0, 'users');\r\n                    } else {\r\n                        throw new Error('Enhanced leaderboard failed');\r\n                    }\r\n                } catch (enhancedError) {\r\n                    console.log('❌ Falling back to regular ranking:', enhancedError);\r\n                    response = await getAllReportsForRanking();\r\n                    console.log('📊 Regular ranking response:', response);\r\n                }\r\n            }\r\n\r\n            if (response.success) {\r\n                console.log('🔄 Transforming ranking data...', response.data?.length || 0, 'users');\r\n\r\n                // Transform data to match UserRankingCard expectations with enhanced XP system support\r\n                const transformedData = response.data.map((userData, index) => ({\r\n                    // User identification - handle both old and new API formats\r\n                    userId: userData.userId || userData._id,\r\n                    _id: userData.userId || userData._id,\r\n                    name: userData.userName || userData.name || 'Unknown User',\r\n                    profilePicture: userData.userPhoto || userData.profileImage,\r\n                    profileImage: userData.profileImage || userData.userPhoto,\r\n                    school: userData.userSchool || userData.school || 'Unknown School',\r\n                    class: userData.userClass || userData.class || 'Unknown',\r\n                    level: userData.userLevel || userData.level || 'Primary',\r\n                    email: userData.email,\r\n\r\n                    // Legacy points system\r\n                    totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,\r\n                    totalPointsEarned: userData.totalPointsEarned || userData.totalPoints || 0,\r\n                    quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\r\n                    totalQuizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\r\n                    passedExamsCount: userData.passedExamsCount || 0,\r\n                    retryCount: userData.retryCount || 0,\r\n                    scoreRatio: userData.scoreRatio || 0,\r\n\r\n                    // XP System data (new)\r\n                    totalXP: userData.totalXP || 0,\r\n                    currentLevel: userData.currentLevel || 1,\r\n                    xpToNextLevel: userData.xpToNextLevel || 0,\r\n                    seasonXP: userData.seasonXP || 0,\r\n                    lifetimeXP: userData.lifetimeXP || 0,\r\n\r\n                    // Statistics\r\n                    averageScore: userData.averageScore || 0,\r\n                    bestStreak: userData.bestStreak || 0,\r\n                    currentStreak: userData.currentStreak || 0,\r\n                    achievements: userData.achievements || [],\r\n                    achievementCount: userData.achievementCount || (userData.achievements ? userData.achievements.length : 0),\r\n\r\n                    // Ranking data (prioritize ranking score from enhanced system)\r\n                    rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,\r\n                    rank: userData.rank || index + 1,\r\n                    score: userData.rankingScore || userData.score || userData.totalXP || userData.totalPoints || 0,\r\n\r\n                    // Subscription status (handle normalized status)\r\n                    subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\r\n                    normalizedSubscriptionStatus: userData.normalizedSubscriptionStatus || userData.subscriptionStatus || 'free',\r\n                    subscriptionPlan: userData.subscriptionPlan,\r\n                    subscriptionEndDate: userData.subscriptionEndDate,\r\n\r\n                    // XP breakdown (if available from new system)\r\n                    breakdown: userData.breakdown || null,\r\n\r\n                    // Additional metadata\r\n                    createdAt: userData.createdAt,\r\n                    updatedAt: userData.updatedAt\r\n                }));\r\n\r\n                setRankingData(transformedData);\r\n                setError(null);\r\n                setLastUpdated(new Date());\r\n\r\n                // Find current user's rank\r\n                const userRank = transformedData.findIndex(item =>\r\n                    item._id === user?._id || item.userId === user?._id\r\n                );\r\n                setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\r\n\r\n                if (showRefreshMessage) {\r\n                    message.success(\"Rankings updated successfully!\");\r\n                }\r\n            } else {\r\n                setError(response.message || \"Failed to fetch ranking data\");\r\n                message.error(\"Failed to load rankings\");\r\n            }\r\n        } catch (err) {\r\n            console.error('Ranking fetch error:', err);\r\n\r\n            // Handle different types of errors\r\n            let errorMessage = \"An error occurred while fetching rankings\";\r\n\r\n            if (err.response) {\r\n                // Server responded with error status\r\n                errorMessage = err.response.data?.message || `Server error: ${err.response.status}`;\r\n            } else if (err.request) {\r\n                // Request was made but no response received\r\n                errorMessage = \"Network error: Unable to connect to server\";\r\n            } else if (err.message) {\r\n                // Something else happened\r\n                errorMessage = err.message;\r\n            }\r\n\r\n            setError(errorMessage);\r\n            message.error(errorMessage);\r\n        } finally {\r\n            setLoading(false);\r\n            setRefreshing(false);\r\n        }\r\n    }, [user]); // Add user as dependency\r\n\r\n    useEffect(() => {\r\n        fetchRankingData();\r\n\r\n        // Cleanup function to reset states when component unmounts\r\n        return () => {\r\n            setLoading(false);\r\n            setRefreshing(false);\r\n            setError(null);\r\n        };\r\n    }, [fetchRankingData]);\r\n\r\n    // Auto-refresh functionality\r\n    useEffect(() => {\r\n        if (autoRefresh) {\r\n            refreshIntervalRef.current = setInterval(() => {\r\n                fetchRankingData(false); // Silent refresh\r\n            }, 30000); // Refresh every 30 seconds\r\n        } else {\r\n            if (refreshIntervalRef.current) {\r\n                clearInterval(refreshIntervalRef.current);\r\n                refreshIntervalRef.current = null;\r\n            }\r\n        }\r\n\r\n        // Cleanup on unmount\r\n        return () => {\r\n            if (refreshIntervalRef.current) {\r\n                clearInterval(refreshIntervalRef.current);\r\n            }\r\n        };\r\n    }, [autoRefresh, fetchRankingData]);\r\n\r\n    // Find Me functionality\r\n    const handleFindMe = () => {\r\n        if (currentUserRef.current) {\r\n            currentUserRef.current.scrollIntoView({\r\n                behavior: 'smooth',\r\n                block: 'center'\r\n            });\r\n            setShowFindMe(true);\r\n            setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds\r\n        }\r\n    };\r\n\r\n    const handleRefresh = () => {\r\n        fetchRankingData(true);\r\n    };\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center\">\r\n                <div className=\"text-center bg-white rounded-xl p-8 shadow-lg animate-fadeInUp\">\r\n                    <div className=\"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4 animate-spin\" />\r\n                    <p className=\"text-gray-600 font-medium\">Loading rankings...</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\">\r\n                <div className=\"text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full animate-fadeInUp\">\r\n                    <TbAlertCircle className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\r\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Error Loading Rankings</h2>\r\n                    <p className=\"text-gray-600 mb-6\">{error}</p>\r\n                    <button\r\n                        onClick={handleRefresh}\r\n                        className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center space-x-2 mx-auto hover:scale-105 active:scale-95\"\r\n                    >\r\n                        <TbRefresh className=\"w-5 h-5\" />\r\n                        <span>Try Again</span>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden\">\r\n            {/* Modern Background Pattern */}\r\n            <div className=\"absolute inset-0 overflow-hidden\">\r\n                <div className=\"absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse\"></div>\r\n                <div className=\"absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-purple-200 to-pink-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-2000\"></div>\r\n                <div className=\"absolute bottom-0 left-1/2 w-96 h-96 bg-gradient-to-br from-yellow-200 to-orange-300 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-pulse animation-delay-4000\"></div>\r\n\r\n                {/* Grid Pattern */}\r\n                <div className=\"absolute inset-0 bg-grid-pattern opacity-5\"></div>\r\n            </div>\r\n\r\n            <div className=\"relative z-10\">\r\n                {/* Modern Header with Back Button */}\r\n                {/* Enhanced Header */}\r\n                <div className=\"p-6 border-b border-white/20 animate-slideInLeft\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <div className=\"flex items-center space-x-4\">\r\n                            <button\r\n                                onClick={() => window.history.back()}\r\n                                className=\"bg-white/80 backdrop-blur-lg hover:bg-white/90 text-gray-700 px-4 py-2 rounded-xl font-semibold transition-all duration-200 flex items-center space-x-2 border border-gray-200 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95\"\r\n                            >\r\n                                <TbArrowLeft className=\"w-5 h-5\" />\r\n                                <span>Back</span>\r\n                            </button>\r\n\r\n                            {/* Page Title */}\r\n                            <div>\r\n                                <h1 className=\"text-2xl font-black text-gray-900 gradient-text-blue\">\r\n                                    🏆 Student Rankings\r\n                                </h1>\r\n                                <p className=\"text-sm text-gray-600 font-medium\">\r\n                                    Compete with students across all levels\r\n                                </p>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div className=\"flex items-center space-x-3\">\r\n                            {/* Current User Rank Display */}\r\n                            {currentUserRank && (\r\n                                <div className=\"bg-white/80 backdrop-blur-lg rounded-xl px-4 py-2 border border-gray-200 shadow-lg\">\r\n                                    <div className=\"text-center\">\r\n                                        <div className=\"text-xs text-gray-500 font-medium\">Your Rank</div>\r\n                                        <div className=\"text-lg font-black text-blue-600\">#{currentUserRank.rank}</div>\r\n                                    </div>\r\n                                </div>\r\n                            )}\r\n\r\n                            {/* Find Me Button */}\r\n                            {currentUserRank && (\r\n                                <button\r\n                                    onClick={handleFindMe}\r\n                                    className=\"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-3 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg hover:scale-105 active:scale-95\"\r\n                                >\r\n                                    <TbTarget className=\"w-5 h-5\" />\r\n                                    <span>Find Me</span>\r\n                                </button>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                {/* Amazing Header */}\r\n                <div className=\"text-center mb-8 px-4 animate-fadeInUp\">\r\n                    <div className=\"flex items-center justify-center gap-4 mb-6\">\r\n                        <div className=\"relative animate-wiggle\">\r\n                            <TbTrophy className=\"text-6xl text-yellow-400 drop-shadow-lg\" />\r\n                            <div className=\"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-pulse\" />\r\n                        </div>\r\n                        <div>\r\n                            <h1 className=\"text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2\">\r\n                                🏆 LEADERBOARD\r\n                            </h1>\r\n                            <div className=\"flex items-center justify-center gap-2 text-white/80\">\r\n                                <TbStar className=\"text-yellow-400\" />\r\n                                <span className=\"text-lg font-medium\">Battle for Glory</span>\r\n                                <TbStar className=\"text-yellow-400\" />\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Stats Bar */}\r\n                    <div className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto animate-scaleIn\">\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\">\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbFlame className=\"text-orange-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    {currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'}\r\n                                </span>\r\n                            </div>\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbAward className=\"text-purple-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    {rankingData.length} Competitors\r\n                                </span>\r\n                            </div>\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbMedal className=\"text-yellow-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    Live Rankings\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            {/* Main Content */}\r\n            <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\">\r\n                {rankingData.length === 0 ? (\r\n                    <div className=\"text-center py-16 bg-white/80 backdrop-blur-lg rounded-2xl border border-white/50 shadow-2xl animate-fadeInUp\">\r\n                        <div className=\"animate-wiggle\">\r\n                            <TbMedal className=\"w-16 h-16 text-yellow-500 mx-auto mb-4\" />\r\n                        </div>\r\n                        <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">No Rankings Available</h3>\r\n                        <p className=\"text-gray-600 mb-6 text-lg\">Complete some quizzes to join the leaderboard!</p>\r\n                        <button\r\n                            onClick={handleRefresh}\r\n                            disabled={refreshing}\r\n                            className={`bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg hover:scale-105 active:scale-95 ${refreshing ? 'opacity-50 cursor-not-allowed' : ''}`}\r\n                        >\r\n                            <TbRefresh className={`w-5 h-5 inline mr-2 ${refreshing ? 'animate-spin' : ''}`} />\r\n                            {refreshing ? 'Refreshing...' : 'Refresh Rankings'}\r\n                        </button>\r\n                    </div>\r\n                ) : (\r\n                    <div className=\"bg-white/80 backdrop-blur-xl rounded-2xl border border-white/50 p-8 shadow-2xl animate-fadeInUp\">\r\n                        {/* Achievement Banner for Top Users */}\r\n                        {currentUserRank && currentUserRank.rank <= 10 && (\r\n                            <div className=\"mb-6 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 rounded-xl p-4 text-white animate-bounce\">\r\n                                <div className=\"flex items-center justify-center space-x-2\">\r\n                                    <TbTrophy className=\"w-6 h-6\" />\r\n                                    <span className=\"font-bold text-lg\">\r\n                                        🎉 Congratulations! You're in the Top 10! 🎉\r\n                                    </span>\r\n                                    <TbTrophy className=\"w-6 h-6\" />\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n                        <UserRankingList\r\n                            users={rankingData}\r\n                            currentUserId={user?._id || null}\r\n                            layout=\"horizontal\"\r\n                            size=\"medium\"\r\n                            showStats={true}\r\n                            className=\"space-y-4\"\r\n                            currentUserRef={currentUserRef}\r\n                            showFindMe={showFindMe}\r\n                            lastUpdated={lastUpdated}\r\n                            autoRefresh={autoRefresh}\r\n                            onAutoRefreshToggle={() => setAutoRefresh(!autoRefresh)}\r\n                        />\r\n                    </div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC7H,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,OAAO,QAAQ,MAAM;AAC9B,OAAOC,aAAa,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,SAAS,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAM;IAAEC;EAAK,CAAC,GAAGH,SAAS;EAC1B,MAAM,CAACI,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMyC,cAAc,GAAGxC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMyC,kBAAkB,GAAGzC,MAAM,CAAC,IAAI,CAAC;EAEvC,MAAM0C,gBAAgB,GAAGzC,WAAW,CAAC,OAAO0C,kBAAkB,GAAG,KAAK,KAAK;IACvE,IAAI;MACA;MACA,IAAI,CAACpB,IAAI,IAAI,CAACA,IAAI,CAACqB,GAAG,EAAE;QACpBC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC5D;MACJ;MAEAD,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEvB,IAAI,CAACqB,GAAG,CAAC;MACjEjB,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIgB,kBAAkB,EAAE;QACpBZ,aAAa,CAAC,IAAI,CAAC;QACnBjB,OAAO,CAACY,OAAO,CAAC,wBAAwB,EAAE,CAAC,CAAC;MAChD;;MAEA;MACA,IAAIqB,QAAQ;;MAEZ;MACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAEtC,IAAI;QACA;QACAL,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,MAAMK,UAAU,GAAG,MAAMpC,aAAa,CAACqC,GAAG,CAAE,yCAAwCJ,SAAU,EAAC,EAAE;UAC7FK,OAAO,EAAE;YACL,eAAe,EAAE;UACrB;QACJ,CAAC,CAAC;QAEFR,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEK,UAAU,CAACG,IAAI,CAAC;QAE3D,IAAIH,UAAU,CAACG,IAAI,CAACC,OAAO,EAAE;UAAA,IAAAC,cAAA;UACzBT,QAAQ,GAAGI,UAAU,CAACG,IAAI;UAC1BT,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,EAAAU,cAAA,GAAAT,QAAQ,CAACO,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAeC,MAAM,KAAI,CAAC,EAAE,OAAO,CAAC;QACzF,CAAC,MAAM;UACH,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;QACzE;MACJ,CAAC,CAAC,OAAOC,OAAO,EAAE;QACdd,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAEa,OAAO,CAAC;QAC7E,IAAI;UACA,MAAMC,gBAAgB,GAAG,MAAM7C,aAAa,CAACqC,GAAG,CAAE,+CAA8CJ,SAAU,EAAC,EAAE;YACzGK,OAAO,EAAE;cACL,eAAe,EAAE;YACrB;UACJ,CAAC,CAAC;UAEFR,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEc,gBAAgB,CAACN,IAAI,CAAC;UAEvE,IAAIM,gBAAgB,CAACN,IAAI,CAACC,OAAO,EAAE;YAAA,IAAAM,eAAA;YAC/Bd,QAAQ,GAAGa,gBAAgB,CAACN,IAAI;YAChCT,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,EAAAe,eAAA,GAAAd,QAAQ,CAACO,IAAI,cAAAO,eAAA,uBAAbA,eAAA,CAAeJ,MAAM,KAAI,CAAC,EAAE,OAAO,CAAC;UACzF,CAAC,MAAM;YACH,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAClD;QACJ,CAAC,CAAC,OAAOI,aAAa,EAAE;UACpBjB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEgB,aAAa,CAAC;UAChEf,QAAQ,GAAG,MAAMnC,uBAAuB,CAAC,CAAC;UAC1CiC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,QAAQ,CAAC;QACzD;MACJ;MAEA,IAAIA,QAAQ,CAACQ,OAAO,EAAE;QAAA,IAAAQ,eAAA;QAClBlB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,EAAAiB,eAAA,GAAAhB,QAAQ,CAACO,IAAI,cAAAS,eAAA,uBAAbA,eAAA,CAAeN,MAAM,KAAI,CAAC,EAAE,OAAO,CAAC;;QAEnF;QACA,MAAMO,eAAe,GAAGjB,QAAQ,CAACO,IAAI,CAACW,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,MAAM;UAC5D;UACAC,MAAM,EAAEF,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACtB,GAAG;UACvCA,GAAG,EAAEsB,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACtB,GAAG;UACpCyB,IAAI,EAAEH,QAAQ,CAACI,QAAQ,IAAIJ,QAAQ,CAACG,IAAI,IAAI,cAAc;UAC1DE,cAAc,EAAEL,QAAQ,CAACM,SAAS,IAAIN,QAAQ,CAACO,YAAY;UAC3DA,YAAY,EAAEP,QAAQ,CAACO,YAAY,IAAIP,QAAQ,CAACM,SAAS;UACzDE,MAAM,EAAER,QAAQ,CAACS,UAAU,IAAIT,QAAQ,CAACQ,MAAM,IAAI,gBAAgB;UAClEE,KAAK,EAAEV,QAAQ,CAACW,SAAS,IAAIX,QAAQ,CAACU,KAAK,IAAI,SAAS;UACxDE,KAAK,EAAEZ,QAAQ,CAACa,SAAS,IAAIb,QAAQ,CAACY,KAAK,IAAI,SAAS;UACxDE,KAAK,EAAEd,QAAQ,CAACc,KAAK;UAErB;UACAC,WAAW,EAAEf,QAAQ,CAACgB,iBAAiB,IAAIhB,QAAQ,CAACe,WAAW,IAAI,CAAC;UACpEC,iBAAiB,EAAEhB,QAAQ,CAACgB,iBAAiB,IAAIhB,QAAQ,CAACe,WAAW,IAAI,CAAC;UAC1EE,YAAY,EAAEjB,QAAQ,CAACkB,iBAAiB,IAAIlB,QAAQ,CAACiB,YAAY,IAAI,CAAC;UACtEC,iBAAiB,EAAElB,QAAQ,CAACkB,iBAAiB,IAAIlB,QAAQ,CAACiB,YAAY,IAAI,CAAC;UAC3EE,gBAAgB,EAAEnB,QAAQ,CAACmB,gBAAgB,IAAI,CAAC;UAChDC,UAAU,EAAEpB,QAAQ,CAACoB,UAAU,IAAI,CAAC;UACpCC,UAAU,EAAErB,QAAQ,CAACqB,UAAU,IAAI,CAAC;UAEpC;UACAC,OAAO,EAAEtB,QAAQ,CAACsB,OAAO,IAAI,CAAC;UAC9BC,YAAY,EAAEvB,QAAQ,CAACuB,YAAY,IAAI,CAAC;UACxCC,aAAa,EAAExB,QAAQ,CAACwB,aAAa,IAAI,CAAC;UAC1CC,QAAQ,EAAEzB,QAAQ,CAACyB,QAAQ,IAAI,CAAC;UAChCC,UAAU,EAAE1B,QAAQ,CAAC0B,UAAU,IAAI,CAAC;UAEpC;UACAC,YAAY,EAAE3B,QAAQ,CAAC2B,YAAY,IAAI,CAAC;UACxCC,UAAU,EAAE5B,QAAQ,CAAC4B,UAAU,IAAI,CAAC;UACpCC,aAAa,EAAE7B,QAAQ,CAAC6B,aAAa,IAAI,CAAC;UAC1CC,YAAY,EAAE9B,QAAQ,CAAC8B,YAAY,IAAI,EAAE;UACzCC,gBAAgB,EAAE/B,QAAQ,CAAC+B,gBAAgB,KAAK/B,QAAQ,CAAC8B,YAAY,GAAG9B,QAAQ,CAAC8B,YAAY,CAACvC,MAAM,GAAG,CAAC,CAAC;UAEzG;UACAyC,YAAY,EAAEhC,QAAQ,CAACgC,YAAY,IAAIhC,QAAQ,CAACiC,oBAAoB,IAAIjC,QAAQ,CAACsB,OAAO,IAAItB,QAAQ,CAACe,WAAW,IAAI,CAAC;UACrHmB,IAAI,EAAElC,QAAQ,CAACkC,IAAI,IAAIjC,KAAK,GAAG,CAAC;UAChCkC,KAAK,EAAEnC,QAAQ,CAACgC,YAAY,IAAIhC,QAAQ,CAACmC,KAAK,IAAInC,QAAQ,CAACsB,OAAO,IAAItB,QAAQ,CAACe,WAAW,IAAI,CAAC;UAE/F;UACAqB,kBAAkB,EAAEpC,QAAQ,CAACoC,kBAAkB,IAAIpC,QAAQ,CAACqC,4BAA4B,IAAI,MAAM;UAClGA,4BAA4B,EAAErC,QAAQ,CAACqC,4BAA4B,IAAIrC,QAAQ,CAACoC,kBAAkB,IAAI,MAAM;UAC5GE,gBAAgB,EAAEtC,QAAQ,CAACsC,gBAAgB;UAC3CC,mBAAmB,EAAEvC,QAAQ,CAACuC,mBAAmB;UAEjD;UACAC,SAAS,EAAExC,QAAQ,CAACwC,SAAS,IAAI,IAAI;UAErC;UACAC,SAAS,EAAEzC,QAAQ,CAACyC,SAAS;UAC7BC,SAAS,EAAE1C,QAAQ,CAAC0C;QACxB,CAAC,CAAC,CAAC;QAEHnF,cAAc,CAACuC,eAAe,CAAC;QAC/BnC,QAAQ,CAAC,IAAI,CAAC;QACdQ,cAAc,CAAC,IAAIY,IAAI,CAAC,CAAC,CAAC;;QAE1B;QACA,MAAM4D,QAAQ,GAAG7C,eAAe,CAAC8C,SAAS,CAACC,IAAI,IAC3CA,IAAI,CAACnE,GAAG,MAAKrB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,GAAG,KAAImE,IAAI,CAAC3C,MAAM,MAAK7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,GAAG,CACvD,CAAC;QACDX,kBAAkB,CAAC4E,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;QAEvD,IAAIlE,kBAAkB,EAAE;UACpB7B,OAAO,CAACyC,OAAO,CAAC,gCAAgC,CAAC;QACrD;MACJ,CAAC,MAAM;QACH1B,QAAQ,CAACkB,QAAQ,CAACjC,OAAO,IAAI,8BAA8B,CAAC;QAC5DA,OAAO,CAACc,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACJ,CAAC,CAAC,OAAOoF,GAAG,EAAE;MACVnE,OAAO,CAACjB,KAAK,CAAC,sBAAsB,EAAEoF,GAAG,CAAC;;MAE1C;MACA,IAAIC,YAAY,GAAG,2CAA2C;MAE9D,IAAID,GAAG,CAACjE,QAAQ,EAAE;QAAA,IAAAmE,kBAAA;QACd;QACAD,YAAY,GAAG,EAAAC,kBAAA,GAAAF,GAAG,CAACjE,QAAQ,CAACO,IAAI,cAAA4D,kBAAA,uBAAjBA,kBAAA,CAAmBpG,OAAO,KAAK,iBAAgBkG,GAAG,CAACjE,QAAQ,CAACoE,MAAO,EAAC;MACvF,CAAC,MAAM,IAAIH,GAAG,CAACI,OAAO,EAAE;QACpB;QACAH,YAAY,GAAG,4CAA4C;MAC/D,CAAC,MAAM,IAAID,GAAG,CAAClG,OAAO,EAAE;QACpB;QACAmG,YAAY,GAAGD,GAAG,CAAClG,OAAO;MAC9B;MAEAe,QAAQ,CAACoF,YAAY,CAAC;MACtBnG,OAAO,CAACc,KAAK,CAACqF,YAAY,CAAC;IAC/B,CAAC,SAAS;MACNtF,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC,EAAE,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZzB,SAAS,CAAC,MAAM;IACZ4C,gBAAgB,CAAC,CAAC;;IAElB;IACA,OAAO,MAAM;MACTf,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,KAAK,CAAC;MACpBF,QAAQ,CAAC,IAAI,CAAC;IAClB,CAAC;EACL,CAAC,EAAE,CAACa,gBAAgB,CAAC,CAAC;;EAEtB;EACA5C,SAAS,CAAC,MAAM;IACZ,IAAIwC,WAAW,EAAE;MACbG,kBAAkB,CAAC4E,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC3C5E,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC7B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACf,CAAC,MAAM;MACH,IAAID,kBAAkB,CAAC4E,OAAO,EAAE;QAC5BE,aAAa,CAAC9E,kBAAkB,CAAC4E,OAAO,CAAC;QACzC5E,kBAAkB,CAAC4E,OAAO,GAAG,IAAI;MACrC;IACJ;;IAEA;IACA,OAAO,MAAM;MACT,IAAI5E,kBAAkB,CAAC4E,OAAO,EAAE;QAC5BE,aAAa,CAAC9E,kBAAkB,CAAC4E,OAAO,CAAC;MAC7C;IACJ,CAAC;EACL,CAAC,EAAE,CAAC/E,WAAW,EAAEI,gBAAgB,CAAC,CAAC;;EAEnC;EACA,MAAM8E,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIhF,cAAc,CAAC6E,OAAO,EAAE;MACxB7E,cAAc,CAAC6E,OAAO,CAACI,cAAc,CAAC;QAClCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACFxF,aAAa,CAAC,IAAI,CAAC;MACnByF,UAAU,CAAC,MAAMzF,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAClD;EACJ,CAAC;;EAED,MAAM0F,aAAa,GAAGA,CAAA,KAAM;IACxBnF,gBAAgB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAIhB,OAAO,EAAE;IACT,oBACIT,OAAA;MAAK6G,SAAS,EAAC,2FAA2F;MAAAC,QAAA,eACtG9G,OAAA;QAAK6G,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAC3E9G,OAAA;UAAK6G,SAAS,EAAC;QAAgG;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClHlH,OAAA;UAAG6G,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIvG,KAAK,EAAE;IACP,oBACIX,OAAA;MAAK6G,SAAS,EAAC,+FAA+F;MAAAC,QAAA,eAC1G9G,OAAA;QAAK6G,SAAS,EAAC,gFAAgF;QAAAC,QAAA,gBAC3F9G,OAAA,CAACX,aAAa;UAACwH,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjElH,OAAA;UAAI6G,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpFlH,OAAA;UAAG6G,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEnG;QAAK;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7ClH,OAAA;UACImH,OAAO,EAAEP,aAAc;UACvBC,SAAS,EAAC,2KAA2K;UAAAC,QAAA,gBAErL9G,OAAA,CAACZ,SAAS;YAACyH,SAAS,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjClH,OAAA;YAAA8G,QAAA,EAAM;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIlH,OAAA;IAAK6G,SAAS,EAAC,iGAAiG;IAAAC,QAAA,gBAE5G9G,OAAA;MAAK6G,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC7C9G,OAAA;QAAK6G,SAAS,EAAC;MAAwJ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC9KlH,OAAA;QAAK6G,SAAS,EAAC;MAA8K;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpMlH,OAAA;QAAK6G,SAAS,EAAC;MAAoL;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAG1MlH,OAAA;QAAK6G,SAAS,EAAC;MAA4C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjE,CAAC,eAENlH,OAAA;MAAK6G,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAG1B9G,OAAA;QAAK6G,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC7D9G,OAAA;UAAK6G,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAC9C9G,OAAA;YAAK6G,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBACxC9G,OAAA;cACImH,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;cACrCT,SAAS,EAAC,0OAA0O;cAAAC,QAAA,gBAEpP9G,OAAA,CAACV,WAAW;gBAACuH,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnClH,OAAA;gBAAA8G,QAAA,EAAM;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eAGTlH,OAAA;cAAA8G,QAAA,gBACI9G,OAAA;gBAAI6G,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,EAAC;cAErE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlH,OAAA;gBAAG6G,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAEjD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENlH,OAAA;YAAK6G,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GAEvC/F,eAAe,iBACZf,OAAA;cAAK6G,SAAS,EAAC,oFAAoF;cAAAC,QAAA,eAC/F9G,OAAA;gBAAK6G,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxB9G,OAAA;kBAAK6G,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClElH,OAAA;kBAAK6G,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,GAAC,GAAC,EAAC/F,eAAe,CAACoE,IAAI;gBAAA;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR,EAGAnG,eAAe,iBACZf,OAAA;cACImH,OAAO,EAAEZ,YAAa;cACtBM,SAAS,EAAC,sOAAsO;cAAAC,QAAA,gBAEhP9G,OAAA,CAACT,QAAQ;gBAACsH,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAChClH,OAAA;gBAAA8G,QAAA,EAAM;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB,CACX;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlH,OAAA;QAAK6G,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACnD9G,OAAA;UAAK6G,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBACxD9G,OAAA;YAAK6G,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACpC9G,OAAA,CAACd,QAAQ;cAAC2H,SAAS,EAAC;YAAyC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChElH,OAAA;cAAK6G,SAAS,EAAC;YAA2E;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5F,CAAC,eACNlH,OAAA;YAAA8G,QAAA,gBACI9G,OAAA;cAAI6G,SAAS,EAAC,oHAAoH;cAAAC,QAAA,EAAC;YAEnI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlH,OAAA;cAAK6G,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACjE9G,OAAA,CAACR,MAAM;gBAACqH,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtClH,OAAA;gBAAM6G,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7DlH,OAAA,CAACR,MAAM;gBAACqH,SAAS,EAAC;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNlH,OAAA;UAAK6G,SAAS,EAAC,4GAA4G;UAAAC,QAAA,eACvH9G,OAAA;YAAK6G,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAC9D9G,OAAA;cAAK6G,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD9G,OAAA,CAACP,OAAO;gBAACoH,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/ClH,OAAA;gBAAM6G,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACrC/F,eAAe,GAAI,eAAcA,eAAgB,EAAC,GAAG;cAAuB;gBAAAgG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNlH,OAAA;cAAK6G,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD9G,OAAA,CAACN,OAAO;gBAACmH,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/ClH,OAAA;gBAAM6G,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GACrCvG,WAAW,CAACiC,MAAM,EAAC,cACxB;cAAA;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNlH,OAAA;cAAK6G,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD9G,OAAA,CAACb,OAAO;gBAAC0H,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/ClH,OAAA;gBAAM6G,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAE3C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNlH,OAAA;MAAK6G,SAAS,EAAC,2DAA2D;MAAAC,QAAA,EACrEvG,WAAW,CAACiC,MAAM,KAAK,CAAC,gBACrBxC,OAAA;QAAK6G,SAAS,EAAC,+GAA+G;QAAAC,QAAA,gBAC1H9G,OAAA;UAAK6G,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC3B9G,OAAA,CAACb,OAAO;YAAC0H,SAAS,EAAC;UAAwC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNlH,OAAA;UAAI6G,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChFlH,OAAA;UAAG6G,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5FlH,OAAA;UACImH,OAAO,EAAEP,aAAc;UACvBW,QAAQ,EAAE1G,UAAW;UACrBgG,SAAS,EAAG,wMAAuMhG,UAAU,GAAG,+BAA+B,GAAG,EAAG,EAAE;UAAAiG,QAAA,gBAEvQ9G,OAAA,CAACZ,SAAS;YAACyH,SAAS,EAAG,uBAAsBhG,UAAU,GAAG,cAAc,GAAG,EAAG;UAAE;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAClFrG,UAAU,GAAG,eAAe,GAAG,kBAAkB;QAAA;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,gBAENlH,OAAA;QAAK6G,SAAS,EAAC,iGAAiG;QAAAC,QAAA,GAE3G/F,eAAe,IAAIA,eAAe,CAACoE,IAAI,IAAI,EAAE,iBAC1CnF,OAAA;UAAK6G,SAAS,EAAC,0GAA0G;UAAAC,QAAA,eACrH9G,OAAA;YAAK6G,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACvD9G,OAAA,CAACd,QAAQ;cAAC2H,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChClH,OAAA;cAAM6G,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAEpC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPlH,OAAA,CAACd,QAAQ;cAAC2H,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,eACDlH,OAAA,CAACJ,eAAe;UACZS,KAAK,EAAEE,WAAY;UACnBiH,aAAa,EAAE,CAAAlH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,GAAG,KAAI,IAAK;UACjC8F,MAAM,EAAC,YAAY;UACnBC,IAAI,EAAC,QAAQ;UACbC,SAAS,EAAE,IAAK;UAChBd,SAAS,EAAC,WAAW;UACrBtF,cAAc,EAAEA,cAAe;UAC/BN,UAAU,EAAEA,UAAW;UACvBE,WAAW,EAAEA,WAAY;UACzBE,WAAW,EAAEA,WAAY;UACzBuG,mBAAmB,EAAEA,CAAA,KAAMtG,cAAc,CAAC,CAACD,WAAW;QAAE;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAChH,EAAA,CA5ZID,OAAO;EAAA,QACShB,WAAW;AAAA;AAAA4I,EAAA,GAD3B5H,OAAO;AA8Zb,eAAeA,OAAO;AAAC,IAAA4H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}