{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { TbTrophy, TbMedal, TbRefresh, TbAlertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from \"react-icons/tb\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\nimport { message } from \"antd\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const {\n    user\n  } = userState;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [refreshing, setRefreshing] = useState(false);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [showFindMe, setShowFindMe] = useState(false);\n  const currentUserRef = useRef(null);\n  const fetchRankingData = async (showRefreshMessage = false) => {\n    try {\n      if (showRefreshMessage) {\n        setRefreshing(true);\n        message.loading(\"Refreshing rankings...\", 1);\n      }\n\n      // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking\n      let response;\n      try {\n        // Add cache-busting timestamp to ensure fresh data\n        const timestamp = new Date().getTime();\n\n        // Try new XP-based leaderboard first\n        const xpResponse = await fetch(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Cache-Control': 'no-cache'\n          }\n        });\n        const xpData = await xpResponse.json();\n        if (xpData.success) {\n          response = xpData;\n          console.log('Using XP-based leaderboard');\n        } else {\n          throw new Error('XP leaderboard failed, trying enhanced leaderboard');\n        }\n      } catch (xpError) {\n        console.log('XP leaderboard failed, trying enhanced leaderboard:', xpError);\n        try {\n          const enhancedResponse = await fetch(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {\n            headers: {\n              'Authorization': `Bearer ${localStorage.getItem('token')}`,\n              'Cache-Control': 'no-cache'\n            }\n          });\n          const enhancedData = await enhancedResponse.json();\n          if (enhancedData.success) {\n            response = enhancedData;\n            console.log('Using enhanced leaderboard');\n          } else {\n            throw new Error('Enhanced leaderboard failed');\n          }\n        } catch (enhancedError) {\n          console.log('Falling back to regular ranking:', enhancedError);\n          response = await getAllReportsForRanking();\n        }\n      }\n      if (response.success) {\n        // Transform data to match UserRankingCard expectations with XP system support\n        const transformedData = response.data.map((userData, index) => ({\n          userId: userData.userId || userData._id,\n          _id: userData.userId || userData._id,\n          name: userData.userName || userData.name,\n          profilePicture: userData.userPhoto || userData.profileImage,\n          school: userData.userSchool || userData.school,\n          class: userData.userClass || userData.class,\n          level: userData.userLevel || userData.level,\n          // Legacy points system\n          totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,\n          quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\n          passedExamsCount: userData.passedExamsCount || 0,\n          retryCount: userData.retryCount || 0,\n          scoreRatio: userData.scoreRatio || 0,\n          // XP System data (new)\n          totalXP: userData.totalXP || 0,\n          currentLevel: userData.currentLevel || 1,\n          xpToNextLevel: userData.xpToNextLevel || 0,\n          seasonXP: userData.seasonXP || 0,\n          lifetimeXP: userData.lifetimeXP || 0,\n          // Statistics\n          averageScore: userData.averageScore || 0,\n          bestStreak: userData.bestStreak || 0,\n          currentStreak: userData.currentStreak || 0,\n          achievements: userData.achievements || [],\n          // Ranking data (prioritize XP-based ranking)\n          rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,\n          rank: userData.rank || index + 1,\n          subscriptionStatus: userData.subscriptionStatus || 'free',\n          // XP breakdown (if available from new system)\n          breakdown: userData.breakdown || null\n        }));\n        setRankingData(transformedData);\n        setError(null);\n\n        // Find current user's rank\n        const userRank = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id) || item.userId === (user === null || user === void 0 ? void 0 : user._id));\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n        if (showRefreshMessage) {\n          message.success(\"Rankings updated successfully!\");\n        }\n      } else {\n        setError(response.message || \"Failed to fetch ranking data\");\n        message.error(\"Failed to load rankings\");\n      }\n    } catch (err) {\n      console.error('Ranking fetch error:', err);\n      setError(err.message || \"An error occurred while fetching rankings\");\n      message.error(\"Network error while loading rankings\");\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n  useEffect(() => {\n    fetchRankingData();\n  }, []);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRef.current) {\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setShowFindMe(true);\n      setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds\n    }\n  };\n\n  const handleRefresh = () => {\n    fetchRankingData(true);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        className: \"text-center bg-white rounded-xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 1,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading rankings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-2\",\n          children: \"Error Loading Rankings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: handleRefresh,\n          className: \"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-40 left-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"p-4 flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05,\n            x: -5\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => window.history.back(),\n          className: \"bg-white/10 backdrop-blur-lg hover:bg-white/20 text-white px-4 py-2 rounded-xl font-medium transition-all duration-200 flex items-center space-x-2 border border-white/20\",\n          children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 21\n        }, this), currentUserRank && /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: handleFindMe,\n          className: \"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-2 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Find Me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8 px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: [0, 10, -10, 0],\n              scale: [1, 1.1, 1]\n            },\n            transition: {\n              duration: 3,\n              repeat: Infinity,\n              repeatDelay: 2\n            },\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"text-6xl text-yellow-400 drop-shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                scale: [1, 1.2, 1]\n              },\n              transition: {\n                duration: 2,\n                repeat: Infinity\n              },\n              className: \"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2\",\n              children: \"\\uD83C\\uDFC6 LEADERBOARD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2 text-white/80\",\n              children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-medium\",\n                children: \"Battle for Glory\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            delay: 0.3\n          },\n          className: \"bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                className: \"text-orange-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbAward, {\n                className: \"text-purple-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: [rankingData.length, \" Competitors\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbMedal, {\n                className: \"text-yellow-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: \"Live Rankings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\",\n      children: rankingData.length === 0 ? /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        className: \"text-center py-16 bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: [0, 10, -10, 0]\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity\n          },\n          children: /*#__PURE__*/_jsxDEV(TbMedal, {\n            className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-white mb-2\",\n          children: \"No Rankings Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 mb-6 text-lg\",\n          children: \"Complete some quizzes to join the leaderboard!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: handleRefresh,\n          className: \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n            className: \"w-5 h-5 inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 29\n          }, this), \"Refresh Rankings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        className: \"bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6\",\n        children: /*#__PURE__*/_jsxDEV(UserRankingList, {\n          users: rankingData,\n          currentUserId: (user === null || user === void 0 ? void 0 : user._id) || null,\n          layout: \"horizontal\",\n          size: \"medium\",\n          showStats: true,\n          className: \"space-y-4\",\n          currentUserRef: currentUserRef,\n          showFindMe: showFindMe\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 202,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"t3kpr1qEzfksVXGlFe4pWAFSbIQ=\", false, function () {\n  return [useSelector];\n});\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useSelector", "motion", "AnimatePresence", "TbTrophy", "TbMedal", "TbRefresh", "TbAlertCircle", "TbArrowLeft", "TbTarget", "TbStar", "TbFlame", "TbAward", "getAllReportsForRanking", "UserRankingList", "message", "jsxDEV", "_jsxDEV", "Ranking", "_s", "userState", "state", "users", "user", "rankingData", "setRankingData", "loading", "setLoading", "error", "setError", "refreshing", "setRefreshing", "currentUserRank", "setCurrentUserRank", "showFindMe", "setShowFindMe", "currentUserRef", "fetchRankingData", "showRefreshMessage", "response", "timestamp", "Date", "getTime", "xpResponse", "fetch", "headers", "localStorage", "getItem", "xpData", "json", "success", "console", "log", "Error", "xpError", "enhancedResponse", "enhancedData", "enhancedError", "transformedData", "data", "map", "userData", "index", "userId", "_id", "name", "userName", "profilePicture", "userPhoto", "profileImage", "school", "userSchool", "class", "userClass", "level", "userLevel", "totalPoints", "totalPointsEarned", "quizzesTaken", "totalQuizzesTaken", "passedExamsCount", "retryCount", "scoreRatio", "totalXP", "currentLevel", "xpToNextLevel", "seasonXP", "lifetimeXP", "averageScore", "bestStreak", "currentStreak", "achievements", "rankingScore", "enhancedRankingScore", "rank", "subscriptionStatus", "breakdown", "userRank", "findIndex", "item", "err", "handleFindMe", "current", "scrollIntoView", "behavior", "block", "setTimeout", "handleRefresh", "className", "children", "div", "initial", "opacity", "scale", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "y", "button", "whileHover", "whileTap", "onClick", "x", "window", "history", "back", "repeatDelay", "delay", "length", "currentUserId", "layout", "size", "showStats", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { TbTrophy, TbMedal, TbRefresh, TbAlertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from \"react-icons/tb\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\r\nimport { message } from \"antd\";\r\n\r\nconst Ranking = () => {\r\n    const userState = useSelector((state) => state.users || {});\r\n    const { user } = userState;\r\n    const [rankingData, setRankingData] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [refreshing, setRefreshing] = useState(false);\r\n    const [currentUserRank, setCurrentUserRank] = useState(null);\r\n    const [showFindMe, setShowFindMe] = useState(false);\r\n    const currentUserRef = useRef(null);\r\n\r\n    const fetchRankingData = async (showRefreshMessage = false) => {\r\n        try {\r\n            if (showRefreshMessage) {\r\n                setRefreshing(true);\r\n                message.loading(\"Refreshing rankings...\", 1);\r\n            }\r\n\r\n            // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking\r\n            let response;\r\n            try {\r\n                // Add cache-busting timestamp to ensure fresh data\r\n                const timestamp = new Date().getTime();\r\n\r\n                // Try new XP-based leaderboard first\r\n                const xpResponse = await fetch(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {\r\n                    headers: {\r\n                        'Authorization': `Bearer ${localStorage.getItem('token')}`,\r\n                        'Cache-Control': 'no-cache'\r\n                    }\r\n                });\r\n                const xpData = await xpResponse.json();\r\n\r\n                if (xpData.success) {\r\n                    response = xpData;\r\n                    console.log('Using XP-based leaderboard');\r\n                } else {\r\n                    throw new Error('XP leaderboard failed, trying enhanced leaderboard');\r\n                }\r\n            } catch (xpError) {\r\n                console.log('XP leaderboard failed, trying enhanced leaderboard:', xpError);\r\n                try {\r\n                    const enhancedResponse = await fetch(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {\r\n                        headers: {\r\n                            'Authorization': `Bearer ${localStorage.getItem('token')}`,\r\n                            'Cache-Control': 'no-cache'\r\n                        }\r\n                    });\r\n                    const enhancedData = await enhancedResponse.json();\r\n\r\n                    if (enhancedData.success) {\r\n                        response = enhancedData;\r\n                        console.log('Using enhanced leaderboard');\r\n                    } else {\r\n                        throw new Error('Enhanced leaderboard failed');\r\n                    }\r\n                } catch (enhancedError) {\r\n                    console.log('Falling back to regular ranking:', enhancedError);\r\n                    response = await getAllReportsForRanking();\r\n                }\r\n            }\r\n\r\n            if (response.success) {\r\n                // Transform data to match UserRankingCard expectations with XP system support\r\n                const transformedData = response.data.map((userData, index) => ({\r\n                    userId: userData.userId || userData._id,\r\n                    _id: userData.userId || userData._id,\r\n                    name: userData.userName || userData.name,\r\n                    profilePicture: userData.userPhoto || userData.profileImage,\r\n                    school: userData.userSchool || userData.school,\r\n                    class: userData.userClass || userData.class,\r\n                    level: userData.userLevel || userData.level,\r\n\r\n                    // Legacy points system\r\n                    totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,\r\n                    quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\r\n                    passedExamsCount: userData.passedExamsCount || 0,\r\n                    retryCount: userData.retryCount || 0,\r\n                    scoreRatio: userData.scoreRatio || 0,\r\n\r\n                    // XP System data (new)\r\n                    totalXP: userData.totalXP || 0,\r\n                    currentLevel: userData.currentLevel || 1,\r\n                    xpToNextLevel: userData.xpToNextLevel || 0,\r\n                    seasonXP: userData.seasonXP || 0,\r\n                    lifetimeXP: userData.lifetimeXP || 0,\r\n\r\n                    // Statistics\r\n                    averageScore: userData.averageScore || 0,\r\n                    bestStreak: userData.bestStreak || 0,\r\n                    currentStreak: userData.currentStreak || 0,\r\n                    achievements: userData.achievements || [],\r\n\r\n                    // Ranking data (prioritize XP-based ranking)\r\n                    rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,\r\n                    rank: userData.rank || index + 1,\r\n                    subscriptionStatus: userData.subscriptionStatus || 'free',\r\n\r\n                    // XP breakdown (if available from new system)\r\n                    breakdown: userData.breakdown || null\r\n                }));\r\n\r\n                setRankingData(transformedData);\r\n                setError(null);\r\n\r\n                // Find current user's rank\r\n                const userRank = transformedData.findIndex(item =>\r\n                    item._id === user?._id || item.userId === user?._id\r\n                );\r\n                setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\r\n\r\n                if (showRefreshMessage) {\r\n                    message.success(\"Rankings updated successfully!\");\r\n                }\r\n            } else {\r\n                setError(response.message || \"Failed to fetch ranking data\");\r\n                message.error(\"Failed to load rankings\");\r\n            }\r\n        } catch (err) {\r\n            console.error('Ranking fetch error:', err);\r\n            setError(err.message || \"An error occurred while fetching rankings\");\r\n            message.error(\"Network error while loading rankings\");\r\n        } finally {\r\n            setLoading(false);\r\n            setRefreshing(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchRankingData();\r\n    }, []);\r\n\r\n    // Find Me functionality\r\n    const handleFindMe = () => {\r\n        if (currentUserRef.current) {\r\n            currentUserRef.current.scrollIntoView({\r\n                behavior: 'smooth',\r\n                block: 'center'\r\n            });\r\n            setShowFindMe(true);\r\n            setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds\r\n        }\r\n    };\r\n\r\n    const handleRefresh = () => {\r\n        fetchRankingData(true);\r\n    };\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center\">\r\n                <motion.div\r\n                    initial={{ opacity: 0, scale: 0.9 }}\r\n                    animate={{ opacity: 1, scale: 1 }}\r\n                    className=\"text-center bg-white rounded-xl p-8 shadow-lg\"\r\n                >\r\n                    <motion.div\r\n                        animate={{ rotate: 360 }}\r\n                        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\r\n                        className=\"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4\"\r\n                    />\r\n                    <p className=\"text-gray-600 font-medium\">Loading rankings...</p>\r\n                </motion.div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\">\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full\"\r\n                >\r\n                    <TbAlertCircle className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\r\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Error Loading Rankings</h2>\r\n                    <p className=\"text-gray-600 mb-6\">{error}</p>\r\n                    <motion.button\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                        onClick={handleRefresh}\r\n                        className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 mx-auto\"\r\n                    >\r\n                        <TbRefresh className=\"w-5 h-5\" />\r\n                        <span>Try Again</span>\r\n                    </motion.button>\r\n                </motion.div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-hidden\">\r\n            {/* Animated Background Elements */}\r\n            <div className=\"absolute inset-0 overflow-hidden\">\r\n                <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\r\n                <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\r\n                <div className=\"absolute top-40 left-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\r\n            </div>\r\n\r\n            <div className=\"relative z-10\">\r\n                {/* Modern Header with Back Button */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: -20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"p-4 flex items-center justify-between\"\r\n                >\r\n                    <motion.button\r\n                        whileHover={{ scale: 1.05, x: -5 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                        onClick={() => window.history.back()}\r\n                        className=\"bg-white/10 backdrop-blur-lg hover:bg-white/20 text-white px-4 py-2 rounded-xl font-medium transition-all duration-200 flex items-center space-x-2 border border-white/20\"\r\n                    >\r\n                        <TbArrowLeft className=\"w-5 h-5\" />\r\n                        <span>Back</span>\r\n                    </motion.button>\r\n\r\n                    {/* Find Me Button */}\r\n                    {currentUserRank && (\r\n                        <motion.button\r\n                            whileHover={{ scale: 1.05 }}\r\n                            whileTap={{ scale: 0.95 }}\r\n                            onClick={handleFindMe}\r\n                            className=\"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-2 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg\"\r\n                        >\r\n                            <TbTarget className=\"w-5 h-5\" />\r\n                            <span>Find Me</span>\r\n                        </motion.button>\r\n                    )}\r\n                </motion.div>\r\n\r\n                {/* Amazing Header */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: -20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"text-center mb-8 px-4\"\r\n                >\r\n                    <div className=\"flex items-center justify-center gap-4 mb-6\">\r\n                        <motion.div\r\n                            animate={{\r\n                                rotate: [0, 10, -10, 0],\r\n                                scale: [1, 1.1, 1]\r\n                            }}\r\n                            transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}\r\n                            className=\"relative\"\r\n                        >\r\n                            <TbTrophy className=\"text-6xl text-yellow-400 drop-shadow-lg\" />\r\n                            <motion.div\r\n                                animate={{ scale: [1, 1.2, 1] }}\r\n                                transition={{ duration: 2, repeat: Infinity }}\r\n                                className=\"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full\"\r\n                            />\r\n                        </motion.div>\r\n                        <div>\r\n                            <h1 className=\"text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2\">\r\n                                🏆 LEADERBOARD\r\n                            </h1>\r\n                            <div className=\"flex items-center justify-center gap-2 text-white/80\">\r\n                                <TbStar className=\"text-yellow-400\" />\r\n                                <span className=\"text-lg font-medium\">Battle for Glory</span>\r\n                                <TbStar className=\"text-yellow-400\" />\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Stats Bar */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, scale: 0.9 }}\r\n                        animate={{ opacity: 1, scale: 1 }}\r\n                        transition={{ delay: 0.3 }}\r\n                        className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto\"\r\n                    >\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\">\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbFlame className=\"text-orange-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    {currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'}\r\n                                </span>\r\n                            </div>\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbAward className=\"text-purple-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    {rankingData.length} Competitors\r\n                                </span>\r\n                            </div>\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbMedal className=\"text-yellow-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    Live Rankings\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </motion.div>\r\n                </motion.div>\r\n            </div>\r\n\r\n            {/* Main Content */}\r\n            <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\">\r\n                {rankingData.length === 0 ? (\r\n                    <motion.div\r\n                        initial={{ opacity: 0, scale: 0.9 }}\r\n                        animate={{ opacity: 1, scale: 1 }}\r\n                        className=\"text-center py-16 bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20\"\r\n                    >\r\n                        <motion.div\r\n                            animate={{ rotate: [0, 10, -10, 0] }}\r\n                            transition={{ duration: 2, repeat: Infinity }}\r\n                        >\r\n                            <TbMedal className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\r\n                        </motion.div>\r\n                        <h3 className=\"text-2xl font-bold text-white mb-2\">No Rankings Available</h3>\r\n                        <p className=\"text-white/80 mb-6 text-lg\">Complete some quizzes to join the leaderboard!</p>\r\n                        <motion.button\r\n                            whileHover={{ scale: 1.05 }}\r\n                            whileTap={{ scale: 0.95 }}\r\n                            onClick={handleRefresh}\r\n                            className=\"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg\"\r\n                        >\r\n                            <TbRefresh className=\"w-5 h-5 inline mr-2\" />\r\n                            Refresh Rankings\r\n                        </motion.button>\r\n                    </motion.div>\r\n                ) : (\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.4 }}\r\n                        className=\"bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6\"\r\n                    >\r\n                        <UserRankingList\r\n                            users={rankingData}\r\n                            currentUserId={user?._id || null}\r\n                            layout=\"horizontal\"\r\n                            size=\"medium\"\r\n                            showStats={true}\r\n                            className=\"space-y-4\"\r\n                            currentUserRef={currentUserRef}\r\n                            showFindMe={showFindMe}\r\n                        />\r\n                    </motion.div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC7H,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,OAAO,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,SAAS,GAAGnB,WAAW,CAAEoB,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAM;IAAEC;EAAK,CAAC,GAAGH,SAAS;EAC1B,MAAM,CAACI,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMqC,cAAc,GAAGpC,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMqC,gBAAgB,GAAG,MAAAA,CAAOC,kBAAkB,GAAG,KAAK,KAAK;IAC3D,IAAI;MACA,IAAIA,kBAAkB,EAAE;QACpBP,aAAa,CAAC,IAAI,CAAC;QACnBhB,OAAO,CAACW,OAAO,CAAC,wBAAwB,EAAE,CAAC,CAAC;MAChD;;MAEA;MACA,IAAIa,QAAQ;MACZ,IAAI;QACA;QACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;;QAEtC;QACA,MAAMC,UAAU,GAAG,MAAMC,KAAK,CAAE,yCAAwCJ,SAAU,EAAC,EAAE;UACjFK,OAAO,EAAE;YACL,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE,EAAC;YAC1D,eAAe,EAAE;UACrB;QACJ,CAAC,CAAC;QACF,MAAMC,MAAM,GAAG,MAAML,UAAU,CAACM,IAAI,CAAC,CAAC;QAEtC,IAAID,MAAM,CAACE,OAAO,EAAE;UAChBX,QAAQ,GAAGS,MAAM;UACjBG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QAC7C,CAAC,MAAM;UACH,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;QACzE;MACJ,CAAC,CAAC,OAAOC,OAAO,EAAE;QACdH,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEE,OAAO,CAAC;QAC3E,IAAI;UACA,MAAMC,gBAAgB,GAAG,MAAMX,KAAK,CAAE,+CAA8CJ,SAAU,EAAC,EAAE;YAC7FK,OAAO,EAAE;cACL,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE,EAAC;cAC1D,eAAe,EAAE;YACrB;UACJ,CAAC,CAAC;UACF,MAAMS,YAAY,GAAG,MAAMD,gBAAgB,CAACN,IAAI,CAAC,CAAC;UAElD,IAAIO,YAAY,CAACN,OAAO,EAAE;YACtBX,QAAQ,GAAGiB,YAAY;YACvBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UAC7C,CAAC,MAAM;YACH,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAClD;QACJ,CAAC,CAAC,OAAOI,aAAa,EAAE;UACpBN,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEK,aAAa,CAAC;UAC9DlB,QAAQ,GAAG,MAAM1B,uBAAuB,CAAC,CAAC;QAC9C;MACJ;MAEA,IAAI0B,QAAQ,CAACW,OAAO,EAAE;QAClB;QACA,MAAMQ,eAAe,GAAGnB,QAAQ,CAACoB,IAAI,CAACC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,MAAM;UAC5DC,MAAM,EAAEF,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,GAAG;UACvCA,GAAG,EAAEH,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,GAAG;UACpCC,IAAI,EAAEJ,QAAQ,CAACK,QAAQ,IAAIL,QAAQ,CAACI,IAAI;UACxCE,cAAc,EAAEN,QAAQ,CAACO,SAAS,IAAIP,QAAQ,CAACQ,YAAY;UAC3DC,MAAM,EAAET,QAAQ,CAACU,UAAU,IAAIV,QAAQ,CAACS,MAAM;UAC9CE,KAAK,EAAEX,QAAQ,CAACY,SAAS,IAAIZ,QAAQ,CAACW,KAAK;UAC3CE,KAAK,EAAEb,QAAQ,CAACc,SAAS,IAAId,QAAQ,CAACa,KAAK;UAE3C;UACAE,WAAW,EAAEf,QAAQ,CAACgB,iBAAiB,IAAIhB,QAAQ,CAACe,WAAW,IAAI,CAAC;UACpEE,YAAY,EAAEjB,QAAQ,CAACkB,iBAAiB,IAAIlB,QAAQ,CAACiB,YAAY,IAAI,CAAC;UACtEE,gBAAgB,EAAEnB,QAAQ,CAACmB,gBAAgB,IAAI,CAAC;UAChDC,UAAU,EAAEpB,QAAQ,CAACoB,UAAU,IAAI,CAAC;UACpCC,UAAU,EAAErB,QAAQ,CAACqB,UAAU,IAAI,CAAC;UAEpC;UACAC,OAAO,EAAEtB,QAAQ,CAACsB,OAAO,IAAI,CAAC;UAC9BC,YAAY,EAAEvB,QAAQ,CAACuB,YAAY,IAAI,CAAC;UACxCC,aAAa,EAAExB,QAAQ,CAACwB,aAAa,IAAI,CAAC;UAC1CC,QAAQ,EAAEzB,QAAQ,CAACyB,QAAQ,IAAI,CAAC;UAChCC,UAAU,EAAE1B,QAAQ,CAAC0B,UAAU,IAAI,CAAC;UAEpC;UACAC,YAAY,EAAE3B,QAAQ,CAAC2B,YAAY,IAAI,CAAC;UACxCC,UAAU,EAAE5B,QAAQ,CAAC4B,UAAU,IAAI,CAAC;UACpCC,aAAa,EAAE7B,QAAQ,CAAC6B,aAAa,IAAI,CAAC;UAC1CC,YAAY,EAAE9B,QAAQ,CAAC8B,YAAY,IAAI,EAAE;UAEzC;UACAC,YAAY,EAAE/B,QAAQ,CAAC+B,YAAY,IAAI/B,QAAQ,CAACgC,oBAAoB,IAAIhC,QAAQ,CAACsB,OAAO,IAAItB,QAAQ,CAACe,WAAW,IAAI,CAAC;UACrHkB,IAAI,EAAEjC,QAAQ,CAACiC,IAAI,IAAIhC,KAAK,GAAG,CAAC;UAChCiC,kBAAkB,EAAElC,QAAQ,CAACkC,kBAAkB,IAAI,MAAM;UAEzD;UACAC,SAAS,EAAEnC,QAAQ,CAACmC,SAAS,IAAI;QACrC,CAAC,CAAC,CAAC;QAEHvE,cAAc,CAACiC,eAAe,CAAC;QAC/B7B,QAAQ,CAAC,IAAI,CAAC;;QAEd;QACA,MAAMoE,QAAQ,GAAGvC,eAAe,CAACwC,SAAS,CAACC,IAAI,IAC3CA,IAAI,CAACnC,GAAG,MAAKzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,GAAG,KAAImC,IAAI,CAACpC,MAAM,MAAKxC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,GAAG,CACvD,CAAC;QACD/B,kBAAkB,CAACgE,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;QAEvD,IAAI3D,kBAAkB,EAAE;UACpBvB,OAAO,CAACmC,OAAO,CAAC,gCAAgC,CAAC;QACrD;MACJ,CAAC,MAAM;QACHrB,QAAQ,CAACU,QAAQ,CAACxB,OAAO,IAAI,8BAA8B,CAAC;QAC5DA,OAAO,CAACa,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACJ,CAAC,CAAC,OAAOwE,GAAG,EAAE;MACVjD,OAAO,CAACvB,KAAK,CAAC,sBAAsB,EAAEwE,GAAG,CAAC;MAC1CvE,QAAQ,CAACuE,GAAG,CAACrF,OAAO,IAAI,2CAA2C,CAAC;MACpEA,OAAO,CAACa,KAAK,CAAC,sCAAsC,CAAC;IACzD,CAAC,SAAS;MACND,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAEDjC,SAAS,CAAC,MAAM;IACZuC,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgE,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIjE,cAAc,CAACkE,OAAO,EAAE;MACxBlE,cAAc,CAACkE,OAAO,CAACC,cAAc,CAAC;QAClCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACFtE,aAAa,CAAC,IAAI,CAAC;MACnBuE,UAAU,CAAC,MAAMvE,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAClD;EACJ,CAAC;;EAED,MAAMwE,aAAa,GAAGA,CAAA,KAAM;IACxBtE,gBAAgB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAIX,OAAO,EAAE;IACT,oBACIT,OAAA;MAAK2F,SAAS,EAAC,2FAA2F;MAAAC,QAAA,eACtG5F,OAAA,CAACf,MAAM,CAAC4G,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAE;QACpCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCL,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAEzD5F,OAAA,CAACf,MAAM,CAAC4G,GAAG;UACPI,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DZ,SAAS,EAAC;QAAmF;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACF3G,OAAA;UAAG2F,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAmB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEd;EAEA,IAAIhG,KAAK,EAAE;IACP,oBACIX,OAAA;MAAK2F,SAAS,EAAC,+FAA+F;MAAAC,QAAA,eAC1G5F,OAAA,CAACf,MAAM,CAAC4G,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAG,CAAE;QAC/BX,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAE,CAAE;QAC9BjB,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAEzE5F,OAAA,CAACV,aAAa;UAACqG,SAAS,EAAC;QAAqC;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjE3G,OAAA;UAAI2F,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF3G,OAAA;UAAG2F,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEjF;QAAK;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7C3G,OAAA,CAACf,MAAM,CAAC4H,MAAM;UACVC,UAAU,EAAE;YAAEd,KAAK,EAAE;UAAK,CAAE;UAC5Be,QAAQ,EAAE;YAAEf,KAAK,EAAE;UAAK,CAAE;UAC1BgB,OAAO,EAAEtB,aAAc;UACvBC,SAAS,EAAC,8IAA8I;UAAAC,QAAA,gBAExJ5F,OAAA,CAACX,SAAS;YAACsG,SAAS,EAAC;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjC3G,OAAA;YAAA4F,QAAA,EAAM;UAAS;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEd;EAEA,oBACI3G,OAAA;IAAK2F,SAAS,EAAC,oGAAoG;IAAAC,QAAA,gBAE/G5F,OAAA;MAAK2F,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC7C5F,OAAA;QAAK2F,SAAS,EAAC;MAA2H;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjJ3G,OAAA;QAAK2F,SAAS,EAAC;MAAgJ;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtK3G,OAAA;QAAK2F,SAAS,EAAC;MAA2I;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChK,CAAC,eAEN3G,OAAA;MAAK2F,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAE1B5F,OAAA,CAACf,MAAM,CAAC4G,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCX,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAE,CAAE;QAC9BjB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEjD5F,OAAA,CAACf,MAAM,CAAC4H,MAAM;UACVC,UAAU,EAAE;YAAEd,KAAK,EAAE,IAAI;YAAEiB,CAAC,EAAE,CAAC;UAAE,CAAE;UACnCF,QAAQ,EAAE;YAAEf,KAAK,EAAE;UAAK,CAAE;UAC1BgB,OAAO,EAAEA,CAAA,KAAME,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCzB,SAAS,EAAC,2KAA2K;UAAAC,QAAA,gBAErL5F,OAAA,CAACT,WAAW;YAACoG,SAAS,EAAC;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnC3G,OAAA;YAAA4F,QAAA,EAAM;UAAI;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGf5F,eAAe,iBACZf,OAAA,CAACf,MAAM,CAAC4H,MAAM;UACVC,UAAU,EAAE;YAAEd,KAAK,EAAE;UAAK,CAAE;UAC5Be,QAAQ,EAAE;YAAEf,KAAK,EAAE;UAAK,CAAE;UAC1BgB,OAAO,EAAE5B,YAAa;UACtBO,SAAS,EAAC,sMAAsM;UAAAC,QAAA,gBAEhN5F,OAAA,CAACR,QAAQ;YAACmG,SAAS,EAAC;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChC3G,OAAA;YAAA4F,QAAA,EAAM;UAAO;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAClB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGb3G,OAAA,CAACf,MAAM,CAAC4G,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCX,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAE,CAAE;QAC9BjB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBAEjC5F,OAAA;UAAK2F,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBACxD5F,OAAA,CAACf,MAAM,CAAC4G,GAAG;YACPI,OAAO,EAAE;cACLC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;cACvBF,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;YACrB,CAAE;YACFG,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEe,WAAW,EAAE;YAAE,CAAE;YAC9D1B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAEpB5F,OAAA,CAACb,QAAQ;cAACwG,SAAS,EAAC;YAAyC;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChE3G,OAAA,CAACf,MAAM,CAAC4G,GAAG;cACPI,OAAO,EAAE;gBAAED,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;cAAE,CAAE;cAChCG,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEC,MAAM,EAAEC;cAAS,CAAE;cAC9CX,SAAS,EAAC;YAA6D;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACb3G,OAAA;YAAA4F,QAAA,gBACI5F,OAAA;cAAI2F,SAAS,EAAC,oHAAoH;cAAAC,QAAA,EAAC;YAEnI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3G,OAAA;cAAK2F,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACjE5F,OAAA,CAACP,MAAM;gBAACkG,SAAS,EAAC;cAAiB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtC3G,OAAA;gBAAM2F,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7D3G,OAAA,CAACP,MAAM;gBAACkG,SAAS,EAAC;cAAiB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN3G,OAAA,CAACf,MAAM,CAAC4G,GAAG;UACPC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCG,UAAU,EAAE;YAAEmB,KAAK,EAAE;UAAI,CAAE;UAC3B3B,SAAS,EAAC,4FAA4F;UAAAC,QAAA,eAEtG5F,OAAA;YAAK2F,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAC9D5F,OAAA;cAAK2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD5F,OAAA,CAACN,OAAO;gBAACiG,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C3G,OAAA;gBAAM2F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACrC7E,eAAe,GAAI,eAAcA,eAAgB,EAAC,GAAG;cAAuB;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN3G,OAAA;cAAK2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD5F,OAAA,CAACL,OAAO;gBAACgG,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C3G,OAAA;gBAAM2F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GACrCrF,WAAW,CAACgH,MAAM,EAAC,cACxB;cAAA;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN3G,OAAA;cAAK2F,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD5F,OAAA,CAACZ,OAAO;gBAACuG,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C3G,OAAA;gBAAM2F,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAE3C;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAGN3G,OAAA;MAAK2F,SAAS,EAAC,2DAA2D;MAAAC,QAAA,EACrErF,WAAW,CAACgH,MAAM,KAAK,CAAC,gBACrBvH,OAAA,CAACf,MAAM,CAAC4G,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAE;QACpCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCL,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBAE7F5F,OAAA,CAACf,MAAM,CAAC4G,GAAG;UACPI,OAAO,EAAE;YAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;UAAE,CAAE;UACrCC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC;UAAS,CAAE;UAAAV,QAAA,eAE9C5F,OAAA,CAACZ,OAAO;YAACuG,SAAS,EAAC;UAAwC;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACb3G,OAAA;UAAI2F,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAqB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E3G,OAAA;UAAG2F,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAA8C;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5F3G,OAAA,CAACf,MAAM,CAAC4H,MAAM;UACVC,UAAU,EAAE;YAAEd,KAAK,EAAE;UAAK,CAAE;UAC5Be,QAAQ,EAAE;YAAEf,KAAK,EAAE;UAAK,CAAE;UAC1BgB,OAAO,EAAEtB,aAAc;UACvBC,SAAS,EAAC,sKAAsK;UAAAC,QAAA,gBAEhL5F,OAAA,CAACX,SAAS;YAACsG,SAAS,EAAC;UAAqB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,gBAEb3G,OAAA,CAACf,MAAM,CAAC4G,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAG,CAAE;QAC/BX,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAE,CAAE;QAC9BT,UAAU,EAAE;UAAEmB,KAAK,EAAE;QAAI,CAAE;QAC3B3B,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eAE9E5F,OAAA,CAACH,eAAe;UACZQ,KAAK,EAAEE,WAAY;UACnBiH,aAAa,EAAE,CAAAlH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyC,GAAG,KAAI,IAAK;UACjC0E,MAAM,EAAC,YAAY;UACnBC,IAAI,EAAC,QAAQ;UACbC,SAAS,EAAE,IAAK;UAChBhC,SAAS,EAAC,WAAW;UACrBxE,cAAc,EAAEA,cAAe;UAC/BF,UAAU,EAAEA;QAAW;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzG,EAAA,CAzVID,OAAO;EAAA,QACSjB,WAAW;AAAA;AAAA4I,EAAA,GAD3B3H,OAAO;AA2Vb,eAAeA,OAAO;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}