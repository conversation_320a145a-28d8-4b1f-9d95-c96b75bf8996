{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Ranking\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { useSelector } from \"react-redux\";\nimport { motion } from \"framer-motion\";\nimport { TbTrophy, TbMedal, TbRefresh, TbAlertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from \"react-icons/tb\";\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\nimport { message } from \"antd\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Ranking = () => {\n  _s();\n  const userState = useSelector(state => state.users || {});\n  const {\n    user\n  } = userState;\n  const [rankingData, setRankingData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [refreshing, setRefreshing] = useState(false);\n  const [currentUserRank, setCurrentUserRank] = useState(null);\n  const [showFindMe, setShowFindMe] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [autoRefresh, setAutoRefresh] = useState(false);\n  const currentUserRef = useRef(null);\n  const refreshIntervalRef = useRef(null);\n  const fetchRankingData = async (showRefreshMessage = false) => {\n    try {\n      if (showRefreshMessage) {\n        setRefreshing(true);\n        message.loading(\"Refreshing rankings...\", 1);\n      }\n\n      // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking\n      let response;\n\n      // Add cache-busting timestamp to ensure fresh data\n      const timestamp = new Date().getTime();\n      try {\n        // Try new XP-based leaderboard first\n        const xpResponse = await fetch(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Cache-Control': 'no-cache'\n          }\n        });\n        const xpData = await xpResponse.json();\n        if (xpData.success) {\n          response = xpData;\n          console.log('Using XP-based leaderboard');\n        } else {\n          throw new Error('XP leaderboard failed, trying enhanced leaderboard');\n        }\n      } catch (xpError) {\n        console.log('XP leaderboard failed, trying enhanced leaderboard:', xpError);\n        try {\n          const enhancedResponse = await fetch(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {\n            headers: {\n              'Authorization': `Bearer ${localStorage.getItem('token')}`,\n              'Cache-Control': 'no-cache'\n            }\n          });\n          const enhancedData = await enhancedResponse.json();\n          if (enhancedData.success) {\n            response = enhancedData;\n            console.log('Using enhanced leaderboard');\n          } else {\n            throw new Error('Enhanced leaderboard failed');\n          }\n        } catch (enhancedError) {\n          console.log('Falling back to regular ranking:', enhancedError);\n          response = await getAllReportsForRanking();\n        }\n      }\n      if (response.success) {\n        // Transform data to match UserRankingCard expectations with enhanced XP system support\n        const transformedData = response.data.map((userData, index) => ({\n          // User identification - handle both old and new API formats\n          userId: userData.userId || userData._id,\n          _id: userData.userId || userData._id,\n          name: userData.userName || userData.name || 'Unknown User',\n          profilePicture: userData.userPhoto || userData.profileImage,\n          profileImage: userData.profileImage || userData.userPhoto,\n          school: userData.userSchool || userData.school || 'Unknown School',\n          class: userData.userClass || userData.class || 'Unknown',\n          level: userData.userLevel || userData.level || 'Primary',\n          email: userData.email,\n          // Legacy points system\n          totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,\n          totalPointsEarned: userData.totalPointsEarned || userData.totalPoints || 0,\n          quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\n          totalQuizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\n          passedExamsCount: userData.passedExamsCount || 0,\n          retryCount: userData.retryCount || 0,\n          scoreRatio: userData.scoreRatio || 0,\n          // XP System data (new)\n          totalXP: userData.totalXP || 0,\n          currentLevel: userData.currentLevel || 1,\n          xpToNextLevel: userData.xpToNextLevel || 0,\n          seasonXP: userData.seasonXP || 0,\n          lifetimeXP: userData.lifetimeXP || 0,\n          // Statistics\n          averageScore: userData.averageScore || 0,\n          bestStreak: userData.bestStreak || 0,\n          currentStreak: userData.currentStreak || 0,\n          achievements: userData.achievements || [],\n          achievementCount: userData.achievementCount || (userData.achievements ? userData.achievements.length : 0),\n          // Ranking data (prioritize ranking score from enhanced system)\n          rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,\n          rank: userData.rank || index + 1,\n          score: userData.rankingScore || userData.score || userData.totalXP || userData.totalPoints || 0,\n          // Subscription status (handle normalized status)\n          subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\n          normalizedSubscriptionStatus: userData.normalizedSubscriptionStatus || userData.subscriptionStatus || 'free',\n          subscriptionPlan: userData.subscriptionPlan,\n          subscriptionEndDate: userData.subscriptionEndDate,\n          // XP breakdown (if available from new system)\n          breakdown: userData.breakdown || null,\n          // Additional metadata\n          createdAt: userData.createdAt,\n          updatedAt: userData.updatedAt\n        }));\n        setRankingData(transformedData);\n        setError(null);\n        setLastUpdated(new Date());\n\n        // Find current user's rank\n        const userRank = transformedData.findIndex(item => item._id === (user === null || user === void 0 ? void 0 : user._id) || item.userId === (user === null || user === void 0 ? void 0 : user._id));\n        setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\n        if (showRefreshMessage) {\n          message.success(\"Rankings updated successfully!\");\n        }\n      } else {\n        setError(response.message || \"Failed to fetch ranking data\");\n        message.error(\"Failed to load rankings\");\n      }\n    } catch (err) {\n      console.error('Ranking fetch error:', err);\n      setError(err.message || \"An error occurred while fetching rankings\");\n      message.error(\"Network error while loading rankings\");\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n  useEffect(() => {\n    fetchRankingData();\n  }, []);\n\n  // Auto-refresh functionality\n  useEffect(() => {\n    if (autoRefresh) {\n      refreshIntervalRef.current = setInterval(() => {\n        fetchRankingData(false); // Silent refresh\n      }, 30000); // Refresh every 30 seconds\n    } else {\n      if (refreshIntervalRef.current) {\n        clearInterval(refreshIntervalRef.current);\n        refreshIntervalRef.current = null;\n      }\n    }\n\n    // Cleanup on unmount\n    return () => {\n      if (refreshIntervalRef.current) {\n        clearInterval(refreshIntervalRef.current);\n      }\n    };\n  }, [autoRefresh]);\n\n  // Find Me functionality\n  const handleFindMe = () => {\n    if (currentUserRef.current) {\n      currentUserRef.current.scrollIntoView({\n        behavior: 'smooth',\n        block: 'center'\n      });\n      setShowFindMe(true);\n      setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds\n    }\n  };\n\n  const handleRefresh = () => {\n    fetchRankingData(true);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        className: \"text-center bg-white rounded-xl p-8 shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: 360\n          },\n          transition: {\n            duration: 1,\n            repeat: Infinity,\n            ease: \"linear\"\n          },\n          className: \"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading rankings...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full\",\n        children: [/*#__PURE__*/_jsxDEV(TbAlertCircle, {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-semibold text-gray-900 mb-2\",\n          children: \"Error Loading Rankings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-6\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: handleRefresh,\n          className: \"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 mx-auto\",\n          children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Try Again\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -top-40 -right-40 w-80 h-80 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-40 left-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"p-4 flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05,\n            x: -5\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => window.history.back(),\n          className: \"bg-white/10 backdrop-blur-lg hover:bg-white/20 text-white px-4 py-2 rounded-xl font-medium transition-all duration-200 flex items-center space-x-2 border border-white/20\",\n          children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 21\n        }, this), currentUserRank && /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: handleFindMe,\n          className: \"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-2 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(TbTarget, {\n            className: \"w-5 h-5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"Find Me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        className: \"text-center mb-8 px-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center gap-4 mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: [0, 10, -10, 0],\n              scale: [1, 1.1, 1]\n            },\n            transition: {\n              duration: 3,\n              repeat: Infinity,\n              repeatDelay: 2\n            },\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(TbTrophy, {\n              className: \"text-6xl text-yellow-400 drop-shadow-lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              animate: {\n                scale: [1, 1.2, 1]\n              },\n              transition: {\n                duration: 2,\n                repeat: Infinity\n              },\n              className: \"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2\",\n              children: \"\\uD83C\\uDFC6 LEADERBOARD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2 text-white/80\",\n              children: [/*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-lg font-medium\",\n                children: \"Battle for Glory\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(TbStar, {\n                className: \"text-yellow-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            scale: 0.9\n          },\n          animate: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            delay: 0.3\n          },\n          className: \"bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n                className: \"text-orange-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbAward, {\n                className: \"text-purple-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: [rankingData.length, \" Competitors\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(TbMedal, {\n                className: \"text-yellow-400 text-xl\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-semibold\",\n                children: \"Live Rankings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\",\n      children: rankingData.length === 0 ? /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        className: \"text-center py-16 bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          animate: {\n            rotate: [0, 10, -10, 0]\n          },\n          transition: {\n            duration: 2,\n            repeat: Infinity\n          },\n          children: /*#__PURE__*/_jsxDEV(TbMedal, {\n            className: \"w-16 h-16 text-yellow-400 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-white mb-2\",\n          children: \"No Rankings Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white/80 mb-6 text-lg\",\n          children: \"Complete some quizzes to join the leaderboard!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: handleRefresh,\n          className: \"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(TbRefresh, {\n            className: \"w-5 h-5 inline mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 29\n          }, this), \"Refresh Rankings\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: 0.4\n        },\n        className: \"bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6\",\n        children: /*#__PURE__*/_jsxDEV(UserRankingList, {\n          users: rankingData,\n          currentUserId: (user === null || user === void 0 ? void 0 : user._id) || null,\n          layout: \"horizontal\",\n          size: \"medium\",\n          showStats: true,\n          className: \"space-y-4\",\n          currentUserRef: currentUserRef,\n          showFindMe: showFindMe,\n          onRefresh: handleRefresh,\n          lastUpdated: lastUpdated,\n          autoRefresh: autoRefresh,\n          onAutoRefreshToggle: () => setAutoRefresh(!autoRefresh)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 9\n  }, this);\n};\n_s(Ranking, \"G3f5xZWFGrBPLhNIop2S545k6TE=\", false, function () {\n  return [useSelector];\n});\n_c = Ranking;\nexport default Ranking;\nvar _c;\n$RefreshReg$(_c, \"Ranking\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useSelector", "motion", "TbTrophy", "TbMedal", "TbRefresh", "TbAlertCircle", "TbArrowLeft", "TbTarget", "TbStar", "TbFlame", "TbAward", "getAllReportsForRanking", "UserRankingList", "message", "jsxDEV", "_jsxDEV", "Ranking", "_s", "userState", "state", "users", "user", "rankingData", "setRankingData", "loading", "setLoading", "error", "setError", "refreshing", "setRefreshing", "currentUserRank", "setCurrentUserRank", "showFindMe", "setShowFindMe", "lastUpdated", "setLastUpdated", "autoRefresh", "setAutoRefresh", "currentUserRef", "refreshIntervalRef", "fetchRankingData", "showRefreshMessage", "response", "timestamp", "Date", "getTime", "xpResponse", "fetch", "headers", "localStorage", "getItem", "xpData", "json", "success", "console", "log", "Error", "xpError", "enhancedResponse", "enhancedData", "enhancedError", "transformedData", "data", "map", "userData", "index", "userId", "_id", "name", "userName", "profilePicture", "userPhoto", "profileImage", "school", "userSchool", "class", "userClass", "level", "userLevel", "email", "totalPoints", "totalPointsEarned", "quizzesTaken", "totalQuizzesTaken", "passedExamsCount", "retryCount", "scoreRatio", "totalXP", "currentLevel", "xpToNextLevel", "seasonXP", "lifetimeXP", "averageScore", "bestStreak", "currentStreak", "achievements", "achievementCount", "length", "rankingScore", "enhancedRankingScore", "rank", "score", "subscriptionStatus", "normalizedSubscriptionStatus", "subscriptionPlan", "subscriptionEndDate", "breakdown", "createdAt", "updatedAt", "userRank", "findIndex", "item", "err", "current", "setInterval", "clearInterval", "handleFindMe", "scrollIntoView", "behavior", "block", "setTimeout", "handleRefresh", "className", "children", "div", "initial", "opacity", "scale", "animate", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "y", "button", "whileHover", "whileTap", "onClick", "x", "window", "history", "back", "repeatDelay", "delay", "currentUserId", "layout", "size", "showStats", "onRefresh", "onAutoRefreshToggle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Ranking/index.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Tb<PERSON>rophy, TbMedal, TbRefresh, TbAlertCircle, TbArrowLeft, TbTarget, TbStar, TbFlame, TbAward } from \"react-icons/tb\";\r\nimport { getAllReportsForRanking } from \"../../../apicalls/reports\";\r\nimport UserRankingList from \"../../../components/modern/UserRankingList\";\r\nimport { message } from \"antd\";\r\n\r\nconst Ranking = () => {\r\n    const userState = useSelector((state) => state.users || {});\r\n    const { user } = userState;\r\n    const [rankingData, setRankingData] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    const [refreshing, setRefreshing] = useState(false);\r\n    const [currentUserRank, setCurrentUserRank] = useState(null);\r\n    const [showFindMe, setShowFindMe] = useState(false);\r\n    const [lastUpdated, setLastUpdated] = useState(null);\r\n    const [autoRefresh, setAutoRefresh] = useState(false);\r\n    const currentUserRef = useRef(null);\r\n    const refreshIntervalRef = useRef(null);\r\n\r\n    const fetchRankingData = async (showRefreshMessage = false) => {\r\n        try {\r\n            if (showRefreshMessage) {\r\n                setRefreshing(true);\r\n                message.loading(\"Refreshing rankings...\", 1);\r\n            }\r\n\r\n            // Try XP leaderboard first, then enhanced leaderboard, fallback to regular ranking\r\n            let response;\r\n\r\n            // Add cache-busting timestamp to ensure fresh data\r\n            const timestamp = new Date().getTime();\r\n\r\n            try {\r\n                // Try new XP-based leaderboard first\r\n                const xpResponse = await fetch(`/api/quiz/xp-leaderboard?limit=1000&t=${timestamp}`, {\r\n                    headers: {\r\n                        'Authorization': `Bearer ${localStorage.getItem('token')}`,\r\n                        'Cache-Control': 'no-cache'\r\n                    }\r\n                });\r\n                const xpData = await xpResponse.json();\r\n\r\n                if (xpData.success) {\r\n                    response = xpData;\r\n                    console.log('Using XP-based leaderboard');\r\n                } else {\r\n                    throw new Error('XP leaderboard failed, trying enhanced leaderboard');\r\n                }\r\n            } catch (xpError) {\r\n                console.log('XP leaderboard failed, trying enhanced leaderboard:', xpError);\r\n                try {\r\n                    const enhancedResponse = await fetch(`/api/quiz/enhanced-leaderboard?limit=1000&t=${timestamp}`, {\r\n                        headers: {\r\n                            'Authorization': `Bearer ${localStorage.getItem('token')}`,\r\n                            'Cache-Control': 'no-cache'\r\n                        }\r\n                    });\r\n                    const enhancedData = await enhancedResponse.json();\r\n\r\n                    if (enhancedData.success) {\r\n                        response = enhancedData;\r\n                        console.log('Using enhanced leaderboard');\r\n                    } else {\r\n                        throw new Error('Enhanced leaderboard failed');\r\n                    }\r\n                } catch (enhancedError) {\r\n                    console.log('Falling back to regular ranking:', enhancedError);\r\n                    response = await getAllReportsForRanking();\r\n                }\r\n            }\r\n\r\n            if (response.success) {\r\n                // Transform data to match UserRankingCard expectations with enhanced XP system support\r\n                const transformedData = response.data.map((userData, index) => ({\r\n                    // User identification - handle both old and new API formats\r\n                    userId: userData.userId || userData._id,\r\n                    _id: userData.userId || userData._id,\r\n                    name: userData.userName || userData.name || 'Unknown User',\r\n                    profilePicture: userData.userPhoto || userData.profileImage,\r\n                    profileImage: userData.profileImage || userData.userPhoto,\r\n                    school: userData.userSchool || userData.school || 'Unknown School',\r\n                    class: userData.userClass || userData.class || 'Unknown',\r\n                    level: userData.userLevel || userData.level || 'Primary',\r\n                    email: userData.email,\r\n\r\n                    // Legacy points system\r\n                    totalPoints: userData.totalPointsEarned || userData.totalPoints || 0,\r\n                    totalPointsEarned: userData.totalPointsEarned || userData.totalPoints || 0,\r\n                    quizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\r\n                    totalQuizzesTaken: userData.totalQuizzesTaken || userData.quizzesTaken || 0,\r\n                    passedExamsCount: userData.passedExamsCount || 0,\r\n                    retryCount: userData.retryCount || 0,\r\n                    scoreRatio: userData.scoreRatio || 0,\r\n\r\n                    // XP System data (new)\r\n                    totalXP: userData.totalXP || 0,\r\n                    currentLevel: userData.currentLevel || 1,\r\n                    xpToNextLevel: userData.xpToNextLevel || 0,\r\n                    seasonXP: userData.seasonXP || 0,\r\n                    lifetimeXP: userData.lifetimeXP || 0,\r\n\r\n                    // Statistics\r\n                    averageScore: userData.averageScore || 0,\r\n                    bestStreak: userData.bestStreak || 0,\r\n                    currentStreak: userData.currentStreak || 0,\r\n                    achievements: userData.achievements || [],\r\n                    achievementCount: userData.achievementCount || (userData.achievements ? userData.achievements.length : 0),\r\n\r\n                    // Ranking data (prioritize ranking score from enhanced system)\r\n                    rankingScore: userData.rankingScore || userData.enhancedRankingScore || userData.totalXP || userData.totalPoints || 0,\r\n                    rank: userData.rank || index + 1,\r\n                    score: userData.rankingScore || userData.score || userData.totalXP || userData.totalPoints || 0,\r\n\r\n                    // Subscription status (handle normalized status)\r\n                    subscriptionStatus: userData.subscriptionStatus || userData.normalizedSubscriptionStatus || 'free',\r\n                    normalizedSubscriptionStatus: userData.normalizedSubscriptionStatus || userData.subscriptionStatus || 'free',\r\n                    subscriptionPlan: userData.subscriptionPlan,\r\n                    subscriptionEndDate: userData.subscriptionEndDate,\r\n\r\n                    // XP breakdown (if available from new system)\r\n                    breakdown: userData.breakdown || null,\r\n\r\n                    // Additional metadata\r\n                    createdAt: userData.createdAt,\r\n                    updatedAt: userData.updatedAt\r\n                }));\r\n\r\n                setRankingData(transformedData);\r\n                setError(null);\r\n                setLastUpdated(new Date());\r\n\r\n                // Find current user's rank\r\n                const userRank = transformedData.findIndex(item =>\r\n                    item._id === user?._id || item.userId === user?._id\r\n                );\r\n                setCurrentUserRank(userRank >= 0 ? userRank + 1 : null);\r\n\r\n                if (showRefreshMessage) {\r\n                    message.success(\"Rankings updated successfully!\");\r\n                }\r\n            } else {\r\n                setError(response.message || \"Failed to fetch ranking data\");\r\n                message.error(\"Failed to load rankings\");\r\n            }\r\n        } catch (err) {\r\n            console.error('Ranking fetch error:', err);\r\n            setError(err.message || \"An error occurred while fetching rankings\");\r\n            message.error(\"Network error while loading rankings\");\r\n        } finally {\r\n            setLoading(false);\r\n            setRefreshing(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchRankingData();\r\n    }, []);\r\n\r\n    // Auto-refresh functionality\r\n    useEffect(() => {\r\n        if (autoRefresh) {\r\n            refreshIntervalRef.current = setInterval(() => {\r\n                fetchRankingData(false); // Silent refresh\r\n            }, 30000); // Refresh every 30 seconds\r\n        } else {\r\n            if (refreshIntervalRef.current) {\r\n                clearInterval(refreshIntervalRef.current);\r\n                refreshIntervalRef.current = null;\r\n            }\r\n        }\r\n\r\n        // Cleanup on unmount\r\n        return () => {\r\n            if (refreshIntervalRef.current) {\r\n                clearInterval(refreshIntervalRef.current);\r\n            }\r\n        };\r\n    }, [autoRefresh]);\r\n\r\n    // Find Me functionality\r\n    const handleFindMe = () => {\r\n        if (currentUserRef.current) {\r\n            currentUserRef.current.scrollIntoView({\r\n                behavior: 'smooth',\r\n                block: 'center'\r\n            });\r\n            setShowFindMe(true);\r\n            setTimeout(() => setShowFindMe(false), 3000); // Hide highlight after 3 seconds\r\n        }\r\n    };\r\n\r\n    const handleRefresh = () => {\r\n        fetchRankingData(true);\r\n    };\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center\">\r\n                <motion.div\r\n                    initial={{ opacity: 0, scale: 0.9 }}\r\n                    animate={{ opacity: 1, scale: 1 }}\r\n                    className=\"text-center bg-white rounded-xl p-8 shadow-lg\"\r\n                >\r\n                    <motion.div\r\n                        animate={{ rotate: 360 }}\r\n                        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\r\n                        className=\"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4\"\r\n                    />\r\n                    <p className=\"text-gray-600 font-medium\">Loading rankings...</p>\r\n                </motion.div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4\">\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: 20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"text-center bg-white rounded-xl p-8 shadow-lg max-w-md w-full\"\r\n                >\r\n                    <TbAlertCircle className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\r\n                    <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Error Loading Rankings</h2>\r\n                    <p className=\"text-gray-600 mb-6\">{error}</p>\r\n                    <motion.button\r\n                        whileHover={{ scale: 1.05 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                        onClick={handleRefresh}\r\n                        className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 mx-auto\"\r\n                    >\r\n                        <TbRefresh className=\"w-5 h-5\" />\r\n                        <span>Try Again</span>\r\n                    </motion.button>\r\n                </motion.div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 relative overflow-hidden\">\r\n            {/* Animated Background Elements */}\r\n            <div className=\"absolute inset-0 overflow-hidden\">\r\n                <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob\"></div>\r\n                <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000\"></div>\r\n                <div className=\"absolute top-40 left-40 w-80 h-80 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000\"></div>\r\n            </div>\r\n\r\n            <div className=\"relative z-10\">\r\n                {/* Modern Header with Back Button */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: -20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"p-4 flex items-center justify-between\"\r\n                >\r\n                    <motion.button\r\n                        whileHover={{ scale: 1.05, x: -5 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                        onClick={() => window.history.back()}\r\n                        className=\"bg-white/10 backdrop-blur-lg hover:bg-white/20 text-white px-4 py-2 rounded-xl font-medium transition-all duration-200 flex items-center space-x-2 border border-white/20\"\r\n                    >\r\n                        <TbArrowLeft className=\"w-5 h-5\" />\r\n                        <span>Back</span>\r\n                    </motion.button>\r\n\r\n                    {/* Find Me Button */}\r\n                    {currentUserRank && (\r\n                        <motion.button\r\n                            whileHover={{ scale: 1.05 }}\r\n                            whileTap={{ scale: 0.95 }}\r\n                            onClick={handleFindMe}\r\n                            className=\"bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white px-6 py-2 rounded-xl font-bold transition-all duration-200 flex items-center space-x-2 shadow-lg\"\r\n                        >\r\n                            <TbTarget className=\"w-5 h-5\" />\r\n                            <span>Find Me</span>\r\n                        </motion.button>\r\n                    )}\r\n                </motion.div>\r\n\r\n                {/* Amazing Header */}\r\n                <motion.div\r\n                    initial={{ opacity: 0, y: -20 }}\r\n                    animate={{ opacity: 1, y: 0 }}\r\n                    className=\"text-center mb-8 px-4\"\r\n                >\r\n                    <div className=\"flex items-center justify-center gap-4 mb-6\">\r\n                        <motion.div\r\n                            animate={{\r\n                                rotate: [0, 10, -10, 0],\r\n                                scale: [1, 1.1, 1]\r\n                            }}\r\n                            transition={{ duration: 3, repeat: Infinity, repeatDelay: 2 }}\r\n                            className=\"relative\"\r\n                        >\r\n                            <TbTrophy className=\"text-6xl text-yellow-400 drop-shadow-lg\" />\r\n                            <motion.div\r\n                                animate={{ scale: [1, 1.2, 1] }}\r\n                                transition={{ duration: 2, repeat: Infinity }}\r\n                                className=\"absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full\"\r\n                            />\r\n                        </motion.div>\r\n                        <div>\r\n                            <h1 className=\"text-5xl font-black bg-gradient-to-r from-yellow-400 via-pink-400 to-purple-400 bg-clip-text text-transparent mb-2\">\r\n                                🏆 LEADERBOARD\r\n                            </h1>\r\n                            <div className=\"flex items-center justify-center gap-2 text-white/80\">\r\n                                <TbStar className=\"text-yellow-400\" />\r\n                                <span className=\"text-lg font-medium\">Battle for Glory</span>\r\n                                <TbStar className=\"text-yellow-400\" />\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Stats Bar */}\r\n                    <motion.div\r\n                        initial={{ opacity: 0, scale: 0.9 }}\r\n                        animate={{ opacity: 1, scale: 1 }}\r\n                        transition={{ delay: 0.3 }}\r\n                        className=\"bg-white/10 backdrop-blur-lg rounded-2xl p-4 mb-6 border border-white/20 max-w-4xl mx-auto\"\r\n                    >\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\">\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbFlame className=\"text-orange-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    {currentUserRank ? `Your Rank: #${currentUserRank}` : 'Join the Competition!'}\r\n                                </span>\r\n                            </div>\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbAward className=\"text-purple-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    {rankingData.length} Competitors\r\n                                </span>\r\n                            </div>\r\n                            <div className=\"flex items-center justify-center gap-2\">\r\n                                <TbMedal className=\"text-yellow-400 text-xl\" />\r\n                                <span className=\"text-white font-semibold\">\r\n                                    Live Rankings\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </motion.div>\r\n                </motion.div>\r\n            </div>\r\n\r\n            {/* Main Content */}\r\n            <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8\">\r\n                {rankingData.length === 0 ? (\r\n                    <motion.div\r\n                        initial={{ opacity: 0, scale: 0.9 }}\r\n                        animate={{ opacity: 1, scale: 1 }}\r\n                        className=\"text-center py-16 bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20\"\r\n                    >\r\n                        <motion.div\r\n                            animate={{ rotate: [0, 10, -10, 0] }}\r\n                            transition={{ duration: 2, repeat: Infinity }}\r\n                        >\r\n                            <TbMedal className=\"w-16 h-16 text-yellow-400 mx-auto mb-4\" />\r\n                        </motion.div>\r\n                        <h3 className=\"text-2xl font-bold text-white mb-2\">No Rankings Available</h3>\r\n                        <p className=\"text-white/80 mb-6 text-lg\">Complete some quizzes to join the leaderboard!</p>\r\n                        <motion.button\r\n                            whileHover={{ scale: 1.05 }}\r\n                            whileTap={{ scale: 0.95 }}\r\n                            onClick={handleRefresh}\r\n                            className=\"bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-xl font-bold transition-all duration-200 shadow-lg\"\r\n                        >\r\n                            <TbRefresh className=\"w-5 h-5 inline mr-2\" />\r\n                            Refresh Rankings\r\n                        </motion.button>\r\n                    </motion.div>\r\n                ) : (\r\n                    <motion.div\r\n                        initial={{ opacity: 0, y: 20 }}\r\n                        animate={{ opacity: 1, y: 0 }}\r\n                        transition={{ delay: 0.4 }}\r\n                        className=\"bg-white/5 backdrop-blur-lg rounded-2xl border border-white/10 p-6\"\r\n                    >\r\n                        <UserRankingList\r\n                            users={rankingData}\r\n                            currentUserId={user?._id || null}\r\n                            layout=\"horizontal\"\r\n                            size=\"medium\"\r\n                            showStats={true}\r\n                            className=\"space-y-4\"\r\n                            currentUserRef={currentUserRef}\r\n                            showFindMe={showFindMe}\r\n                            onRefresh={handleRefresh}\r\n                            lastUpdated={lastUpdated}\r\n                            autoRefresh={autoRefresh}\r\n                            onAutoRefreshToggle={() => setAutoRefresh(!autoRefresh)}\r\n                        />\r\n                    </motion.div>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Ranking;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,aAAa,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAQ,gBAAgB;AAC7H,SAASC,uBAAuB,QAAQ,2BAA2B;AACnE,OAAOC,eAAe,MAAM,4CAA4C;AACxE,SAASC,OAAO,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAMC,SAAS,GAAGlB,WAAW,CAAEmB,KAAK,IAAKA,KAAK,CAACC,KAAK,IAAI,CAAC,CAAC,CAAC;EAC3D,MAAM;IAAEC;EAAK,CAAC,GAAGH,SAAS;EAC1B,MAAM,CAACI,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkC,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMwC,cAAc,GAAGvC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMwC,kBAAkB,GAAGxC,MAAM,CAAC,IAAI,CAAC;EAEvC,MAAMyC,gBAAgB,GAAG,MAAAA,CAAOC,kBAAkB,GAAG,KAAK,KAAK;IAC3D,IAAI;MACA,IAAIA,kBAAkB,EAAE;QACpBZ,aAAa,CAAC,IAAI,CAAC;QACnBhB,OAAO,CAACW,OAAO,CAAC,wBAAwB,EAAE,CAAC,CAAC;MAChD;;MAEA;MACA,IAAIkB,QAAQ;;MAEZ;MACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAEtC,IAAI;QACA;QACA,MAAMC,UAAU,GAAG,MAAMC,KAAK,CAAE,yCAAwCJ,SAAU,EAAC,EAAE;UACjFK,OAAO,EAAE;YACL,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE,EAAC;YAC1D,eAAe,EAAE;UACrB;QACJ,CAAC,CAAC;QACF,MAAMC,MAAM,GAAG,MAAML,UAAU,CAACM,IAAI,CAAC,CAAC;QAEtC,IAAID,MAAM,CAACE,OAAO,EAAE;UAChBX,QAAQ,GAAGS,MAAM;UACjBG,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QAC7C,CAAC,MAAM;UACH,MAAM,IAAIC,KAAK,CAAC,oDAAoD,CAAC;QACzE;MACJ,CAAC,CAAC,OAAOC,OAAO,EAAE;QACdH,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEE,OAAO,CAAC;QAC3E,IAAI;UACA,MAAMC,gBAAgB,GAAG,MAAMX,KAAK,CAAE,+CAA8CJ,SAAU,EAAC,EAAE;YAC7FK,OAAO,EAAE;cACL,eAAe,EAAG,UAASC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAE,EAAC;cAC1D,eAAe,EAAE;YACrB;UACJ,CAAC,CAAC;UACF,MAAMS,YAAY,GAAG,MAAMD,gBAAgB,CAACN,IAAI,CAAC,CAAC;UAElD,IAAIO,YAAY,CAACN,OAAO,EAAE;YACtBX,QAAQ,GAAGiB,YAAY;YACvBL,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UAC7C,CAAC,MAAM;YACH,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;UAClD;QACJ,CAAC,CAAC,OAAOI,aAAa,EAAE;UACpBN,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEK,aAAa,CAAC;UAC9DlB,QAAQ,GAAG,MAAM/B,uBAAuB,CAAC,CAAC;QAC9C;MACJ;MAEA,IAAI+B,QAAQ,CAACW,OAAO,EAAE;QAClB;QACA,MAAMQ,eAAe,GAAGnB,QAAQ,CAACoB,IAAI,CAACC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,MAAM;UAC5D;UACAC,MAAM,EAAEF,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,GAAG;UACvCA,GAAG,EAAEH,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAACG,GAAG;UACpCC,IAAI,EAAEJ,QAAQ,CAACK,QAAQ,IAAIL,QAAQ,CAACI,IAAI,IAAI,cAAc;UAC1DE,cAAc,EAAEN,QAAQ,CAACO,SAAS,IAAIP,QAAQ,CAACQ,YAAY;UAC3DA,YAAY,EAAER,QAAQ,CAACQ,YAAY,IAAIR,QAAQ,CAACO,SAAS;UACzDE,MAAM,EAAET,QAAQ,CAACU,UAAU,IAAIV,QAAQ,CAACS,MAAM,IAAI,gBAAgB;UAClEE,KAAK,EAAEX,QAAQ,CAACY,SAAS,IAAIZ,QAAQ,CAACW,KAAK,IAAI,SAAS;UACxDE,KAAK,EAAEb,QAAQ,CAACc,SAAS,IAAId,QAAQ,CAACa,KAAK,IAAI,SAAS;UACxDE,KAAK,EAAEf,QAAQ,CAACe,KAAK;UAErB;UACAC,WAAW,EAAEhB,QAAQ,CAACiB,iBAAiB,IAAIjB,QAAQ,CAACgB,WAAW,IAAI,CAAC;UACpEC,iBAAiB,EAAEjB,QAAQ,CAACiB,iBAAiB,IAAIjB,QAAQ,CAACgB,WAAW,IAAI,CAAC;UAC1EE,YAAY,EAAElB,QAAQ,CAACmB,iBAAiB,IAAInB,QAAQ,CAACkB,YAAY,IAAI,CAAC;UACtEC,iBAAiB,EAAEnB,QAAQ,CAACmB,iBAAiB,IAAInB,QAAQ,CAACkB,YAAY,IAAI,CAAC;UAC3EE,gBAAgB,EAAEpB,QAAQ,CAACoB,gBAAgB,IAAI,CAAC;UAChDC,UAAU,EAAErB,QAAQ,CAACqB,UAAU,IAAI,CAAC;UACpCC,UAAU,EAAEtB,QAAQ,CAACsB,UAAU,IAAI,CAAC;UAEpC;UACAC,OAAO,EAAEvB,QAAQ,CAACuB,OAAO,IAAI,CAAC;UAC9BC,YAAY,EAAExB,QAAQ,CAACwB,YAAY,IAAI,CAAC;UACxCC,aAAa,EAAEzB,QAAQ,CAACyB,aAAa,IAAI,CAAC;UAC1CC,QAAQ,EAAE1B,QAAQ,CAAC0B,QAAQ,IAAI,CAAC;UAChCC,UAAU,EAAE3B,QAAQ,CAAC2B,UAAU,IAAI,CAAC;UAEpC;UACAC,YAAY,EAAE5B,QAAQ,CAAC4B,YAAY,IAAI,CAAC;UACxCC,UAAU,EAAE7B,QAAQ,CAAC6B,UAAU,IAAI,CAAC;UACpCC,aAAa,EAAE9B,QAAQ,CAAC8B,aAAa,IAAI,CAAC;UAC1CC,YAAY,EAAE/B,QAAQ,CAAC+B,YAAY,IAAI,EAAE;UACzCC,gBAAgB,EAAEhC,QAAQ,CAACgC,gBAAgB,KAAKhC,QAAQ,CAAC+B,YAAY,GAAG/B,QAAQ,CAAC+B,YAAY,CAACE,MAAM,GAAG,CAAC,CAAC;UAEzG;UACAC,YAAY,EAAElC,QAAQ,CAACkC,YAAY,IAAIlC,QAAQ,CAACmC,oBAAoB,IAAInC,QAAQ,CAACuB,OAAO,IAAIvB,QAAQ,CAACgB,WAAW,IAAI,CAAC;UACrHoB,IAAI,EAAEpC,QAAQ,CAACoC,IAAI,IAAInC,KAAK,GAAG,CAAC;UAChCoC,KAAK,EAAErC,QAAQ,CAACkC,YAAY,IAAIlC,QAAQ,CAACqC,KAAK,IAAIrC,QAAQ,CAACuB,OAAO,IAAIvB,QAAQ,CAACgB,WAAW,IAAI,CAAC;UAE/F;UACAsB,kBAAkB,EAAEtC,QAAQ,CAACsC,kBAAkB,IAAItC,QAAQ,CAACuC,4BAA4B,IAAI,MAAM;UAClGA,4BAA4B,EAAEvC,QAAQ,CAACuC,4BAA4B,IAAIvC,QAAQ,CAACsC,kBAAkB,IAAI,MAAM;UAC5GE,gBAAgB,EAAExC,QAAQ,CAACwC,gBAAgB;UAC3CC,mBAAmB,EAAEzC,QAAQ,CAACyC,mBAAmB;UAEjD;UACAC,SAAS,EAAE1C,QAAQ,CAAC0C,SAAS,IAAI,IAAI;UAErC;UACAC,SAAS,EAAE3C,QAAQ,CAAC2C,SAAS;UAC7BC,SAAS,EAAE5C,QAAQ,CAAC4C;QACxB,CAAC,CAAC,CAAC;QAEHrF,cAAc,CAACsC,eAAe,CAAC;QAC/BlC,QAAQ,CAAC,IAAI,CAAC;QACdQ,cAAc,CAAC,IAAIS,IAAI,CAAC,CAAC,CAAC;;QAE1B;QACA,MAAMiE,QAAQ,GAAGhD,eAAe,CAACiD,SAAS,CAACC,IAAI,IAC3CA,IAAI,CAAC5C,GAAG,MAAK9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,GAAG,KAAI4C,IAAI,CAAC7C,MAAM,MAAK7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,GAAG,CACvD,CAAC;QACDpC,kBAAkB,CAAC8E,QAAQ,IAAI,CAAC,GAAGA,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC;QAEvD,IAAIpE,kBAAkB,EAAE;UACpB5B,OAAO,CAACwC,OAAO,CAAC,gCAAgC,CAAC;QACrD;MACJ,CAAC,MAAM;QACH1B,QAAQ,CAACe,QAAQ,CAAC7B,OAAO,IAAI,8BAA8B,CAAC;QAC5DA,OAAO,CAACa,KAAK,CAAC,yBAAyB,CAAC;MAC5C;IACJ,CAAC,CAAC,OAAOsF,GAAG,EAAE;MACV1D,OAAO,CAAC5B,KAAK,CAAC,sBAAsB,EAAEsF,GAAG,CAAC;MAC1CrF,QAAQ,CAACqF,GAAG,CAACnG,OAAO,IAAI,2CAA2C,CAAC;MACpEA,OAAO,CAACa,KAAK,CAAC,sCAAsC,CAAC;IACzD,CAAC,SAAS;MACND,UAAU,CAAC,KAAK,CAAC;MACjBI,aAAa,CAAC,KAAK,CAAC;IACxB;EACJ,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACZ2C,gBAAgB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3C,SAAS,CAAC,MAAM;IACZ,IAAIuC,WAAW,EAAE;MACbG,kBAAkB,CAAC0E,OAAO,GAAGC,WAAW,CAAC,MAAM;QAC3C1E,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC7B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IACf,CAAC,MAAM;MACH,IAAID,kBAAkB,CAAC0E,OAAO,EAAE;QAC5BE,aAAa,CAAC5E,kBAAkB,CAAC0E,OAAO,CAAC;QACzC1E,kBAAkB,CAAC0E,OAAO,GAAG,IAAI;MACrC;IACJ;;IAEA;IACA,OAAO,MAAM;MACT,IAAI1E,kBAAkB,CAAC0E,OAAO,EAAE;QAC5BE,aAAa,CAAC5E,kBAAkB,CAAC0E,OAAO,CAAC;MAC7C;IACJ,CAAC;EACL,CAAC,EAAE,CAAC7E,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMgF,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAI9E,cAAc,CAAC2E,OAAO,EAAE;MACxB3E,cAAc,CAAC2E,OAAO,CAACI,cAAc,CAAC;QAClCC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE;MACX,CAAC,CAAC;MACFtF,aAAa,CAAC,IAAI,CAAC;MACnBuF,UAAU,CAAC,MAAMvF,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAClD;EACJ,CAAC;;EAED,MAAMwF,aAAa,GAAGA,CAAA,KAAM;IACxBjF,gBAAgB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,IAAIhB,OAAO,EAAE;IACT,oBACIT,OAAA;MAAK2G,SAAS,EAAC,2FAA2F;MAAAC,QAAA,eACtG5G,OAAA,CAACd,MAAM,CAAC2H,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAE;QACpCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCL,SAAS,EAAC,+CAA+C;QAAAC,QAAA,gBAEzD5G,OAAA,CAACd,MAAM,CAAC2H,GAAG;UACPI,OAAO,EAAE;YAAEC,MAAM,EAAE;UAAI,CAAE;UACzBC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC,QAAQ;YAAEC,IAAI,EAAE;UAAS,CAAE;UAC9DZ,SAAS,EAAC;QAAmF;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChG,CAAC,eACF3H,OAAA;UAAG2G,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAmB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEd;EAEA,IAAIhH,KAAK,EAAE;IACP,oBACIX,OAAA;MAAK2G,SAAS,EAAC,+FAA+F;MAAAC,QAAA,eAC1G5G,OAAA,CAACd,MAAM,CAAC2H,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAG,CAAE;QAC/BX,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAE,CAAE;QAC9BjB,SAAS,EAAC,+DAA+D;QAAAC,QAAA,gBAEzE5G,OAAA,CAACV,aAAa;UAACqH,SAAS,EAAC;QAAqC;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACjE3H,OAAA;UAAI2G,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAsB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF3H,OAAA;UAAG2G,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAEjG;QAAK;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7C3H,OAAA,CAACd,MAAM,CAAC2I,MAAM;UACVC,UAAU,EAAE;YAAEd,KAAK,EAAE;UAAK,CAAE;UAC5Be,QAAQ,EAAE;YAAEf,KAAK,EAAE;UAAK,CAAE;UAC1BgB,OAAO,EAAEtB,aAAc;UACvBC,SAAS,EAAC,8IAA8I;UAAAC,QAAA,gBAExJ5G,OAAA,CAACX,SAAS;YAACsH,SAAS,EAAC;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjC3H,OAAA;YAAA4G,QAAA,EAAM;UAAS;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAEd;EAEA,oBACI3H,OAAA;IAAK2G,SAAS,EAAC,oGAAoG;IAAAC,QAAA,gBAE/G5G,OAAA;MAAK2G,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC7C5G,OAAA;QAAK2G,SAAS,EAAC;MAA2H;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjJ3H,OAAA;QAAK2G,SAAS,EAAC;MAAgJ;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtK3H,OAAA;QAAK2G,SAAS,EAAC;MAA2I;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChK,CAAC,eAEN3H,OAAA;MAAK2G,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAE1B5G,OAAA,CAACd,MAAM,CAAC2H,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCX,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAE,CAAE;QAC9BjB,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEjD5G,OAAA,CAACd,MAAM,CAAC2I,MAAM;UACVC,UAAU,EAAE;YAAEd,KAAK,EAAE,IAAI;YAAEiB,CAAC,EAAE,CAAC;UAAE,CAAE;UACnCF,QAAQ,EAAE;YAAEf,KAAK,EAAE;UAAK,CAAE;UAC1BgB,OAAO,EAAEA,CAAA,KAAME,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE;UACrCzB,SAAS,EAAC,2KAA2K;UAAAC,QAAA,gBAErL5G,OAAA,CAACT,WAAW;YAACoH,SAAS,EAAC;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnC3H,OAAA;YAAA4G,QAAA,EAAM;UAAI;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGf5G,eAAe,iBACZf,OAAA,CAACd,MAAM,CAAC2I,MAAM;UACVC,UAAU,EAAE;YAAEd,KAAK,EAAE;UAAK,CAAE;UAC5Be,QAAQ,EAAE;YAAEf,KAAK,EAAE;UAAK,CAAE;UAC1BgB,OAAO,EAAE3B,YAAa;UACtBM,SAAS,EAAC,sMAAsM;UAAAC,QAAA,gBAEhN5G,OAAA,CAACR,QAAQ;YAACmH,SAAS,EAAC;UAAS;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChC3H,OAAA;YAAA4G,QAAA,EAAM;UAAO;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAClB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAGb3H,OAAA,CAACd,MAAM,CAAC2H,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCX,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAE,CAAE;QAC9BjB,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBAEjC5G,OAAA;UAAK2G,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBACxD5G,OAAA,CAACd,MAAM,CAAC2H,GAAG;YACPI,OAAO,EAAE;cACLC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;cACvBF,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;YACrB,CAAE;YACFG,UAAU,EAAE;cAAEC,QAAQ,EAAE,CAAC;cAAEC,MAAM,EAAEC,QAAQ;cAAEe,WAAW,EAAE;YAAE,CAAE;YAC9D1B,SAAS,EAAC,UAAU;YAAAC,QAAA,gBAEpB5G,OAAA,CAACb,QAAQ;cAACwH,SAAS,EAAC;YAAyC;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChE3H,OAAA,CAACd,MAAM,CAAC2H,GAAG;cACPI,OAAO,EAAE;gBAAED,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;cAAE,CAAE;cAChCG,UAAU,EAAE;gBAAEC,QAAQ,EAAE,CAAC;gBAAEC,MAAM,EAAEC;cAAS,CAAE;cAC9CX,SAAS,EAAC;YAA6D;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACb3H,OAAA;YAAA4G,QAAA,gBACI5G,OAAA;cAAI2G,SAAS,EAAC,oHAAoH;cAAAC,QAAA,EAAC;YAEnI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3H,OAAA;cAAK2G,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACjE5G,OAAA,CAACP,MAAM;gBAACkH,SAAS,EAAC;cAAiB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtC3H,OAAA;gBAAM2G,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAC;cAAgB;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7D3H,OAAA,CAACP,MAAM;gBAACkH,SAAS,EAAC;cAAiB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN3H,OAAA,CAACd,MAAM,CAAC2H,GAAG;UACPC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAI,CAAE;UACpCC,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAE,CAAE;UAClCG,UAAU,EAAE;YAAEmB,KAAK,EAAE;UAAI,CAAE;UAC3B3B,SAAS,EAAC,4FAA4F;UAAAC,QAAA,eAEtG5G,OAAA;YAAK2G,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAC9D5G,OAAA;cAAK2G,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD5G,OAAA,CAACN,OAAO;gBAACiH,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C3H,OAAA;gBAAM2G,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACrC7F,eAAe,GAAI,eAAcA,eAAgB,EAAC,GAAG;cAAuB;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN3H,OAAA;cAAK2G,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD5G,OAAA,CAACL,OAAO;gBAACgH,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C3H,OAAA;gBAAM2G,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,GACrCrG,WAAW,CAAC2E,MAAM,EAAC,cACxB;cAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACN3H,OAAA;cAAK2G,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACnD5G,OAAA,CAACZ,OAAO;gBAACuH,SAAS,EAAC;cAAyB;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/C3H,OAAA;gBAAM2G,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAC;cAE3C;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,eAGN3H,OAAA;MAAK2G,SAAS,EAAC,2DAA2D;MAAAC,QAAA,EACrErG,WAAW,CAAC2E,MAAM,KAAK,CAAC,gBACrBlF,OAAA,CAACd,MAAM,CAAC2H,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAI,CAAE;QACpCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAE,CAAE;QAClCL,SAAS,EAAC,mFAAmF;QAAAC,QAAA,gBAE7F5G,OAAA,CAACd,MAAM,CAAC2H,GAAG;UACPI,OAAO,EAAE;YAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;UAAE,CAAE;UACrCC,UAAU,EAAE;YAAEC,QAAQ,EAAE,CAAC;YAAEC,MAAM,EAAEC;UAAS,CAAE;UAAAV,QAAA,eAE9C5G,OAAA,CAACZ,OAAO;YAACuH,SAAS,EAAC;UAAwC;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eACb3H,OAAA;UAAI2G,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAqB;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E3H,OAAA;UAAG2G,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAA8C;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5F3H,OAAA,CAACd,MAAM,CAAC2I,MAAM;UACVC,UAAU,EAAE;YAAEd,KAAK,EAAE;UAAK,CAAE;UAC5Be,QAAQ,EAAE;YAAEf,KAAK,EAAE;UAAK,CAAE;UAC1BgB,OAAO,EAAEtB,aAAc;UACvBC,SAAS,EAAC,sKAAsK;UAAAC,QAAA,gBAEhL5G,OAAA,CAACX,SAAS;YAACsH,SAAS,EAAC;UAAqB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEjD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,gBAEb3H,OAAA,CAACd,MAAM,CAAC2H,GAAG;QACPC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAG,CAAE;QAC/BX,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEa,CAAC,EAAE;QAAE,CAAE;QAC9BT,UAAU,EAAE;UAAEmB,KAAK,EAAE;QAAI,CAAE;QAC3B3B,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eAE9E5G,OAAA,CAACH,eAAe;UACZQ,KAAK,EAAEE,WAAY;UACnBgI,aAAa,EAAE,CAAAjI,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,GAAG,KAAI,IAAK;UACjCoF,MAAM,EAAC,YAAY;UACnBC,IAAI,EAAC,QAAQ;UACbC,SAAS,EAAE,IAAK;UAChB/B,SAAS,EAAC,WAAW;UACrBpF,cAAc,EAAEA,cAAe;UAC/BN,UAAU,EAAEA,UAAW;UACvB0H,SAAS,EAAEjC,aAAc;UACzBvF,WAAW,EAAEA,WAAY;UACzBE,WAAW,EAAEA,WAAY;UACzBuH,mBAAmB,EAAEA,CAAA,KAAMtH,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzH,EAAA,CAvYID,OAAO;EAAA,QACShB,WAAW;AAAA;AAAA4J,EAAA,GAD3B5I,OAAO;AAyYb,eAAeA,OAAO;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}