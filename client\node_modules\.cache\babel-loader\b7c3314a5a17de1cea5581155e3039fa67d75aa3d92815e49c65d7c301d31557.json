{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingCard.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { TbTrophy, TbMedal, TbCrown, TbStar, TbFlame, TbBolt } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport XPProgressBar from './XPProgressBar';\nimport LevelBadge from './LevelBadge';\nimport EnhancedAchievementBadge from './EnhancedAchievementBadge';\nimport './UserRankingCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingCard = ({\n  user,\n  rank,\n  classRank,\n  isCurrentUser = false,\n  layout = 'horizontal',\n  // 'horizontal' or 'vertical'\n  size = 'medium',\n  // 'small', 'medium', 'large'\n  showStats = true,\n  className = ''\n}) => {\n  // Size configurations - Optimized profile circle sizes for better visibility\n  const sizeConfig = {\n    small: {\n      avatar: 'w-12 h-12',\n      text: 'text-sm',\n      subtext: 'text-xs',\n      padding: 'p-3',\n      spacing: 'space-x-3'\n    },\n    medium: {\n      avatar: 'w-14 h-14',\n      text: 'text-base',\n      subtext: 'text-sm',\n      padding: 'p-4',\n      spacing: 'space-x-4'\n    },\n    large: {\n      avatar: 'w-16 h-16',\n      text: 'text-lg',\n      subtext: 'text-base',\n      padding: 'p-5',\n      spacing: 'space-x-5'\n    }\n  };\n  const config = sizeConfig[size];\n\n  // Get subscription status styling with improved status detection\n  const getSubscriptionStyling = () => {\n    const subscriptionStatus = (user === null || user === void 0 ? void 0 : user.subscriptionStatus) || 'free';\n\n    // Normalize status for better handling\n    const normalizedStatus = subscriptionStatus.toLowerCase();\n    if (normalizedStatus === 'active' || normalizedStatus === 'premium') {\n      return {\n        avatarClass: 'avatar-premium premium-glow',\n        badge: 'status-premium',\n        glow: 'shadow-lg shadow-yellow-400/50',\n        statusText: 'Premium',\n        borderClass: 'ring-2 ring-yellow-400'\n      };\n    } else if (normalizedStatus === 'free') {\n      return {\n        avatarClass: 'avatar-free',\n        badge: 'status-free',\n        glow: 'shadow-md shadow-blue-400/30',\n        statusText: 'Free',\n        borderClass: 'ring-2 ring-blue-400'\n      };\n    } else {\n      return {\n        avatarClass: 'avatar-expired',\n        badge: 'status-expired',\n        glow: 'shadow-sm',\n        statusText: 'Expired',\n        borderClass: 'ring-2 ring-red-400'\n      };\n    }\n  };\n  const styling = getSubscriptionStyling();\n\n  // Get rank icon and color\n  const getRankDisplay = () => {\n    if (rank === 1) {\n      return {\n        icon: TbCrown,\n        color: 'text-yellow-500',\n        bg: 'bg-yellow-50'\n      };\n    } else if (rank === 2) {\n      return {\n        icon: TbMedal,\n        color: 'text-gray-400',\n        bg: 'bg-gray-50'\n      };\n    } else if (rank === 3) {\n      return {\n        icon: TbTrophy,\n        color: 'text-amber-600',\n        bg: 'bg-amber-50'\n      };\n    } else if (rank <= 10) {\n      return {\n        icon: TbStar,\n        color: 'text-blue-500',\n        bg: 'bg-blue-50'\n      };\n    } else {\n      return {\n        icon: null,\n        color: 'text-gray-500',\n        bg: 'bg-gray-50'\n      };\n    }\n  };\n  const rankDisplay = getRankDisplay();\n  const RankIcon = rankDisplay.icon;\n\n  // Animation variants\n  const cardVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.3\n      }\n    },\n    hover: {\n      scale: 1.02,\n      transition: {\n        duration: 0.2\n      }\n    }\n  };\n  const avatarVariants = {\n    hover: {\n      scale: 1.1,\n      transition: {\n        duration: 0.2\n      }\n    }\n  };\n\n  // Avatar wrapper with subscription styling\n  const StyledAvatar = ({\n    children\n  }) => {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${config.avatar} ${styling.avatarClass} ${styling.glow}`,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 13\n    }, this);\n  };\n  if (layout === 'vertical') {\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: cardVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      whileHover: \"hover\",\n      className: `\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    bg-white rounded-xl border border-gray-200\n                    ${isCurrentUser ? 'current-user-card' : ''}\n                    ${className}\n                `,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ${rankDisplay.bg} ${rankDisplay.color}\n                    `,\n          children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold\",\n            children: [\"#\", rank]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 21\n        }, this), classRank && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold\",\n            children: [\"C\", classRank]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: avatarVariants,\n        whileHover: \"hover\",\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                        ${styling.avatarClass} ${styling.borderClass}\n                    `,\n          children: user !== null && user !== void 0 && user.profilePicture || user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: user.profilePicture || user.profileImage,\n            alt: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n            className: \"w-full h-full object-cover object-center\",\n            style: {\n              objectFit: 'cover'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-bold text-white\",\n              children: ((user === null || user === void 0 ? void 0 : user.name) || 'U').charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate max-w-24`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(LevelBadge, {\n            level: (user === null || user === void 0 ? void 0 : user.currentLevel) || 1,\n            size: \"small\",\n            showTitle: false,\n            animated: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: `${config.subtext} text-blue-600 font-medium`,\n              children: [(user === null || user === void 0 ? void 0 : user.totalXP) || 0, \" XP\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 29\n            }, this), (user === null || user === void 0 ? void 0 : user.xpToNextLevel) > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs text-gray-400`,\n              children: [user.xpToNextLevel, \" to next\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs text-gray-400`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" pts (legacy)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 21\n        }, this), (user === null || user === void 0 ? void 0 : user.averageScore) && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `${config.subtext} text-gray-500`,\n          children: [\"Avg: \", user.averageScore, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.currentStreak) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.achievements) && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [user.achievements.slice(0, 3).map((achievement, index) => /*#__PURE__*/_jsxDEV(EnhancedAchievementBadge, {\n            achievement: achievement,\n            size: \"small\",\n            showTooltip: true,\n            animated: true,\n            showXP: false\n          }, achievement.id || achievement.type || index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 33\n          }, this)), user.achievements.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500 ml-1\",\n            children: [\"+\", user.achievements.length - 3]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `\n                        inline-block px-2 py-1 rounded-full text-xs font-medium\n                        ${styling.badge}\n                    `,\n          children: (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'active' || (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'premium' ? 'Premium' : (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'free' ? 'Free' : 'Expired'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Horizontal layout (default)\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    variants: cardVariants,\n    initial: \"hidden\",\n    animate: \"visible\",\n    whileHover: \"hover\",\n    className: `\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200\n                ${isCurrentUser ? 'current-user-card' : ''}\n                ${className}\n            `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 flex-shrink-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `,\n        children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-bold\",\n          children: [\"#\", rank]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 17\n      }, this), classRank && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-bold\",\n          children: [\"C\", classRank]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: avatarVariants,\n      whileHover: \"hover\",\n      className: \"flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                    ${styling.avatarClass} ${styling.borderClass}\n                `,\n        children: user !== null && user !== void 0 && user.profilePicture || user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: user.profilePicture || user.profileImage,\n          alt: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n          className: \"w-full h-full object-cover object-center\",\n          style: {\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold text-white\",\n            children: ((user === null || user === void 0 ? void 0 : user.name) || 'U').charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(LevelBadge, {\n          level: (user === null || user === void 0 ? void 0 : user.currentLevel) || 1,\n          size: \"small\",\n          showTitle: false,\n          animated: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.badge}\n                    `,\n          children: (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'active' || (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'premium' ? 'Premium' : (user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'free' ? 'Free' : 'Expired'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 332,\n        columnNumber: 17\n      }, this), showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbBolt, {\n            className: \"w-3 h-3 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-blue-600 font-medium`,\n            children: [(user === null || user === void 0 ? void 0 : user.totalXP) || 0, \" XP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-xs text-gray-400`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" pts\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.passedExamsCount) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-green-600`,\n          children: [user.passedExamsCount, \" passed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.quizzesTaken) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-blue-600`,\n          children: [user.quizzesTaken, \" quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.averageScore) && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-gray-600`,\n          children: [user.averageScore, \"% avg\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.currentStreak) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 21\n      }, this), (user === null || user === void 0 ? void 0 : user.xpToNextLevel) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(XPProgressBar, {\n          currentXP: user.totalXP || 0,\n          totalXP: (user.totalXP || 0) + (user.xpToNextLevel || 0),\n          currentLevel: user.currentLevel || 1,\n          xpToNextLevel: user.xpToNextLevel || 0,\n          size: \"small\",\n          showLevel: false,\n          showXPNumbers: false,\n          showAnimation: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 21\n      }, this), (user === null || user === void 0 ? void 0 : user.achievements) && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(AchievementList, {\n          achievements: user.achievements,\n          maxDisplay: 5,\n          size: \"small\",\n          layout: \"horizontal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-right flex-shrink-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`,\n        children: user.score || user.totalPoints || 0\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${config.subtext} text-gray-500`,\n        children: \"score\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 9\n  }, this);\n};\n_c = UserRankingCard;\nexport default UserRankingCard;\nvar _c;\n$RefreshReg$(_c, \"UserRankingCard\");", "map": {"version": 3, "names": ["React", "motion", "TbTrophy", "TbMedal", "TbCrown", "TbStar", "TbFlame", "TbBolt", "AchievementList", "XPProgressBar", "LevelBadge", "EnhancedAchievementBadge", "jsxDEV", "_jsxDEV", "UserRankingCard", "user", "rank", "classRank", "isCurrentUser", "layout", "size", "showStats", "className", "sizeConfig", "small", "avatar", "text", "subtext", "padding", "spacing", "medium", "large", "config", "getSubscriptionStyling", "subscriptionStatus", "normalizedStatus", "toLowerCase", "avatarClass", "badge", "glow", "statusText", "borderClass", "styling", "getRankDisplay", "icon", "color", "bg", "rankDisplay", "RankIcon", "cardVariants", "hidden", "opacity", "y", "visible", "transition", "duration", "hover", "scale", "avatar<PERSON><PERSON><PERSON>", "Styled<PERSON>vatar", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "variants", "initial", "animate", "whileHover", "profilePicture", "profileImage", "src", "alt", "name", "style", "objectFit", "char<PERSON>t", "toUpperCase", "level", "currentLevel", "showTitle", "animated", "totalXP", "xpToNextLevel", "totalPoints", "averageScore", "currentStreak", "achievements", "length", "slice", "map", "achievement", "index", "showTooltip", "showXP", "id", "type", "passedExamsCount", "undefined", "quizzesTaken", "currentXP", "showLevel", "showXPNumbers", "showAnimation", "maxDisplay", "score", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingCard.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>rophy, TbMedal, TbCrown, TbStar, TbFlame, TbBolt } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport XPProgressBar from './XPProgressBar';\nimport LevelBadge from './LevelBadge';\nimport EnhancedAchievementBadge from './EnhancedAchievementBadge';\nimport './UserRankingCard.css';\n\nconst UserRankingCard = ({\n    user,\n    rank,\n    classRank,\n    isCurrentUser = false,\n    layout = 'horizontal', // 'horizontal' or 'vertical'\n    size = 'medium', // 'small', 'medium', 'large'\n    showStats = true,\n    className = ''\n}) => {\n    // Size configurations - Optimized profile circle sizes for better visibility\n    const sizeConfig = {\n        small: {\n            avatar: 'w-12 h-12',\n            text: 'text-sm',\n            subtext: 'text-xs',\n            padding: 'p-3',\n            spacing: 'space-x-3'\n        },\n        medium: {\n            avatar: 'w-14 h-14',\n            text: 'text-base',\n            subtext: 'text-sm',\n            padding: 'p-4',\n            spacing: 'space-x-4'\n        },\n        large: {\n            avatar: 'w-16 h-16',\n            text: 'text-lg',\n            subtext: 'text-base',\n            padding: 'p-5',\n            spacing: 'space-x-5'\n        }\n    };\n\n    const config = sizeConfig[size];\n\n    // Get subscription status styling with improved status detection\n    const getSubscriptionStyling = () => {\n        const subscriptionStatus = user?.subscriptionStatus || 'free';\n\n        // Normalize status for better handling\n        const normalizedStatus = subscriptionStatus.toLowerCase();\n\n        if (normalizedStatus === 'active' || normalizedStatus === 'premium') {\n            return {\n                avatarClass: 'avatar-premium premium-glow',\n                badge: 'status-premium',\n                glow: 'shadow-lg shadow-yellow-400/50',\n                statusText: 'Premium',\n                borderClass: 'ring-2 ring-yellow-400'\n            };\n        } else if (normalizedStatus === 'free') {\n            return {\n                avatarClass: 'avatar-free',\n                badge: 'status-free',\n                glow: 'shadow-md shadow-blue-400/30',\n                statusText: 'Free',\n                borderClass: 'ring-2 ring-blue-400'\n            };\n        } else {\n            return {\n                avatarClass: 'avatar-expired',\n                badge: 'status-expired',\n                glow: 'shadow-sm',\n                statusText: 'Expired',\n                borderClass: 'ring-2 ring-red-400'\n            };\n        }\n    };\n\n    const styling = getSubscriptionStyling();\n\n    // Get rank icon and color\n    const getRankDisplay = () => {\n        if (rank === 1) {\n            return { icon: TbCrown, color: 'text-yellow-500', bg: 'bg-yellow-50' };\n        } else if (rank === 2) {\n            return { icon: TbMedal, color: 'text-gray-400', bg: 'bg-gray-50' };\n        } else if (rank === 3) {\n            return { icon: TbTrophy, color: 'text-amber-600', bg: 'bg-amber-50' };\n        } else if (rank <= 10) {\n            return { icon: TbStar, color: 'text-blue-500', bg: 'bg-blue-50' };\n        } else {\n            return { icon: null, color: 'text-gray-500', bg: 'bg-gray-50' };\n        }\n    };\n\n    const rankDisplay = getRankDisplay();\n    const RankIcon = rankDisplay.icon;\n\n    // Animation variants\n    const cardVariants = {\n        hidden: { opacity: 0, y: 20 },\n        visible: { \n            opacity: 1, \n            y: 0,\n            transition: { duration: 0.3 }\n        },\n        hover: { \n            scale: 1.02,\n            transition: { duration: 0.2 }\n        }\n    };\n\n    const avatarVariants = {\n        hover: { \n            scale: 1.1,\n            transition: { duration: 0.2 }\n        }\n    };\n\n    // Avatar wrapper with subscription styling\n    const StyledAvatar = ({ children }) => {\n        return (\n            <div className={`${config.avatar} ${styling.avatarClass} ${styling.glow}`}>\n                {children}\n            </div>\n        );\n    };\n\n    if (layout === 'vertical') {\n        return (\n            <motion.div\n                variants={cardVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                whileHover=\"hover\"\n                className={`\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    bg-white rounded-xl border border-gray-200\n                    ${isCurrentUser ? 'current-user-card' : ''}\n                    ${className}\n                `}\n            >\n                {/* Rank Badges */}\n                <div className=\"flex items-center space-x-2 mb-3\">\n                    {/* Overall Rank */}\n                    <div className={`\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ${rankDisplay.bg} ${rankDisplay.color}\n                    `}>\n                        {RankIcon ? (\n                            <RankIcon className=\"w-4 h-4\" />\n                        ) : (\n                            <span className=\"text-xs font-bold\">#{rank}</span>\n                        )}\n                    </div>\n\n                    {/* Class Rank */}\n                    {classRank && (\n                        <div className=\"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700\">\n                            <span className=\"text-xs font-bold\">C{classRank}</span>\n                        </div>\n                    )}\n                </div>\n\n                {/* Avatar - Instagram Style Small Circle */}\n                <motion.div variants={avatarVariants} whileHover=\"hover\" className=\"mb-3\">\n                    <div className={`\n                        ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                        ${styling.avatarClass} ${styling.borderClass}\n                    `}>\n                        {user?.profilePicture || user?.profileImage ? (\n                            <img\n                                src={user.profilePicture || user.profileImage}\n                                alt={user?.name || 'User'}\n                                className=\"w-full h-full object-cover object-center\"\n                                style={{ objectFit: 'cover' }}\n                            />\n                        ) : (\n                            <div className=\"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\">\n                                <span className=\"text-xs font-bold text-white\">\n                                    {(user?.name || 'U').charAt(0).toUpperCase()}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                </motion.div>\n\n                {/* User Info */}\n                <div className=\"space-y-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate max-w-24`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n\n                    {/* XP and Level Info */}\n                    <div className=\"flex items-center space-x-2\">\n                        <LevelBadge\n                            level={user?.currentLevel || 1}\n                            size=\"small\"\n                            showTitle={false}\n                            animated={true}\n                        />\n                        <div className=\"flex flex-col\">\n                            <p className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </p>\n                            {user?.xpToNextLevel > 0 && (\n                                <p className={`text-xs text-gray-400`}>\n                                    {user.xpToNextLevel} to next\n                                </p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Legacy Points (smaller) */}\n                    <p className={`text-xs text-gray-400`}>\n                        {user?.totalPoints || 0} pts (legacy)\n                    </p>\n\n                    {/* Enhanced Stats */}\n                    {user?.averageScore && (\n                        <p className={`${config.subtext} text-gray-500`}>\n                            Avg: {user.averageScore}%\n                        </p>\n                    )}\n\n                    {user?.currentStreak > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                            <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                {user.currentStreak}\n                            </span>\n                        </div>\n                    )}\n\n                    {/* Enhanced Achievements */}\n                    {user?.achievements && user.achievements.length > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            {user.achievements.slice(0, 3).map((achievement, index) => (\n                                <EnhancedAchievementBadge\n                                    key={achievement.id || achievement.type || index}\n                                    achievement={achievement}\n                                    size=\"small\"\n                                    showTooltip={true}\n                                    animated={true}\n                                    showXP={false}\n                                />\n                            ))}\n                            {user.achievements.length > 3 && (\n                                <span className=\"text-xs text-gray-500 ml-1\">\n                                    +{user.achievements.length - 3}\n                                </span>\n                            )}\n                        </div>\n                    )}\n\n                    {/* Subscription Badge */}\n                    <span className={`\n                        inline-block px-2 py-1 rounded-full text-xs font-medium\n                        ${styling.badge}\n                    `}>\n                        {(user?.subscriptionStatus === 'active' || user?.subscriptionStatus === 'premium') ? 'Premium' :\n                         user?.subscriptionStatus === 'free' ? 'Free' : 'Expired'}\n                    </span>\n                </div>\n            </motion.div>\n        );\n    }\n\n    // Horizontal layout (default)\n    return (\n        <motion.div\n            variants={cardVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            whileHover=\"hover\"\n            className={`\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200\n                ${isCurrentUser ? 'current-user-card' : ''}\n                ${className}\n            `}\n        >\n            {/* Rank Badges */}\n            <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                {/* Overall Rank */}\n                <div className={`\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `}>\n                    {RankIcon ? (\n                        <RankIcon className=\"w-5 h-5\" />\n                    ) : (\n                        <span className=\"text-sm font-bold\">#{rank}</span>\n                    )}\n                </div>\n\n                {/* Class Rank */}\n                {classRank && (\n                    <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700\">\n                        <span className=\"text-sm font-bold\">C{classRank}</span>\n                    </div>\n                )}\n            </div>\n\n            {/* Avatar - Instagram Style Small Circle */}\n            <motion.div variants={avatarVariants} whileHover=\"hover\" className=\"flex-shrink-0\">\n                <div className={`\n                    ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                    ${styling.avatarClass} ${styling.borderClass}\n                `}>\n                    {user?.profilePicture || user?.profileImage ? (\n                        <img\n                            src={user.profilePicture || user.profileImage}\n                            alt={user?.name || 'User'}\n                            className=\"w-full h-full object-cover object-center\"\n                            style={{ objectFit: 'cover' }}\n                        />\n                    ) : (\n                        <div className=\"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\">\n                            <span className=\"text-xs font-bold text-white\">\n                                {(user?.name || 'U').charAt(0).toUpperCase()}\n                            </span>\n                        </div>\n                    )}\n                </div>\n            </motion.div>\n\n            {/* User Info */}\n            <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2 mb-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n                    <LevelBadge\n                        level={user?.currentLevel || 1}\n                        size=\"small\"\n                        showTitle={false}\n                        animated={true}\n                    />\n                    <span className={`\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.badge}\n                    `}>\n                        {(user?.subscriptionStatus === 'active' || user?.subscriptionStatus === 'premium') ? 'Premium' :\n                         user?.subscriptionStatus === 'free' ? 'Free' : 'Expired'}\n                    </span>\n                </div>\n                \n                {showStats && (\n                    <div className=\"flex items-center space-x-4\">\n                        {/* XP Display */}\n                        <div className=\"flex items-center space-x-1\">\n                            <TbBolt className=\"w-3 h-3 text-blue-500\" />\n                            <span className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </span>\n                        </div>\n\n                        {/* Legacy Points (smaller) */}\n                        <span className={`text-xs text-gray-400`}>\n                            {user?.totalPoints || 0} pts\n                        </span>\n\n                        {user?.passedExamsCount !== undefined && (\n                            <span className={`${config.subtext} text-green-600`}>\n                                {user.passedExamsCount} passed\n                            </span>\n                        )}\n                        {user?.quizzesTaken !== undefined && (\n                            <span className={`${config.subtext} text-blue-600`}>\n                                {user.quizzesTaken} quizzes\n                            </span>\n                        )}\n                        {user?.averageScore && (\n                            <span className={`${config.subtext} text-gray-600`}>\n                                {user.averageScore}% avg\n                            </span>\n                        )}\n                        {user?.currentStreak > 0 && (\n                            <div className=\"flex items-center space-x-1\">\n                                <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                                <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                    {user.currentStreak}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                )}\n\n                {/* XP Progress Bar */}\n                {user?.xpToNextLevel > 0 && (\n                    <div className=\"mt-2\">\n                        <XPProgressBar\n                            currentXP={user.totalXP || 0}\n                            totalXP={(user.totalXP || 0) + (user.xpToNextLevel || 0)}\n                            currentLevel={user.currentLevel || 1}\n                            xpToNextLevel={user.xpToNextLevel || 0}\n                            size=\"small\"\n                            showLevel={false}\n                            showXPNumbers={false}\n                            showAnimation={false}\n                        />\n                    </div>\n                )}\n\n                {/* Achievements for horizontal layout */}\n                {user?.achievements && user.achievements.length > 0 && (\n                    <div className=\"mt-2\">\n                        <AchievementList\n                            achievements={user.achievements}\n                            maxDisplay={5}\n                            size=\"small\"\n                            layout=\"horizontal\"\n                        />\n                    </div>\n                )}\n            </div>\n\n            {/* Score */}\n            <div className=\"text-right flex-shrink-0\">\n                <div className={`font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`}>\n                    {user.score || user.totalPoints || 0}\n                </div>\n                <div className={`${config.subtext} text-gray-500`}>score</div>\n            </div>\n        </motion.div>\n    );\n};\n\nexport default UserRankingCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACpF,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EACrBC,IAAI;EACJC,IAAI;EACJC,SAAS;EACTC,aAAa,GAAG,KAAK;EACrBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EAAE;EACjBC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG;AAChB,CAAC,KAAK;EACF;EACA,MAAMC,UAAU,GAAG;IACfC,KAAK,EAAE;MACHC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACJL,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDE,KAAK,EAAE;MACHN,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb;EACJ,CAAC;EAED,MAAMG,MAAM,GAAGT,UAAU,CAACH,IAAI,CAAC;;EAE/B;EACA,MAAMa,sBAAsB,GAAGA,CAAA,KAAM;IACjC,MAAMC,kBAAkB,GAAG,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,KAAI,MAAM;;IAE7D;IACA,MAAMC,gBAAgB,GAAGD,kBAAkB,CAACE,WAAW,CAAC,CAAC;IAEzD,IAAID,gBAAgB,KAAK,QAAQ,IAAIA,gBAAgB,KAAK,SAAS,EAAE;MACjE,OAAO;QACHE,WAAW,EAAE,6BAA6B;QAC1CC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,gCAAgC;QACtCC,UAAU,EAAE,SAAS;QACrBC,WAAW,EAAE;MACjB,CAAC;IACL,CAAC,MAAM,IAAIN,gBAAgB,KAAK,MAAM,EAAE;MACpC,OAAO;QACHE,WAAW,EAAE,aAAa;QAC1BC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,8BAA8B;QACpCC,UAAU,EAAE,MAAM;QAClBC,WAAW,EAAE;MACjB,CAAC;IACL,CAAC,MAAM;MACH,OAAO;QACHJ,WAAW,EAAE,gBAAgB;QAC7BC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,WAAW;QACjBC,UAAU,EAAE,SAAS;QACrBC,WAAW,EAAE;MACjB,CAAC;IACL;EACJ,CAAC;EAED,MAAMC,OAAO,GAAGT,sBAAsB,CAAC,CAAC;;EAExC;EACA,MAAMU,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAI3B,IAAI,KAAK,CAAC,EAAE;MACZ,OAAO;QAAE4B,IAAI,EAAExC,OAAO;QAAEyC,KAAK,EAAE,iBAAiB;QAAEC,EAAE,EAAE;MAAe,CAAC;IAC1E,CAAC,MAAM,IAAI9B,IAAI,KAAK,CAAC,EAAE;MACnB,OAAO;QAAE4B,IAAI,EAAEzC,OAAO;QAAE0C,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACtE,CAAC,MAAM,IAAI9B,IAAI,KAAK,CAAC,EAAE;MACnB,OAAO;QAAE4B,IAAI,EAAE1C,QAAQ;QAAE2C,KAAK,EAAE,gBAAgB;QAAEC,EAAE,EAAE;MAAc,CAAC;IACzE,CAAC,MAAM,IAAI9B,IAAI,IAAI,EAAE,EAAE;MACnB,OAAO;QAAE4B,IAAI,EAAEvC,MAAM;QAAEwC,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACrE,CAAC,MAAM;MACH,OAAO;QAAEF,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACnE;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGJ,cAAc,CAAC,CAAC;EACpC,MAAMK,QAAQ,GAAGD,WAAW,CAACH,IAAI;;EAEjC;EACA,MAAMK,YAAY,GAAG;IACjBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAC;IAC7BC,OAAO,EAAE;MACLF,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC,CAAC;IACDC,KAAK,EAAE;MACHC,KAAK,EAAE,IAAI;MACXH,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC;EACJ,CAAC;EAED,MAAMG,cAAc,GAAG;IACnBF,KAAK,EAAE;MACHC,KAAK,EAAE,GAAG;MACVH,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC;EACJ,CAAC;;EAED;EACA,MAAMI,YAAY,GAAGA,CAAC;IAAEC;EAAS,CAAC,KAAK;IACnC,oBACI/C,OAAA;MAAKS,SAAS,EAAG,GAAEU,MAAM,CAACP,MAAO,IAAGiB,OAAO,CAACL,WAAY,IAAGK,OAAO,CAACH,IAAK,EAAE;MAAAqB,QAAA,EACrEA;IAAQ;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEd,CAAC;EAED,IAAI7C,MAAM,KAAK,UAAU,EAAE;IACvB,oBACIN,OAAA,CAACZ,MAAM,CAACgE,GAAG;MACPC,QAAQ,EAAEjB,YAAa;MACvBkB,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,SAAS;MACjBC,UAAU,EAAC,OAAO;MAClB/C,SAAS,EAAG;AAC5B,0EAA0EU,MAAM,CAACJ,OAAQ;AACzF;AACA,sBAAsBV,aAAa,GAAG,mBAAmB,GAAG,EAAG;AAC/D,sBAAsBI,SAAU;AAChC,iBAAkB;MAAAsC,QAAA,gBAGF/C,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAsC,QAAA,gBAE7C/C,OAAA;UAAKS,SAAS,EAAG;AACrC;AACA,0BAA0ByB,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AAC9D,qBAAsB;UAAAe,QAAA,EACGZ,QAAQ,gBACLnC,OAAA,CAACmC,QAAQ;YAAC1B,SAAS,EAAC;UAAS;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhCnD,OAAA;YAAMS,SAAS,EAAC,mBAAmB;YAAAsC,QAAA,GAAC,GAAC,EAAC5C,IAAI;UAAA;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QACpD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGL/C,SAAS,iBACNJ,OAAA;UAAKS,SAAS,EAAC,iFAAiF;UAAAsC,QAAA,eAC5F/C,OAAA;YAAMS,SAAS,EAAC,mBAAmB;YAAAsC,QAAA,GAAC,GAAC,EAAC3C,SAAS;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGNnD,OAAA,CAACZ,MAAM,CAACgE,GAAG;QAACC,QAAQ,EAAER,cAAe;QAACW,UAAU,EAAC,OAAO;QAAC/C,SAAS,EAAC,MAAM;QAAAsC,QAAA,eACrE/C,OAAA;UAAKS,SAAS,EAAG;AACrC,0BAA0BU,MAAM,CAACP,MAAO;AACxC,0BAA0BiB,OAAO,CAACL,WAAY,IAAGK,OAAO,CAACD,WAAY;AACrE,qBAAsB;UAAAmB,QAAA,EACG7C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuD,cAAc,IAAIvD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwD,YAAY,gBACvC1D,OAAA;YACI2D,GAAG,EAAEzD,IAAI,CAACuD,cAAc,IAAIvD,IAAI,CAACwD,YAAa;YAC9CE,GAAG,EAAE,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,KAAI,MAAO;YAC1BpD,SAAS,EAAC,0CAA0C;YACpDqD,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAQ;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,gBAEFnD,OAAA;YAAKS,SAAS,EAAC,8FAA8F;YAAAsC,QAAA,eACzG/C,OAAA;cAAMS,SAAS,EAAC,8BAA8B;cAAAsC,QAAA,EACzC,CAAC,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,KAAI,GAAG,EAAEG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGbnD,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAsC,QAAA,gBACtB/C,OAAA;UAAIS,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,kCAAkC;UAAAkC,QAAA,EACzE,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,KAAI;QAAc;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAGLnD,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAsC,QAAA,gBACxC/C,OAAA,CAACH,UAAU;YACPqE,KAAK,EAAE,CAAAhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,YAAY,KAAI,CAAE;YAC/B5D,IAAI,EAAC,OAAO;YACZ6D,SAAS,EAAE,KAAM;YACjBC,QAAQ,EAAE;UAAK;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACFnD,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAAsC,QAAA,gBAC1B/C,OAAA;cAAGS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,4BAA4B;cAAAiC,QAAA,GACvD,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,OAAO,KAAI,CAAC,EAAC,KACxB;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACH,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,aAAa,IAAG,CAAC,iBACpBvE,OAAA;cAAGS,SAAS,EAAG,uBAAuB;cAAAsC,QAAA,GACjC7C,IAAI,CAACqE,aAAa,EAAC,UACxB;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNnD,OAAA;UAAGS,SAAS,EAAG,uBAAuB;UAAAsC,QAAA,GACjC,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,WAAW,KAAI,CAAC,EAAC,eAC5B;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGH,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,YAAY,kBACfzE,OAAA;UAAGS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAAiC,QAAA,GAAC,OACxC,EAAC7C,IAAI,CAACuE,YAAY,EAAC,GAC5B;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACN,EAEA,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,aAAa,IAAG,CAAC,iBACpB1E,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAsC,QAAA,gBACxC/C,OAAA,CAACP,OAAO;YAACgB,SAAS,EAAC;UAAyB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CnD,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAAiC,QAAA,EAC5D7C,IAAI,CAACwE;UAAa;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR,EAGA,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,YAAY,KAAIzE,IAAI,CAACyE,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC/C5E,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAsC,QAAA,GACvC7C,IAAI,CAACyE,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAClDhF,OAAA,CAACF,wBAAwB;YAErBiF,WAAW,EAAEA,WAAY;YACzBxE,IAAI,EAAC,OAAO;YACZ0E,WAAW,EAAE,IAAK;YAClBZ,QAAQ,EAAE,IAAK;YACfa,MAAM,EAAE;UAAM,GALTH,WAAW,CAACI,EAAE,IAAIJ,WAAW,CAACK,IAAI,IAAIJ,KAAK;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMnD,CACJ,CAAC,EACDjD,IAAI,CAACyE,YAAY,CAACC,MAAM,GAAG,CAAC,iBACzB5E,OAAA;YAAMS,SAAS,EAAC,4BAA4B;YAAAsC,QAAA,GAAC,GACxC,EAAC7C,IAAI,CAACyE,YAAY,CAACC,MAAM,GAAG,CAAC;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR,eAGDnD,OAAA;UAAMS,SAAS,EAAG;AACtC;AACA,0BAA0BoB,OAAO,CAACJ,KAAM;AACxC,qBAAsB;UAAAsB,QAAA,EACI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,MAAK,QAAQ,IAAI,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,MAAK,SAAS,GAAI,SAAS,GAC7F,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,MAAK,MAAM,GAAG,MAAM,GAAG;QAAS;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAErB;;EAEA;EACA,oBACInD,OAAA,CAACZ,MAAM,CAACgE,GAAG;IACPC,QAAQ,EAAEjB,YAAa;IACvBkB,OAAO,EAAC,QAAQ;IAChBC,OAAO,EAAC,SAAS;IACjBC,UAAU,EAAC,OAAO;IAClB/C,SAAS,EAAG;AACxB,iDAAiDU,MAAM,CAACH,OAAQ,IAAGG,MAAM,CAACJ,OAAQ;AAClF;AACA,kBAAkBV,aAAa,GAAG,mBAAmB,GAAG,EAAG;AAC3D,kBAAkBI,SAAU;AAC5B,aAAc;IAAAsC,QAAA,gBAGF/C,OAAA;MAAKS,SAAS,EAAC,2CAA2C;MAAAsC,QAAA,gBAEtD/C,OAAA;QAAKS,SAAS,EAAG;AACjC;AACA,sBAAsByB,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AAC1D,iBAAkB;QAAAe,QAAA,EACGZ,QAAQ,gBACLnC,OAAA,CAACmC,QAAQ;UAAC1B,SAAS,EAAC;QAAS;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEhCnD,OAAA;UAAMS,SAAS,EAAC,mBAAmB;UAAAsC,QAAA,GAAC,GAAC,EAAC5C,IAAI;QAAA;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACpD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGL/C,SAAS,iBACNJ,OAAA;QAAKS,SAAS,EAAC,mFAAmF;QAAAsC,QAAA,eAC9F/C,OAAA;UAAMS,SAAS,EAAC,mBAAmB;UAAAsC,QAAA,GAAC,GAAC,EAAC3C,SAAS;QAAA;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNnD,OAAA,CAACZ,MAAM,CAACgE,GAAG;MAACC,QAAQ,EAAER,cAAe;MAACW,UAAU,EAAC,OAAO;MAAC/C,SAAS,EAAC,eAAe;MAAAsC,QAAA,eAC9E/C,OAAA;QAAKS,SAAS,EAAG;AACjC,sBAAsBU,MAAM,CAACP,MAAO;AACpC,sBAAsBiB,OAAO,CAACL,WAAY,IAAGK,OAAO,CAACD,WAAY;AACjE,iBAAkB;QAAAmB,QAAA,EACG7C,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEuD,cAAc,IAAIvD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEwD,YAAY,gBACvC1D,OAAA;UACI2D,GAAG,EAAEzD,IAAI,CAACuD,cAAc,IAAIvD,IAAI,CAACwD,YAAa;UAC9CE,GAAG,EAAE,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,KAAI,MAAO;UAC1BpD,SAAS,EAAC,0CAA0C;UACpDqD,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAQ;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,gBAEFnD,OAAA;UAAKS,SAAS,EAAC,8FAA8F;UAAAsC,QAAA,eACzG/C,OAAA;YAAMS,SAAS,EAAC,8BAA8B;YAAAsC,QAAA,EACzC,CAAC,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,KAAI,GAAG,EAAEG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGbnD,OAAA;MAAKS,SAAS,EAAC,gBAAgB;MAAAsC,QAAA,gBAC3B/C,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAAsC,QAAA,gBAC7C/C,OAAA;UAAIS,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,yBAAyB;UAAAkC,QAAA,EAChE,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,IAAI,KAAI;QAAc;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACLnD,OAAA,CAACH,UAAU;UACPqE,KAAK,EAAE,CAAAhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,YAAY,KAAI,CAAE;UAC/B5D,IAAI,EAAC,OAAO;UACZ6D,SAAS,EAAE,KAAM;UACjBC,QAAQ,EAAE;QAAK;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACFnD,OAAA;UAAMS,SAAS,EAAG;AACtC;AACA,0BAA0BoB,OAAO,CAACJ,KAAM;AACxC,qBAAsB;UAAAsB,QAAA,EACI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,MAAK,QAAQ,IAAI,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,MAAK,SAAS,GAAI,SAAS,GAC7F,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,MAAK,MAAM,GAAG,MAAM,GAAG;QAAS;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL3C,SAAS,iBACNR,OAAA;QAAKS,SAAS,EAAC,6BAA6B;QAAAsC,QAAA,gBAExC/C,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAsC,QAAA,gBACxC/C,OAAA,CAACN,MAAM;YAACe,SAAS,EAAC;UAAuB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5CnD,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,4BAA4B;YAAAiC,QAAA,GAC1D,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoE,OAAO,KAAI,CAAC,EAAC,KACxB;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNnD,OAAA;UAAMS,SAAS,EAAG,uBAAuB;UAAAsC,QAAA,GACpC,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,WAAW,KAAI,CAAC,EAAC,MAC5B;QAAA;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAEN,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,gBAAgB,MAAKC,SAAS,iBACjCtF,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,iBAAiB;UAAAiC,QAAA,GAC/C7C,IAAI,CAACmF,gBAAgB,EAAC,SAC3B;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqF,YAAY,MAAKD,SAAS,iBAC7BtF,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAAiC,QAAA,GAC9C7C,IAAI,CAACqF,YAAY,EAAC,UACvB;QAAA;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,YAAY,kBACfzE,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAAiC,QAAA,GAC9C7C,IAAI,CAACuE,YAAY,EAAC,OACvB;QAAA;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwE,aAAa,IAAG,CAAC,iBACpB1E,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAsC,QAAA,gBACxC/C,OAAA,CAACP,OAAO;YAACgB,SAAS,EAAC;UAAyB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CnD,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAAiC,QAAA,EAC5D7C,IAAI,CAACwE;UAAa;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR,EAGA,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,aAAa,IAAG,CAAC,iBACpBvE,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAsC,QAAA,eACjB/C,OAAA,CAACJ,aAAa;UACV4F,SAAS,EAAEtF,IAAI,CAACoE,OAAO,IAAI,CAAE;UAC7BA,OAAO,EAAE,CAACpE,IAAI,CAACoE,OAAO,IAAI,CAAC,KAAKpE,IAAI,CAACqE,aAAa,IAAI,CAAC,CAAE;UACzDJ,YAAY,EAAEjE,IAAI,CAACiE,YAAY,IAAI,CAAE;UACrCI,aAAa,EAAErE,IAAI,CAACqE,aAAa,IAAI,CAAE;UACvChE,IAAI,EAAC,OAAO;UACZkF,SAAS,EAAE,KAAM;UACjBC,aAAa,EAAE,KAAM;UACrBC,aAAa,EAAE;QAAM;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGA,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,YAAY,KAAIzE,IAAI,CAACyE,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC/C5E,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAAsC,QAAA,eACjB/C,OAAA,CAACL,eAAe;UACZgF,YAAY,EAAEzE,IAAI,CAACyE,YAAa;UAChCiB,UAAU,EAAE,CAAE;UACdrF,IAAI,EAAC,OAAO;UACZD,MAAM,EAAC;QAAY;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGNnD,OAAA;MAAKS,SAAS,EAAC,0BAA0B;MAAAsC,QAAA,gBACrC/C,OAAA;QAAKS,SAAS,EAAG,aAAYU,MAAM,CAACN,IAAK,IAAGR,aAAa,GAAG,eAAe,GAAG,eAAgB,EAAE;QAAA0C,QAAA,EAC3F7C,IAAI,CAAC2F,KAAK,IAAI3F,IAAI,CAACsE,WAAW,IAAI;MAAC;QAAAxB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC,eACNnD,OAAA;QAAKS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;QAAAiC,QAAA,EAAC;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAAC2C,EAAA,GApaI7F,eAAe;AAsarB,eAAeA,eAAe;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}