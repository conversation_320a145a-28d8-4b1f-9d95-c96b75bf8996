{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\UserRankingCard.js\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { TbTrophy, TbMedal, TbCrown, TbStar, TbFlame, TbBolt } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport XPProgressBar from './XPProgressBar';\nimport LevelBadge from './LevelBadge';\nimport EnhancedAchievementBadge from './EnhancedAchievementBadge';\nimport './UserRankingCard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserRankingCard = ({\n  user,\n  rank,\n  classRank,\n  isCurrentUser = false,\n  layout = 'horizontal',\n  // 'horizontal' or 'vertical'\n  size = 'medium',\n  // 'small', 'medium', 'large'\n  showStats = true,\n  className = ''\n}) => {\n  // Size configurations - Optimized profile circle sizes for better visibility\n  const sizeConfig = {\n    small: {\n      avatar: 'w-12 h-12',\n      text: 'text-sm',\n      subtext: 'text-xs',\n      padding: 'p-3',\n      spacing: 'space-x-3'\n    },\n    medium: {\n      avatar: 'w-14 h-14',\n      text: 'text-base',\n      subtext: 'text-sm',\n      padding: 'p-4',\n      spacing: 'space-x-4'\n    },\n    large: {\n      avatar: 'w-16 h-16',\n      text: 'text-lg',\n      subtext: 'text-base',\n      padding: 'p-5',\n      spacing: 'space-x-5'\n    }\n  };\n  const config = sizeConfig[size];\n\n  // Get subscription status styling with improved status detection\n  const getSubscriptionStyling = () => {\n    const subscriptionStatus = (user === null || user === void 0 ? void 0 : user.subscriptionStatus) || (user === null || user === void 0 ? void 0 : user.normalizedSubscriptionStatus) || 'free';\n\n    // Normalize status for better handling\n    const normalizedStatus = subscriptionStatus.toLowerCase();\n    if (normalizedStatus === 'active' || normalizedStatus === 'premium') {\n      return {\n        avatarClass: 'ring-4 ring-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 ring-offset-2 ring-offset-white',\n        badge: 'status-premium',\n        glow: 'shadow-xl shadow-yellow-500/30 hover:shadow-yellow-500/40',\n        statusText: 'Premium',\n        badgeIcon: '👑',\n        borderClass: 'ring-2 ring-yellow-400',\n        bgClass: 'bg-gradient-to-r from-yellow-500 via-amber-500 to-orange-500 text-white shadow-lg border border-yellow-400/50',\n        cardBg: 'bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50',\n        textColor: 'text-yellow-700',\n        borderColor: 'border-yellow-200'\n      };\n    } else if (normalizedStatus === 'free') {\n      return {\n        avatarClass: 'ring-2 ring-blue-300 ring-offset-2 ring-offset-white',\n        badge: 'status-free',\n        glow: 'shadow-md shadow-blue-500/20 hover:shadow-blue-500/30',\n        statusText: 'Free',\n        badgeIcon: '🆓',\n        borderClass: 'ring-2 ring-blue-400',\n        bgClass: 'bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white shadow-md border border-blue-400/50',\n        cardBg: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50',\n        textColor: 'text-blue-700',\n        borderColor: 'border-blue-200'\n      };\n    } else {\n      return {\n        avatarClass: 'ring-3 ring-red-400 ring-offset-2 ring-offset-white opacity-75',\n        badge: 'status-expired',\n        glow: 'shadow-lg shadow-red-500/25 hover:shadow-red-500/35',\n        statusText: 'Expired',\n        badgeIcon: '⏰',\n        borderClass: 'ring-2 ring-red-400',\n        bgClass: 'bg-gradient-to-r from-red-500 via-red-600 to-red-700 text-white shadow-lg border border-red-400/50',\n        cardBg: 'bg-gradient-to-br from-red-50 via-pink-50 to-red-50',\n        textColor: 'text-red-700',\n        borderColor: 'border-red-200'\n      };\n    }\n  };\n  const styling = getSubscriptionStyling();\n\n  // Get rank icon and color\n  const getRankDisplay = () => {\n    if (rank === 1) {\n      return {\n        icon: TbCrown,\n        color: 'text-yellow-500',\n        bg: 'bg-yellow-50'\n      };\n    } else if (rank === 2) {\n      return {\n        icon: TbMedal,\n        color: 'text-gray-400',\n        bg: 'bg-gray-50'\n      };\n    } else if (rank === 3) {\n      return {\n        icon: TbTrophy,\n        color: 'text-amber-600',\n        bg: 'bg-amber-50'\n      };\n    } else if (rank <= 10) {\n      return {\n        icon: TbStar,\n        color: 'text-blue-500',\n        bg: 'bg-blue-50'\n      };\n    } else {\n      return {\n        icon: null,\n        color: 'text-gray-500',\n        bg: 'bg-gray-50'\n      };\n    }\n  };\n  const rankDisplay = getRankDisplay();\n  const RankIcon = rankDisplay.icon;\n\n  // Simplified animation variants\n  const cardVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.3\n      }\n    }\n  };\n  const avatarVariants = {\n    hover: {\n      scale: 1.05,\n      transition: {\n        duration: 0.2\n      }\n    }\n  };\n\n  // Avatar wrapper with subscription styling (removed unused component)\n\n  if (layout === 'vertical') {\n    return /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: cardVariants,\n      initial: \"hidden\",\n      animate: \"visible\",\n      className: `\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    ${styling.cardBg || 'bg-white'} rounded-xl border ${styling.borderColor || 'border-gray-200'}\n                    ${styling.glow} transition-all duration-300 hover:scale-105\n                    ${isCurrentUser ? 'current-user-card ring-2 ring-blue-500 ring-offset-2' : ''}\n                    ${className}\n                `,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ${rankDisplay.bg} ${rankDisplay.color}\n                    `,\n          children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n            className: \"w-4 h-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold\",\n            children: [\"#\", rank]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 21\n        }, this), classRank && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold\",\n            children: [\"C\", classRank]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: avatarVariants,\n        whileHover: \"hover\",\n        className: \"mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                        ${styling.avatarClass} ${styling.borderClass}\n                    `,\n          children: user !== null && user !== void 0 && user.profilePicture || user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: user.profilePicture || user.profileImage,\n            alt: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n            className: \"w-full h-full object-cover object-center\",\n            style: {\n              objectFit: 'cover'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-bold text-white\",\n              children: ((user === null || user === void 0 ? void 0 : user.name) || 'U').charAt(0).toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate max-w-24`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(LevelBadge, {\n            level: (user === null || user === void 0 ? void 0 : user.currentLevel) || 1,\n            size: \"small\",\n            showTitle: false,\n            animated: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: `${config.subtext} text-blue-600 font-medium`,\n              children: [(user === null || user === void 0 ? void 0 : user.totalXP) || 0, \" XP\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 29\n            }, this), (user === null || user === void 0 ? void 0 : user.xpToNextLevel) > 0 && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-xs text-gray-400`,\n              children: [user.xpToNextLevel, \" to next\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-xs text-gray-400`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" pts (legacy)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 21\n        }, this), (user === null || user === void 0 ? void 0 : user.averageScore) && /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `${config.subtext} text-gray-500`,\n          children: [\"Avg: \", user.averageScore, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.currentStreak) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.achievements) && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [user.achievements.slice(0, 3).map((achievement, index) => /*#__PURE__*/_jsxDEV(EnhancedAchievementBadge, {\n            achievement: achievement,\n            size: \"small\",\n            showTooltip: true,\n            animated: true,\n            showXP: false\n          }, achievement.id || achievement.type || index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 33\n          }, this)), user.achievements.length > 3 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs text-gray-500 ml-1\",\n            children: [\"+\", user.achievements.length - 3]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `\n                        inline-flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-semibold\n                        ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                        backdrop-blur-sm\n                    `,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm\",\n            children: styling.badgeIcon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: styling.statusText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Horizontal layout (default)\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    variants: cardVariants,\n    initial: \"hidden\",\n    animate: \"visible\",\n    whileHover: \"hover\",\n    className: `\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200\n                ${isCurrentUser ? 'current-user-card' : ''}\n                ${className}\n            `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-2 flex-shrink-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `,\n        children: RankIcon ? /*#__PURE__*/_jsxDEV(RankIcon, {\n          className: \"w-5 h-5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-bold\",\n          children: [\"#\", rank]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 17\n      }, this), classRank && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-bold\",\n          children: [\"C\", classRank]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 291,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n      variants: avatarVariants,\n      whileHover: \"hover\",\n      className: \"flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                    ${styling.avatarClass} ${styling.borderClass}\n                `,\n        children: user !== null && user !== void 0 && user.profilePicture || user !== null && user !== void 0 && user.profileImage ? /*#__PURE__*/_jsxDEV(\"img\", {\n          src: user.profilePicture || user.profileImage,\n          alt: (user === null || user === void 0 ? void 0 : user.name) || 'User',\n          className: \"w-full h-full object-cover object-center\",\n          style: {\n            objectFit: 'cover'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs font-bold text-white\",\n            children: ((user === null || user === void 0 ? void 0 : user.name) || 'U').charAt(0).toUpperCase()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 min-w-0\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2 mb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold ${config.text} text-gray-900 truncate`,\n          children: (user === null || user === void 0 ? void 0 : user.name) || 'Unknown User'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(LevelBadge, {\n          level: (user === null || user === void 0 ? void 0 : user.currentLevel) || 1,\n          size: \"small\",\n          showTitle: false,\n          animated: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.bgClass}\n                    `,\n          children: styling.statusText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 17\n      }, this), showStats && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbBolt, {\n            className: \"w-3 h-3 text-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-blue-600 font-medium`,\n            children: [(user === null || user === void 0 ? void 0 : user.totalXP) || 0, \" XP\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-xs text-gray-400`,\n          children: [(user === null || user === void 0 ? void 0 : user.totalPoints) || 0, \" pts\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 25\n        }, this), (user === null || user === void 0 ? void 0 : user.passedExamsCount) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-green-600`,\n          children: [user.passedExamsCount, \" passed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.quizzesTaken) !== undefined && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-blue-600`,\n          children: [user.quizzesTaken, \" quizzes\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.averageScore) && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `${config.subtext} text-gray-600`,\n          children: [user.averageScore, \"% avg\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 29\n        }, this), (user === null || user === void 0 ? void 0 : user.currentStreak) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-1\",\n          children: [/*#__PURE__*/_jsxDEV(TbFlame, {\n            className: \"w-3 h-3 text-orange-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `${config.subtext} text-orange-600 font-medium`,\n            children: user.currentStreak\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 21\n      }, this), (user === null || user === void 0 ? void 0 : user.xpToNextLevel) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(XPProgressBar, {\n          currentXP: user.totalXP || 0,\n          totalXP: (user.totalXP || 0) + (user.xpToNextLevel || 0),\n          currentLevel: user.currentLevel || 1,\n          xpToNextLevel: user.xpToNextLevel || 0,\n          size: \"small\",\n          showLevel: false,\n          showXPNumbers: false,\n          showAnimation: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 21\n      }, this), (user === null || user === void 0 ? void 0 : user.achievements) && user.achievements.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: /*#__PURE__*/_jsxDEV(AchievementList, {\n          achievements: user.achievements,\n          maxDisplay: 5,\n          size: \"small\",\n          layout: \"horizontal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-right flex-shrink-0 space-y-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`,\n          children: (user.rankingScore || user.score || user.totalXP || user.totalPoints || 0).toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${config.subtext} text-gray-500`,\n          children: user.rankingScore ? 'ranking pts' : user.totalXP ? 'XP' : 'points'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 21\n        }, this), user.breakdown && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-400 mt-1\",\n          children: [\"XP: \", (user.totalXP || 0).toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `\n                    inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-semibold flex-shrink-0\n                    ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                    backdrop-blur-sm\n                `,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs\",\n          children: styling.badgeIcon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: styling.statusText\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 278,\n    columnNumber: 9\n  }, this);\n};\n_c = UserRankingCard;\nexport default UserRankingCard;\nvar _c;\n$RefreshReg$(_c, \"UserRankingCard\");", "map": {"version": 3, "names": ["React", "motion", "TbTrophy", "TbMedal", "TbCrown", "TbStar", "TbFlame", "TbBolt", "AchievementList", "XPProgressBar", "LevelBadge", "EnhancedAchievementBadge", "jsxDEV", "_jsxDEV", "UserRankingCard", "user", "rank", "classRank", "isCurrentUser", "layout", "size", "showStats", "className", "sizeConfig", "small", "avatar", "text", "subtext", "padding", "spacing", "medium", "large", "config", "getSubscriptionStyling", "subscriptionStatus", "normalizedSubscriptionStatus", "normalizedStatus", "toLowerCase", "avatarClass", "badge", "glow", "statusText", "badgeIcon", "borderClass", "bgClass", "cardBg", "textColor", "borderColor", "styling", "getRankDisplay", "icon", "color", "bg", "rankDisplay", "RankIcon", "cardVariants", "hidden", "opacity", "y", "visible", "transition", "duration", "avatar<PERSON><PERSON><PERSON>", "hover", "scale", "div", "variants", "initial", "animate", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "whileHover", "profilePicture", "profileImage", "src", "alt", "name", "style", "objectFit", "char<PERSON>t", "toUpperCase", "level", "currentLevel", "showTitle", "animated", "totalXP", "xpToNextLevel", "totalPoints", "averageScore", "currentStreak", "achievements", "length", "slice", "map", "achievement", "index", "showTooltip", "showXP", "id", "type", "passedExamsCount", "undefined", "quizzesTaken", "currentXP", "showLevel", "showXPNumbers", "showAnimation", "maxDisplay", "rankingScore", "score", "toLocaleString", "breakdown", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/UserRankingCard.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Tb<PERSON>rophy, TbMedal, TbCrown, TbStar, TbFlame, TbBolt } from 'react-icons/tb';\nimport { AchievementList } from './AchievementBadge';\nimport XPProgressBar from './XPProgressBar';\nimport LevelBadge from './LevelBadge';\nimport EnhancedAchievementBadge from './EnhancedAchievementBadge';\nimport './UserRankingCard.css';\n\nconst UserRankingCard = ({\n    user,\n    rank,\n    classRank,\n    isCurrentUser = false,\n    layout = 'horizontal', // 'horizontal' or 'vertical'\n    size = 'medium', // 'small', 'medium', 'large'\n    showStats = true,\n    className = ''\n}) => {\n    // Size configurations - Optimized profile circle sizes for better visibility\n    const sizeConfig = {\n        small: {\n            avatar: 'w-12 h-12',\n            text: 'text-sm',\n            subtext: 'text-xs',\n            padding: 'p-3',\n            spacing: 'space-x-3'\n        },\n        medium: {\n            avatar: 'w-14 h-14',\n            text: 'text-base',\n            subtext: 'text-sm',\n            padding: 'p-4',\n            spacing: 'space-x-4'\n        },\n        large: {\n            avatar: 'w-16 h-16',\n            text: 'text-lg',\n            subtext: 'text-base',\n            padding: 'p-5',\n            spacing: 'space-x-5'\n        }\n    };\n\n    const config = sizeConfig[size];\n\n    // Get subscription status styling with improved status detection\n    const getSubscriptionStyling = () => {\n        const subscriptionStatus = user?.subscriptionStatus || user?.normalizedSubscriptionStatus || 'free';\n\n        // Normalize status for better handling\n        const normalizedStatus = subscriptionStatus.toLowerCase();\n\n        if (normalizedStatus === 'active' || normalizedStatus === 'premium') {\n            return {\n                avatarClass: 'ring-4 ring-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 ring-offset-2 ring-offset-white',\n                badge: 'status-premium',\n                glow: 'shadow-xl shadow-yellow-500/30 hover:shadow-yellow-500/40',\n                statusText: 'Premium',\n                badgeIcon: '👑',\n                borderClass: 'ring-2 ring-yellow-400',\n                bgClass: 'bg-gradient-to-r from-yellow-500 via-amber-500 to-orange-500 text-white shadow-lg border border-yellow-400/50',\n                cardBg: 'bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-50',\n                textColor: 'text-yellow-700',\n                borderColor: 'border-yellow-200'\n            };\n        } else if (normalizedStatus === 'free') {\n            return {\n                avatarClass: 'ring-2 ring-blue-300 ring-offset-2 ring-offset-white',\n                badge: 'status-free',\n                glow: 'shadow-md shadow-blue-500/20 hover:shadow-blue-500/30',\n                statusText: 'Free',\n                badgeIcon: '🆓',\n                borderClass: 'ring-2 ring-blue-400',\n                bgClass: 'bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white shadow-md border border-blue-400/50',\n                cardBg: 'bg-gradient-to-br from-blue-50 via-indigo-50 to-blue-50',\n                textColor: 'text-blue-700',\n                borderColor: 'border-blue-200'\n            };\n        } else {\n            return {\n                avatarClass: 'ring-3 ring-red-400 ring-offset-2 ring-offset-white opacity-75',\n                badge: 'status-expired',\n                glow: 'shadow-lg shadow-red-500/25 hover:shadow-red-500/35',\n                statusText: 'Expired',\n                badgeIcon: '⏰',\n                borderClass: 'ring-2 ring-red-400',\n                bgClass: 'bg-gradient-to-r from-red-500 via-red-600 to-red-700 text-white shadow-lg border border-red-400/50',\n                cardBg: 'bg-gradient-to-br from-red-50 via-pink-50 to-red-50',\n                textColor: 'text-red-700',\n                borderColor: 'border-red-200'\n            };\n        }\n    };\n\n    const styling = getSubscriptionStyling();\n\n    // Get rank icon and color\n    const getRankDisplay = () => {\n        if (rank === 1) {\n            return { icon: TbCrown, color: 'text-yellow-500', bg: 'bg-yellow-50' };\n        } else if (rank === 2) {\n            return { icon: TbMedal, color: 'text-gray-400', bg: 'bg-gray-50' };\n        } else if (rank === 3) {\n            return { icon: TbTrophy, color: 'text-amber-600', bg: 'bg-amber-50' };\n        } else if (rank <= 10) {\n            return { icon: TbStar, color: 'text-blue-500', bg: 'bg-blue-50' };\n        } else {\n            return { icon: null, color: 'text-gray-500', bg: 'bg-gray-50' };\n        }\n    };\n\n    const rankDisplay = getRankDisplay();\n    const RankIcon = rankDisplay.icon;\n\n    // Simplified animation variants\n    const cardVariants = {\n        hidden: { opacity: 0, y: 20 },\n        visible: {\n            opacity: 1,\n            y: 0,\n            transition: { duration: 0.3 }\n        }\n    };\n\n    const avatarVariants = {\n        hover: {\n            scale: 1.05,\n            transition: { duration: 0.2 }\n        }\n    };\n\n    // Avatar wrapper with subscription styling (removed unused component)\n\n    if (layout === 'vertical') {\n        return (\n            <motion.div\n                variants={cardVariants}\n                initial=\"hidden\"\n                animate=\"visible\"\n                className={`\n                    ranking-card flex flex-col items-center text-center ${config.padding}\n                    ${styling.cardBg || 'bg-white'} rounded-xl border ${styling.borderColor || 'border-gray-200'}\n                    ${styling.glow} transition-all duration-300 hover:scale-105\n                    ${isCurrentUser ? 'current-user-card ring-2 ring-blue-500 ring-offset-2' : ''}\n                    ${className}\n                `}\n            >\n                {/* Rank Badges */}\n                <div className=\"flex items-center space-x-2 mb-3\">\n                    {/* Overall Rank */}\n                    <div className={`\n                        rank-badge flex items-center justify-center w-8 h-8 rounded-full\n                        ${rankDisplay.bg} ${rankDisplay.color}\n                    `}>\n                        {RankIcon ? (\n                            <RankIcon className=\"w-4 h-4\" />\n                        ) : (\n                            <span className=\"text-xs font-bold\">#{rank}</span>\n                        )}\n                    </div>\n\n                    {/* Class Rank */}\n                    {classRank && (\n                        <div className=\"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-700\">\n                            <span className=\"text-xs font-bold\">C{classRank}</span>\n                        </div>\n                    )}\n                </div>\n\n                {/* Avatar - Instagram Style Small Circle */}\n                <motion.div variants={avatarVariants} whileHover=\"hover\" className=\"mb-3\">\n                    <div className={`\n                        ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                        ${styling.avatarClass} ${styling.borderClass}\n                    `}>\n                        {user?.profilePicture || user?.profileImage ? (\n                            <img\n                                src={user.profilePicture || user.profileImage}\n                                alt={user?.name || 'User'}\n                                className=\"w-full h-full object-cover object-center\"\n                                style={{ objectFit: 'cover' }}\n                            />\n                        ) : (\n                            <div className=\"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\">\n                                <span className=\"text-xs font-bold text-white\">\n                                    {(user?.name || 'U').charAt(0).toUpperCase()}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                </motion.div>\n\n                {/* User Info */}\n                <div className=\"space-y-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate max-w-24`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n\n                    {/* XP and Level Info */}\n                    <div className=\"flex items-center space-x-2\">\n                        <LevelBadge\n                            level={user?.currentLevel || 1}\n                            size=\"small\"\n                            showTitle={false}\n                            animated={true}\n                        />\n                        <div className=\"flex flex-col\">\n                            <p className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </p>\n                            {user?.xpToNextLevel > 0 && (\n                                <p className={`text-xs text-gray-400`}>\n                                    {user.xpToNextLevel} to next\n                                </p>\n                            )}\n                        </div>\n                    </div>\n\n                    {/* Legacy Points (smaller) */}\n                    <p className={`text-xs text-gray-400`}>\n                        {user?.totalPoints || 0} pts (legacy)\n                    </p>\n\n                    {/* Enhanced Stats */}\n                    {user?.averageScore && (\n                        <p className={`${config.subtext} text-gray-500`}>\n                            Avg: {user.averageScore}%\n                        </p>\n                    )}\n\n                    {user?.currentStreak > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                            <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                {user.currentStreak}\n                            </span>\n                        </div>\n                    )}\n\n                    {/* Enhanced Achievements */}\n                    {user?.achievements && user.achievements.length > 0 && (\n                        <div className=\"flex items-center space-x-1\">\n                            {user.achievements.slice(0, 3).map((achievement, index) => (\n                                <EnhancedAchievementBadge\n                                    key={achievement.id || achievement.type || index}\n                                    achievement={achievement}\n                                    size=\"small\"\n                                    showTooltip={true}\n                                    animated={true}\n                                    showXP={false}\n                                />\n                            ))}\n                            {user.achievements.length > 3 && (\n                                <span className=\"text-xs text-gray-500 ml-1\">\n                                    +{user.achievements.length - 3}\n                                </span>\n                            )}\n                        </div>\n                    )}\n\n                    {/* Modern Subscription Badge */}\n                    <div className={`\n                        inline-flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-semibold\n                        ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                        backdrop-blur-sm\n                    `}>\n                        <span className=\"text-sm\">{styling.badgeIcon}</span>\n                        <span>{styling.statusText}</span>\n                    </div>\n                </div>\n            </motion.div>\n        );\n    }\n\n    // Horizontal layout (default)\n    return (\n        <motion.div\n            variants={cardVariants}\n            initial=\"hidden\"\n            animate=\"visible\"\n            whileHover=\"hover\"\n            className={`\n                ranking-card flex items-center ${config.spacing} ${config.padding}\n                bg-white rounded-xl border border-gray-200\n                ${isCurrentUser ? 'current-user-card' : ''}\n                ${className}\n            `}\n        >\n            {/* Rank Badges */}\n            <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                {/* Overall Rank */}\n                <div className={`\n                    rank-badge flex items-center justify-center w-10 h-10 rounded-full\n                    ${rankDisplay.bg} ${rankDisplay.color}\n                `}>\n                    {RankIcon ? (\n                        <RankIcon className=\"w-5 h-5\" />\n                    ) : (\n                        <span className=\"text-sm font-bold\">#{rank}</span>\n                    )}\n                </div>\n\n                {/* Class Rank */}\n                {classRank && (\n                    <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-700\">\n                        <span className=\"text-sm font-bold\">C{classRank}</span>\n                    </div>\n                )}\n            </div>\n\n            {/* Avatar - Instagram Style Small Circle */}\n            <motion.div variants={avatarVariants} whileHover=\"hover\" className=\"flex-shrink-0\">\n                <div className={`\n                    ${config.avatar} rounded-full overflow-hidden border-2 border-white shadow-md\n                    ${styling.avatarClass} ${styling.borderClass}\n                `}>\n                    {user?.profilePicture || user?.profileImage ? (\n                        <img\n                            src={user.profilePicture || user.profileImage}\n                            alt={user?.name || 'User'}\n                            className=\"w-full h-full object-cover object-center\"\n                            style={{ objectFit: 'cover' }}\n                        />\n                    ) : (\n                        <div className=\"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center\">\n                            <span className=\"text-xs font-bold text-white\">\n                                {(user?.name || 'U').charAt(0).toUpperCase()}\n                            </span>\n                        </div>\n                    )}\n                </div>\n            </motion.div>\n\n            {/* User Info */}\n            <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center space-x-2 mb-1\">\n                    <h3 className={`font-semibold ${config.text} text-gray-900 truncate`}>\n                        {user?.name || 'Unknown User'}\n                    </h3>\n                    <LevelBadge\n                        level={user?.currentLevel || 1}\n                        size=\"small\"\n                        showTitle={false}\n                        animated={true}\n                    />\n                    <span className={`\n                        px-2 py-1 rounded-full text-xs font-medium flex-shrink-0\n                        ${styling.bgClass}\n                    `}>\n                        {styling.statusText}\n                    </span>\n                </div>\n                \n                {showStats && (\n                    <div className=\"flex items-center space-x-4\">\n                        {/* XP Display */}\n                        <div className=\"flex items-center space-x-1\">\n                            <TbBolt className=\"w-3 h-3 text-blue-500\" />\n                            <span className={`${config.subtext} text-blue-600 font-medium`}>\n                                {user?.totalXP || 0} XP\n                            </span>\n                        </div>\n\n                        {/* Legacy Points (smaller) */}\n                        <span className={`text-xs text-gray-400`}>\n                            {user?.totalPoints || 0} pts\n                        </span>\n\n                        {user?.passedExamsCount !== undefined && (\n                            <span className={`${config.subtext} text-green-600`}>\n                                {user.passedExamsCount} passed\n                            </span>\n                        )}\n                        {user?.quizzesTaken !== undefined && (\n                            <span className={`${config.subtext} text-blue-600`}>\n                                {user.quizzesTaken} quizzes\n                            </span>\n                        )}\n                        {user?.averageScore && (\n                            <span className={`${config.subtext} text-gray-600`}>\n                                {user.averageScore}% avg\n                            </span>\n                        )}\n                        {user?.currentStreak > 0 && (\n                            <div className=\"flex items-center space-x-1\">\n                                <TbFlame className=\"w-3 h-3 text-orange-500\" />\n                                <span className={`${config.subtext} text-orange-600 font-medium`}>\n                                    {user.currentStreak}\n                                </span>\n                            </div>\n                        )}\n                    </div>\n                )}\n\n                {/* XP Progress Bar */}\n                {user?.xpToNextLevel > 0 && (\n                    <div className=\"mt-2\">\n                        <XPProgressBar\n                            currentXP={user.totalXP || 0}\n                            totalXP={(user.totalXP || 0) + (user.xpToNextLevel || 0)}\n                            currentLevel={user.currentLevel || 1}\n                            xpToNextLevel={user.xpToNextLevel || 0}\n                            size=\"small\"\n                            showLevel={false}\n                            showXPNumbers={false}\n                            showAnimation={false}\n                        />\n                    </div>\n                )}\n\n                {/* Achievements for horizontal layout */}\n                {user?.achievements && user.achievements.length > 0 && (\n                    <div className=\"mt-2\">\n                        <AchievementList\n                            achievements={user.achievements}\n                            maxDisplay={5}\n                            size=\"small\"\n                            layout=\"horizontal\"\n                        />\n                    </div>\n                )}\n            </div>\n\n            {/* Score and Subscription Badge */}\n            <div className=\"text-right flex-shrink-0 space-y-2\">\n                <div>\n                    <div className={`font-bold ${config.text} ${isCurrentUser ? 'text-blue-600' : 'text-gray-900'}`}>\n                        {(user.rankingScore || user.score || user.totalXP || user.totalPoints || 0).toLocaleString()}\n                    </div>\n                    <div className={`${config.subtext} text-gray-500`}>\n                        {user.rankingScore ? 'ranking pts' : user.totalXP ? 'XP' : 'points'}\n                    </div>\n                    {user.breakdown && (\n                        <div className=\"text-xs text-gray-400 mt-1\">\n                            XP: {(user.totalXP || 0).toLocaleString()}\n                        </div>\n                    )}\n                </div>\n\n                {/* Modern Subscription Badge for Horizontal Layout */}\n                <div className={`\n                    inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-semibold flex-shrink-0\n                    ${styling.bgClass} transform hover:scale-105 transition-all duration-200\n                    backdrop-blur-sm\n                `}>\n                    <span className=\"text-xs\">{styling.badgeIcon}</span>\n                    <span>{styling.statusText}</span>\n                </div>\n            </div>\n        </motion.div>\n    );\n};\n\nexport default UserRankingCard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACpF,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,eAAe,GAAGA,CAAC;EACrBC,IAAI;EACJC,IAAI;EACJC,SAAS;EACTC,aAAa,GAAG,KAAK;EACrBC,MAAM,GAAG,YAAY;EAAE;EACvBC,IAAI,GAAG,QAAQ;EAAE;EACjBC,SAAS,GAAG,IAAI;EAChBC,SAAS,GAAG;AAChB,CAAC,KAAK;EACF;EACA,MAAMC,UAAU,GAAG;IACfC,KAAK,EAAE;MACHC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDC,MAAM,EAAE;MACJL,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb,CAAC;IACDE,KAAK,EAAE;MACHN,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,WAAW;MACpBC,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACb;EACJ,CAAC;EAED,MAAMG,MAAM,GAAGT,UAAU,CAACH,IAAI,CAAC;;EAE/B;EACA,MAAMa,sBAAsB,GAAGA,CAAA,KAAM;IACjC,MAAMC,kBAAkB,GAAG,CAAAnB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,kBAAkB,MAAInB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEoB,4BAA4B,KAAI,MAAM;;IAEnG;IACA,MAAMC,gBAAgB,GAAGF,kBAAkB,CAACG,WAAW,CAAC,CAAC;IAEzD,IAAID,gBAAgB,KAAK,QAAQ,IAAIA,gBAAgB,KAAK,SAAS,EAAE;MACjE,OAAO;QACHE,WAAW,EAAE,wGAAwG;QACrHC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,2DAA2D;QACjEC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,wBAAwB;QACrCC,OAAO,EAAE,+GAA+G;QACxHC,MAAM,EAAE,4DAA4D;QACpEC,SAAS,EAAE,iBAAiB;QAC5BC,WAAW,EAAE;MACjB,CAAC;IACL,CAAC,MAAM,IAAIX,gBAAgB,KAAK,MAAM,EAAE;MACpC,OAAO;QACHE,WAAW,EAAE,sDAAsD;QACnEC,KAAK,EAAE,aAAa;QACpBC,IAAI,EAAE,uDAAuD;QAC7DC,UAAU,EAAE,MAAM;QAClBC,SAAS,EAAE,IAAI;QACfC,WAAW,EAAE,sBAAsB;QACnCC,OAAO,EAAE,0GAA0G;QACnHC,MAAM,EAAE,yDAAyD;QACjEC,SAAS,EAAE,eAAe;QAC1BC,WAAW,EAAE;MACjB,CAAC;IACL,CAAC,MAAM;MACH,OAAO;QACHT,WAAW,EAAE,gEAAgE;QAC7EC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE,qDAAqD;QAC3DC,UAAU,EAAE,SAAS;QACrBC,SAAS,EAAE,GAAG;QACdC,WAAW,EAAE,qBAAqB;QAClCC,OAAO,EAAE,oGAAoG;QAC7GC,MAAM,EAAE,qDAAqD;QAC7DC,SAAS,EAAE,cAAc;QACzBC,WAAW,EAAE;MACjB,CAAC;IACL;EACJ,CAAC;EAED,MAAMC,OAAO,GAAGf,sBAAsB,CAAC,CAAC;;EAExC;EACA,MAAMgB,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAIjC,IAAI,KAAK,CAAC,EAAE;MACZ,OAAO;QAAEkC,IAAI,EAAE9C,OAAO;QAAE+C,KAAK,EAAE,iBAAiB;QAAEC,EAAE,EAAE;MAAe,CAAC;IAC1E,CAAC,MAAM,IAAIpC,IAAI,KAAK,CAAC,EAAE;MACnB,OAAO;QAAEkC,IAAI,EAAE/C,OAAO;QAAEgD,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACtE,CAAC,MAAM,IAAIpC,IAAI,KAAK,CAAC,EAAE;MACnB,OAAO;QAAEkC,IAAI,EAAEhD,QAAQ;QAAEiD,KAAK,EAAE,gBAAgB;QAAEC,EAAE,EAAE;MAAc,CAAC;IACzE,CAAC,MAAM,IAAIpC,IAAI,IAAI,EAAE,EAAE;MACnB,OAAO;QAAEkC,IAAI,EAAE7C,MAAM;QAAE8C,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACrE,CAAC,MAAM;MACH,OAAO;QAAEF,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,eAAe;QAAEC,EAAE,EAAE;MAAa,CAAC;IACnE;EACJ,CAAC;EAED,MAAMC,WAAW,GAAGJ,cAAc,CAAC,CAAC;EACpC,MAAMK,QAAQ,GAAGD,WAAW,CAACH,IAAI;;EAEjC;EACA,MAAMK,YAAY,GAAG;IACjBC,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAG,CAAC;IAC7BC,OAAO,EAAE;MACLF,OAAO,EAAE,CAAC;MACVC,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAG;IACnBC,KAAK,EAAE;MACHC,KAAK,EAAE,IAAI;MACXJ,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAChC;EACJ,CAAC;;EAED;;EAEA,IAAI1C,MAAM,KAAK,UAAU,EAAE;IACvB,oBACIN,OAAA,CAACZ,MAAM,CAACgE,GAAG;MACPC,QAAQ,EAAEX,YAAa;MACvBY,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,SAAS;MACjB9C,SAAS,EAAG;AAC5B,0EAA0EU,MAAM,CAACJ,OAAQ;AACzF,sBAAsBoB,OAAO,CAACH,MAAM,IAAI,UAAW,sBAAqBG,OAAO,CAACD,WAAW,IAAI,iBAAkB;AACjH,sBAAsBC,OAAO,CAACR,IAAK;AACnC,sBAAsBtB,aAAa,GAAG,sDAAsD,GAAG,EAAG;AAClG,sBAAsBI,SAAU;AAChC,iBAAkB;MAAA+C,QAAA,gBAGFxD,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAA+C,QAAA,gBAE7CxD,OAAA;UAAKS,SAAS,EAAG;AACrC;AACA,0BAA0B+B,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AAC9D,qBAAsB;UAAAkB,QAAA,EACGf,QAAQ,gBACLzC,OAAA,CAACyC,QAAQ;YAAChC,SAAS,EAAC;UAAS;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAEhC5D,OAAA;YAAMS,SAAS,EAAC,mBAAmB;YAAA+C,QAAA,GAAC,GAAC,EAACrD,IAAI;UAAA;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QACpD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAGLxD,SAAS,iBACNJ,OAAA;UAAKS,SAAS,EAAC,iFAAiF;UAAA+C,QAAA,eAC5FxD,OAAA;YAAMS,SAAS,EAAC,mBAAmB;YAAA+C,QAAA,GAAC,GAAC,EAACpD,SAAS;UAAA;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGN5D,OAAA,CAACZ,MAAM,CAACgE,GAAG;QAACC,QAAQ,EAAEJ,cAAe;QAACY,UAAU,EAAC,OAAO;QAACpD,SAAS,EAAC,MAAM;QAAA+C,QAAA,eACrExD,OAAA;UAAKS,SAAS,EAAG;AACrC,0BAA0BU,MAAM,CAACP,MAAO;AACxC,0BAA0BuB,OAAO,CAACV,WAAY,IAAGU,OAAO,CAACL,WAAY;AACrE,qBAAsB;UAAA0B,QAAA,EACGtD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4D,cAAc,IAAI5D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6D,YAAY,gBACvC/D,OAAA;YACIgE,GAAG,EAAE9D,IAAI,CAAC4D,cAAc,IAAI5D,IAAI,CAAC6D,YAAa;YAC9CE,GAAG,EAAE,CAAA/D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,KAAI,MAAO;YAC1BzD,SAAS,EAAC,0CAA0C;YACpD0D,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAQ;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,gBAEF5D,OAAA;YAAKS,SAAS,EAAC,8FAA8F;YAAA+C,QAAA,eACzGxD,OAAA;cAAMS,SAAS,EAAC,8BAA8B;cAAA+C,QAAA,EACzC,CAAC,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,KAAI,GAAG,EAAEG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGb5D,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAA+C,QAAA,gBACtBxD,OAAA;UAAIS,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,kCAAkC;UAAA2C,QAAA,EACzE,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,KAAI;QAAc;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAGL5D,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAA+C,QAAA,gBACxCxD,OAAA,CAACH,UAAU;YACP0E,KAAK,EAAE,CAAArE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,YAAY,KAAI,CAAE;YAC/BjE,IAAI,EAAC,OAAO;YACZkE,SAAS,EAAE,KAAM;YACjBC,QAAQ,EAAE;UAAK;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC,eACF5D,OAAA;YAAKS,SAAS,EAAC,eAAe;YAAA+C,QAAA,gBAC1BxD,OAAA;cAAGS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,4BAA4B;cAAA0C,QAAA,GACvD,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,OAAO,KAAI,CAAC,EAAC,KACxB;YAAA;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,EACH,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,aAAa,IAAG,CAAC,iBACpB5E,OAAA;cAAGS,SAAS,EAAG,uBAAuB;cAAA+C,QAAA,GACjCtD,IAAI,CAAC0E,aAAa,EAAC,UACxB;YAAA;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGN5D,OAAA;UAAGS,SAAS,EAAG,uBAAuB;UAAA+C,QAAA,GACjC,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,WAAW,KAAI,CAAC,EAAC,eAC5B;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAGH,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,YAAY,kBACf9E,OAAA;UAAGS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA0C,QAAA,GAAC,OACxC,EAACtD,IAAI,CAAC4E,YAAY,EAAC,GAC5B;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CACN,EAEA,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E,aAAa,IAAG,CAAC,iBACpB/E,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAA+C,QAAA,gBACxCxD,OAAA,CAACP,OAAO;YAACgB,SAAS,EAAC;UAAyB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C5D,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAA0C,QAAA,EAC5DtD,IAAI,CAAC6E;UAAa;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR,EAGA,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8E,YAAY,KAAI9E,IAAI,CAAC8E,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC/CjF,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAA+C,QAAA,GACvCtD,IAAI,CAAC8E,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAClDrF,OAAA,CAACF,wBAAwB;YAErBsF,WAAW,EAAEA,WAAY;YACzB7E,IAAI,EAAC,OAAO;YACZ+E,WAAW,EAAE,IAAK;YAClBZ,QAAQ,EAAE,IAAK;YACfa,MAAM,EAAE;UAAM,GALTH,WAAW,CAACI,EAAE,IAAIJ,WAAW,CAACK,IAAI,IAAIJ,KAAK;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMnD,CACJ,CAAC,EACD1D,IAAI,CAAC8E,YAAY,CAACC,MAAM,GAAG,CAAC,iBACzBjF,OAAA;YAAMS,SAAS,EAAC,4BAA4B;YAAA+C,QAAA,GAAC,GACxC,EAACtD,IAAI,CAAC8E,YAAY,CAACC,MAAM,GAAG,CAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACR,eAGD5D,OAAA;UAAKS,SAAS,EAAG;AACrC;AACA,0BAA0B0B,OAAO,CAACJ,OAAQ;AAC1C;AACA,qBAAsB;UAAAyB,QAAA,gBACExD,OAAA;YAAMS,SAAS,EAAC,SAAS;YAAA+C,QAAA,EAAErB,OAAO,CAACN;UAAS;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACpD5D,OAAA;YAAAwD,QAAA,EAAOrB,OAAO,CAACP;UAAU;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAErB;;EAEA;EACA,oBACI5D,OAAA,CAACZ,MAAM,CAACgE,GAAG;IACPC,QAAQ,EAAEX,YAAa;IACvBY,OAAO,EAAC,QAAQ;IAChBC,OAAO,EAAC,SAAS;IACjBM,UAAU,EAAC,OAAO;IAClBpD,SAAS,EAAG;AACxB,iDAAiDU,MAAM,CAACH,OAAQ,IAAGG,MAAM,CAACJ,OAAQ;AAClF;AACA,kBAAkBV,aAAa,GAAG,mBAAmB,GAAG,EAAG;AAC3D,kBAAkBI,SAAU;AAC5B,aAAc;IAAA+C,QAAA,gBAGFxD,OAAA;MAAKS,SAAS,EAAC,2CAA2C;MAAA+C,QAAA,gBAEtDxD,OAAA;QAAKS,SAAS,EAAG;AACjC;AACA,sBAAsB+B,WAAW,CAACD,EAAG,IAAGC,WAAW,CAACF,KAAM;AAC1D,iBAAkB;QAAAkB,QAAA,EACGf,QAAQ,gBACLzC,OAAA,CAACyC,QAAQ;UAAChC,SAAS,EAAC;QAAS;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEhC5D,OAAA;UAAMS,SAAS,EAAC,mBAAmB;UAAA+C,QAAA,GAAC,GAAC,EAACrD,IAAI;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACpD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGLxD,SAAS,iBACNJ,OAAA;QAAKS,SAAS,EAAC,mFAAmF;QAAA+C,QAAA,eAC9FxD,OAAA;UAAMS,SAAS,EAAC,mBAAmB;UAAA+C,QAAA,GAAC,GAAC,EAACpD,SAAS;QAAA;UAAAqD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGN5D,OAAA,CAACZ,MAAM,CAACgE,GAAG;MAACC,QAAQ,EAAEJ,cAAe;MAACY,UAAU,EAAC,OAAO;MAACpD,SAAS,EAAC,eAAe;MAAA+C,QAAA,eAC9ExD,OAAA;QAAKS,SAAS,EAAG;AACjC,sBAAsBU,MAAM,CAACP,MAAO;AACpC,sBAAsBuB,OAAO,CAACV,WAAY,IAAGU,OAAO,CAACL,WAAY;AACjE,iBAAkB;QAAA0B,QAAA,EACGtD,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE4D,cAAc,IAAI5D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAE6D,YAAY,gBACvC/D,OAAA;UACIgE,GAAG,EAAE9D,IAAI,CAAC4D,cAAc,IAAI5D,IAAI,CAAC6D,YAAa;UAC9CE,GAAG,EAAE,CAAA/D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,KAAI,MAAO;UAC1BzD,SAAS,EAAC,0CAA0C;UACpD0D,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAQ;QAAE;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,gBAEF5D,OAAA;UAAKS,SAAS,EAAC,8FAA8F;UAAA+C,QAAA,eACzGxD,OAAA;YAAMS,SAAS,EAAC,8BAA8B;YAAA+C,QAAA,EACzC,CAAC,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,KAAI,GAAG,EAAEG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGb5D,OAAA;MAAKS,SAAS,EAAC,gBAAgB;MAAA+C,QAAA,gBAC3BxD,OAAA;QAAKS,SAAS,EAAC,kCAAkC;QAAA+C,QAAA,gBAC7CxD,OAAA;UAAIS,SAAS,EAAG,iBAAgBU,MAAM,CAACN,IAAK,yBAAyB;UAAA2C,QAAA,EAChE,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgE,IAAI,KAAI;QAAc;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eACL5D,OAAA,CAACH,UAAU;UACP0E,KAAK,EAAE,CAAArE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsE,YAAY,KAAI,CAAE;UAC/BjE,IAAI,EAAC,OAAO;UACZkE,SAAS,EAAE,KAAM;UACjBC,QAAQ,EAAE;QAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACF5D,OAAA;UAAMS,SAAS,EAAG;AACtC;AACA,0BAA0B0B,OAAO,CAACJ,OAAQ;AAC1C,qBAAsB;UAAAyB,QAAA,EACGrB,OAAO,CAACP;QAAU;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELpD,SAAS,iBACNR,OAAA;QAAKS,SAAS,EAAC,6BAA6B;QAAA+C,QAAA,gBAExCxD,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAA+C,QAAA,gBACxCxD,OAAA,CAACN,MAAM;YAACe,SAAS,EAAC;UAAuB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5C5D,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,4BAA4B;YAAA0C,QAAA,GAC1D,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,OAAO,KAAI,CAAC,EAAC,KACxB;UAAA;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5D,OAAA;UAAMS,SAAS,EAAG,uBAAuB;UAAA+C,QAAA,GACpC,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,WAAW,KAAI,CAAC,EAAC,MAC5B;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAEN,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwF,gBAAgB,MAAKC,SAAS,iBACjC3F,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,iBAAiB;UAAA0C,QAAA,GAC/CtD,IAAI,CAACwF,gBAAgB,EAAC,SAC3B;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0F,YAAY,MAAKD,SAAS,iBAC7B3F,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA0C,QAAA,GAC9CtD,IAAI,CAAC0F,YAAY,EAAC,UACvB;QAAA;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4E,YAAY,kBACf9E,OAAA;UAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA0C,QAAA,GAC9CtD,IAAI,CAAC4E,YAAY,EAAC,OACvB;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACT,EACA,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E,aAAa,IAAG,CAAC,iBACpB/E,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAA+C,QAAA,gBACxCxD,OAAA,CAACP,OAAO;YAACgB,SAAS,EAAC;UAAyB;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/C5D,OAAA;YAAMS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,8BAA8B;YAAA0C,QAAA,EAC5DtD,IAAI,CAAC6E;UAAa;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR,EAGA,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0E,aAAa,IAAG,CAAC,iBACpB5E,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAA+C,QAAA,eACjBxD,OAAA,CAACJ,aAAa;UACViG,SAAS,EAAE3F,IAAI,CAACyE,OAAO,IAAI,CAAE;UAC7BA,OAAO,EAAE,CAACzE,IAAI,CAACyE,OAAO,IAAI,CAAC,KAAKzE,IAAI,CAAC0E,aAAa,IAAI,CAAC,CAAE;UACzDJ,YAAY,EAAEtE,IAAI,CAACsE,YAAY,IAAI,CAAE;UACrCI,aAAa,EAAE1E,IAAI,CAAC0E,aAAa,IAAI,CAAE;UACvCrE,IAAI,EAAC,OAAO;UACZuF,SAAS,EAAE,KAAM;UACjBC,aAAa,EAAE,KAAM;UACrBC,aAAa,EAAE;QAAM;UAAAvC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR,EAGA,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8E,YAAY,KAAI9E,IAAI,CAAC8E,YAAY,CAACC,MAAM,GAAG,CAAC,iBAC/CjF,OAAA;QAAKS,SAAS,EAAC,MAAM;QAAA+C,QAAA,eACjBxD,OAAA,CAACL,eAAe;UACZqF,YAAY,EAAE9E,IAAI,CAAC8E,YAAa;UAChCiB,UAAU,EAAE,CAAE;UACd1F,IAAI,EAAC,OAAO;UACZD,MAAM,EAAC;QAAY;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGN5D,OAAA;MAAKS,SAAS,EAAC,oCAAoC;MAAA+C,QAAA,gBAC/CxD,OAAA;QAAAwD,QAAA,gBACIxD,OAAA;UAAKS,SAAS,EAAG,aAAYU,MAAM,CAACN,IAAK,IAAGR,aAAa,GAAG,eAAe,GAAG,eAAgB,EAAE;UAAAmD,QAAA,EAC3F,CAACtD,IAAI,CAACgG,YAAY,IAAIhG,IAAI,CAACiG,KAAK,IAAIjG,IAAI,CAACyE,OAAO,IAAIzE,IAAI,CAAC2E,WAAW,IAAI,CAAC,EAAEuB,cAAc,CAAC;QAAC;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3F,CAAC,eACN5D,OAAA;UAAKS,SAAS,EAAG,GAAEU,MAAM,CAACL,OAAQ,gBAAgB;UAAA0C,QAAA,EAC7CtD,IAAI,CAACgG,YAAY,GAAG,aAAa,GAAGhG,IAAI,CAACyE,OAAO,GAAG,IAAI,GAAG;QAAQ;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,EACL1D,IAAI,CAACmG,SAAS,iBACXrG,OAAA;UAAKS,SAAS,EAAC,4BAA4B;UAAA+C,QAAA,GAAC,MACpC,EAAC,CAACtD,IAAI,CAACyE,OAAO,IAAI,CAAC,EAAEyB,cAAc,CAAC,CAAC;QAAA;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAGN5D,OAAA;QAAKS,SAAS,EAAG;AACjC;AACA,sBAAsB0B,OAAO,CAACJ,OAAQ;AACtC;AACA,iBAAkB;QAAAyB,QAAA,gBACExD,OAAA;UAAMS,SAAS,EAAC,SAAS;UAAA+C,QAAA,EAAErB,OAAO,CAACN;QAAS;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpD5D,OAAA;UAAAwD,QAAA,EAAOrB,OAAO,CAACP;QAAU;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAErB,CAAC;AAAC0C,EAAA,GA3bIrG,eAAe;AA6brB,eAAeA,eAAe;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}